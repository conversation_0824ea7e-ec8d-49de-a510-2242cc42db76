﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace HeroYulgang.Database.Entities.Account;

public partial class AccountDbContext : DbContext
{
    public AccountDbContext()
    {
    }

    public AccountDbContext(DbContextOptions<AccountDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Doanhthu> <PERSON><PERSON>hthus { get; set; }

    public virtual DbSet<LogTransfer> LogTransfers { get; set; }

    public virtual DbSet<Napthe> Napthes { get; set; }

    public virtual DbSet<TblAccount> TblAccounts { get; set; }

    public virtual DbSet<TblAddcash> TblAddcashes { get; set; }

    public virtual DbSet<TblBaned> TblBaneds { get; set; }

    public virtual DbSet<TblIpCheck> TblIpChecks { get; set; }

    public virtual DbSet<TblMoreRun> TblMoreRuns { get; set; }

    public virtual DbSet<TblOnline> TblOnlines { get; set; }

    public virtual DbSet<TblTrucash> TblTrucashes { get; set; }

    public virtual DbSet<TblUpdatelog> TblUpdatelogs { get; set; }

    public virtual DbSet<Trucash> Trucashes { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Server=**************;Database=heroAccount;User Id=yghero_user;Password=****************************************;TrustServerCertificate=True;");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Doanhthu>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("doanhthu");

            entity.Property(e => e.Sothe).HasColumnName("sothe");
            entity.Property(e => e.Stt)
                .ValueGeneratedOnAdd()
                .HasColumnName("stt");
            entity.Property(e => e.Thoigian).HasColumnName("thoigian");
            entity.Property(e => e.Ti)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("ti");
            entity.Property(e => e.Tongtien).HasColumnName("tongtien");
        });

        modelBuilder.Entity<LogTransfer>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__log_tran__3213E83F25B0606E");

            entity.ToTable("log_transfer");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.ConfirmTime)
                .HasColumnType("datetime")
                .HasColumnName("confirm_time");
            entity.Property(e => e.GameId)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("game_id");
            entity.Property(e => e.ReceiverEmail)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("receiver_email");
            entity.Property(e => e.SenderEmail)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("sender_email");
            entity.Property(e => e.Status)
                .HasDefaultValue(0)
                .HasColumnName("status");
            entity.Property(e => e.TransferTime)
                .HasColumnType("datetime")
                .HasColumnName("transfer_time");
        });

        modelBuilder.Entity<Napthe>(entity =>
        {
            entity.HasKey(e => e.Stt);

            entity.ToTable("napthe");

            entity.Property(e => e.Stt).HasColumnName("stt");
            entity.Property(e => e.AdminCheck).HasColumnName("admin_check");
            entity.Property(e => e.Laygi)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("laygi");
            entity.Property(e => e.Loaithe)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("loaithe");
            entity.Property(e => e.Menhgia)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("menhgia");
            entity.Property(e => e.Pin)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("pin");
            entity.Property(e => e.Serial)
                .HasMaxLength(30)
                .IsUnicode(false)
                .HasColumnName("serial");
            entity.Property(e => e.Taikhoan)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("taikhoan");
            entity.Property(e => e.Thoigian)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("thoigian");
            entity.Property(e => e.Thoigian1).HasColumnName("thoigian_");
            entity.Property(e => e.Tiencu)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("tiencu");
            entity.Property(e => e.Tienduoccong)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("tienduoccong");
        });

        modelBuilder.Entity<TblAccount>(entity =>
        {
            entity.HasKey(e => e.FldId)
                .HasName("PK_TBL_ACCOUT")
                .HasFillFactor(90);

            entity.ToTable("TBL_ACCOUNT");

            entity.Property(e => e.FldId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ID");
            entity.Property(e => e.FldAnswer)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ANSWER");
            entity.Property(e => e.FldBd)
                .HasDefaultValue(0)
                .HasColumnName("FLD_BD");
            entity.Property(e => e.FldCard)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_CARD");
            entity.Property(e => e.FldCardold)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValueSql("((0))")
                .HasColumnName("FLD_CARDOLD");
            entity.Property(e => e.FldCheckip)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_CHECKIP");
            entity.Property(e => e.FldChecklogin)
                .HasDefaultValue(false)
                .HasColumnName("FLD_CHECKLOGIN");
            entity.Property(e => e.FldCoin)
                .HasDefaultValue(0)
                .HasColumnName("FLD_COIN");
            entity.Property(e => e.FldDenbu).HasColumnName("FLD_DENBU");
            entity.Property(e => e.FldDiemThuong)
                .HasDefaultValue(0)
                .HasColumnName("FLD_DiemThuong");
            entity.Property(e => e.FldFq)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_FQ");
            entity.Property(e => e.FldGhiChu)
                .IsUnicode(false)
                .HasColumnName("FLD_GhiChu");
            entity.Property(e => e.FldGiftcode)
                .HasDefaultValue(0)
                .HasColumnName("FLD_GIFTCODE");
            entity.Property(e => e.FldIncome)
                .HasDefaultValue(0)
                .HasColumnName("FLD_INCOME");
            entity.Property(e => e.FldJf)
                .HasDefaultValue(0)
                .HasColumnName("FLD_JF");
            entity.Property(e => e.FldLanip)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_LANIP");
            entity.Property(e => e.FldLastloginip)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValue("127.0.0.1")
                .HasColumnName("FLD_LASTLOGINIP");
            entity.Property(e => e.FldLastlogintime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_LASTLOGINTIME");
            entity.Property(e => e.FldLock)
                .HasDefaultValue(0)
                .HasColumnName("FLD_LOCK");
            entity.Property(e => e.FldMachineid)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_MACHINEID");
            entity.Property(e => e.FldMail)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_Mail");
            entity.Property(e => e.FldMoney)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MONEY");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldOnline)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ONLINE");
            entity.Property(e => e.FldPasskey)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("FLD_PASSKEY");
            entity.Property(e => e.FldPasskeyTimestamp)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasDefaultValueSql("(getdate())")
                .HasColumnName("FLD_PASSKEY_TIMESTAMP");
            entity.Property(e => e.FldPassword)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_PASSWORD");
            entity.Property(e => e.FldPay)
                .HasDefaultValue(0)
                .HasColumnName("FLD_PAY");
            entity.Property(e => e.FldQcvip)
                .HasDefaultValue(0)
                .HasColumnName("FLD_QCVIP");
            entity.Property(e => e.FldQcviptim)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_QCVIPTIM");
            entity.Property(e => e.FldQdcs).HasColumnName("FLD_QDCS");
            entity.Property(e => e.FldQdsj)
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_QDSJ");
            entity.Property(e => e.FldQsonoff).HasColumnName("FLD_QSONOFF");
            entity.Property(e => e.FldQu)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_QU");
            entity.Property(e => e.FldQuayso).HasColumnName("FLD_QUAYSO");
            entity.Property(e => e.FldRefreshKey)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("FLD_REFRESH_KEY");
            entity.Property(e => e.FldRefreshKeyTimestamp)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_REFRESH_KEY_TIMESTAMP");
            entity.Property(e => e.FldRegip)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_REGIP");
            entity.Property(e => e.FldRegtime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_REGTIME");
            entity.Property(e => e.FldRunmore)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_RUNMORE");
            entity.Property(e => e.FldRxpiont)
                .HasDefaultValue(0)
                .HasColumnName("FLD_RXPIONT");
            entity.Property(e => e.FldRxpiontx).HasColumnName("FLD_RXPIONTX");
            entity.Property(e => e.FldSafeword)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValueSql("((12345678))")
                .HasColumnName("FLD_SAFEWORD");
            entity.Property(e => e.FldSex).HasColumnName("FLD_SEX");
            entity.Property(e => e.FldSpreaderLevel).HasColumnName("FLD_SPREADER_LEVEL");
            entity.Property(e => e.FldSpreaderid)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValueSql("((0))")
                .HasColumnName("FLD_SPREADERID");
            entity.Property(e => e.FldSvip).HasColumnName("FLD_SVIP");
            entity.Property(e => e.FldTempPasskey)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("FLD_TEMP_PASSKEY");
            entity.Property(e => e.FldThelucchien)
                .HasDefaultValue(0)
                .HasColumnName("FLD_THELUCCHIEN");
            entity.Property(e => e.FldThelucchien2)
                .HasDefaultValue(0)
                .HasColumnName("FLD_THELUCCHIEN2");
            entity.Property(e => e.FldTotalAmount)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TotalAmount");
            entity.Property(e => e.FldTransferTimes).HasColumnName("FLD_TRANSFER_TIMES");
            entity.Property(e => e.FldUserid)
                .HasDefaultValue(0)
                .HasColumnName("FLD_USERID");
            entity.Property(e => e.FldVip)
                .HasDefaultValue(0)
                .HasColumnName("FLD_VIP");
            entity.Property(e => e.FldViptim)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_VIPTIM");
            entity.Property(e => e.FldWcvip).HasColumnName("FLD_WCVIP");
            entity.Property(e => e.FldWcviptim)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_WCVIPTIM");
            entity.Property(e => e.FldYy)
                .HasMaxLength(50)
                .HasDefaultValue("无")
                .HasColumnName("FLD_YY");
            entity.Property(e => e.FldZjf)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ZJF");
            entity.Property(e => e.FldZt)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ZT");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.Pass2)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<TblAddcash>(entity =>
        {
            entity.ToTable("TBL_ADDCASH");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Account)
                .HasMaxLength(50)
                .HasColumnName("account");
            entity.Property(e => e.GhiChu)
                .HasMaxLength(500)
                .HasDefaultValue("");
        });

        modelBuilder.Entity<TblBaned>(entity =>
        {
            entity.ToTable("TBL_BANED");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.FldBanedip)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_BANEDIP");
        });

        modelBuilder.Entity<TblIpCheck>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_IP_CHECK");

            entity.Property(e => e.FldId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ID");
            entity.Property(e => e.FldIp)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_IP");
            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<TblMoreRun>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_TBL_MORE_ACCOUNT");

            entity.ToTable("TBL_MORE_RUN");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.FldNumber).HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPid)
                .HasMaxLength(50)
                .HasColumnName("FLD_PID");
            entity.Property(e => e.FldTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_TIME");
        });

        modelBuilder.Entity<TblOnline>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TBL_ONLINE_PK");

            entity.ToTable("TBL_ONLINE");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.FldMaxuser).HasColumnName("FLD_MAXUSER");
            entity.Property(e => e.FldNowuser).HasColumnName("FLD_NOWUSER");
            entity.Property(e => e.FldServer)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_SERVER");
            entity.Property(e => e.FldZone).HasColumnName("FLD_ZONE");
        });

        modelBuilder.Entity<TblTrucash>(entity =>
        {
            entity.ToTable("TBL_TRUCASH");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Account)
                .HasMaxLength(50)
                .HasColumnName("account");
            entity.Property(e => e.GhiChu)
                .HasMaxLength(500)
                .HasDefaultValue("");
        });

        modelBuilder.Entity<TblUpdatelog>(entity =>
        {
            entity.ToTable("TBL_UPDATELOG");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Dsc)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("DSC");
            entity.Property(e => e.FldId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ID");
            entity.Property(e => e.Hosname)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasColumnName("HOSNAME");
            entity.Property(e => e.NelPiont).HasColumnName("NEL_PIONT");
            entity.Property(e => e.OldPiont).HasColumnName("OLD_PIONT");
            entity.Property(e => e.Regtime)
                .HasColumnType("smalldatetime")
                .HasColumnName("REGTIME");
            entity.Property(e => e.Time)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("TIME");
        });

        modelBuilder.Entity<Trucash>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TRUCASH");

            entity.Property(e => e.Account)
                .HasMaxLength(50)
                .HasColumnName("account");
            entity.Property(e => e.GhiChu).HasMaxLength(500);
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
