﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.BBG;

public partial class Itmeclss
{
    public int Id { get; set; }

    public int? FldType { get; set; }

    public string FldName { get; set; }

    public int? FldReside { get; set; }

    public int? FldMagic0 { get; set; }

    public int? FldMagic1 { get; set; }

    public int? FldMagic2 { get; set; }

    public int? FldMagic3 { get; set; }

    public int? FldMagic4 { get; set; }

    public int? FldMagic5 { get; set; }

    public int? FldFjNj { get; set; }

    public int? FldDays { get; set; }

    public int? FldFjThucTinh { get; set; }

    public int? FldFjTrungCapPhuHon { get; set; }

    public int? FldFjTienHoa { get; set; }

    public string FldSql { get; set; }

    public int? FldBd { get; set; }
}
