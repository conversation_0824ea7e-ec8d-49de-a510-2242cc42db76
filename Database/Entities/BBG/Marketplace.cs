﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.BBG;

public partial class Marketplace
{
    public int Id { get; set; }

    public string ProductCode { get; set; }

    public string ItemName { get; set; }

    public string SellerId { get; set; }

    public string SellerName { get; set; }

    public int BaseAmount { get; set; }

    public long BasePrice { get; set; }

    public long FldPrice { get; set; }

    public int CurrentAmount { get; set; }

    public int? Filter1 { get; set; }

    public int? Filter2 { get; set; }

    public int? Filter3 { get; set; }

    public int? Filter4 { get; set; }

    public int? Filter5 { get; set; }

    public string Status { get; set; }

    public byte[] Item { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime ExpiredAt { get; set; }
}
