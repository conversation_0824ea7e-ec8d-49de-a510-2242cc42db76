﻿using System;
using System.Collections.Generic;
using HeroYulgang.Core;
using Microsoft.EntityFrameworkCore;

namespace HeroYulgang.Database.Entities.BBG;

public partial class BBGDbContext : DbContext
{
    public BBGDbContext()
    {
    }

    public BBGDbContext(DbContextOptions<BBGDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<CashShopLog> CashShopLogs { get; set; }

    public virtual DbSet<GiftCodeLog> GiftCodeLogs { get; set; }

    public virtual DbSet<Item> Items { get; set; }

    public virtual DbSet<ItemVongQuay> ItemVongQuays { get; set; }

    public virtual DbSet<Itemsell> Itemsells { get; set; }

    public virtual DbSet<ItemsellCopy> ItemsellCopies { get; set; }

    public virtual DbSet<ItemsellCopy1> ItemsellCopy1s { get; set; }

    public virtual DbSet<ItemsellTestAoChoang> ItemsellTestAoChoangs { get; set; }

    public virtual DbSet<Itmeclss> Itmeclsses { get; set; }

    public virtual DbSet<MailCod> MailCods { get; set; }

    public virtual DbSet<Marketplace> Marketplaces { get; set; }

    public virtual DbSet<OrderDetail> OrderDetails { get; set; }

    public virtual DbSet<Shopcategory> Shopcategories { get; set; }

    public virtual DbSet<VongQuayResult> VongQuayResults { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            var configManager = ConfigManager.Instance;
            optionsBuilder.UseSqlServer(configManager.ConnectionStrings.BBGDb);
        }
    }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CashShopLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("CASH_SHOP_LOG_PK");

            entity.ToTable("CASH_SHOP_LOG");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Amount)
                .HasDefaultValue(1)
                .HasColumnName("AMOUNT");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("CREATED_AT");
            entity.Property(e => e.ItemId).HasColumnName("ITEM_ID");
            entity.Property(e => e.Message)
                .HasMaxLength(100)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("MESSAGE");
            entity.Property(e => e.Price).HasColumnName("PRICE");
            entity.Property(e => e.ProductId)
                .HasMaxLength(100)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("PRODUCT_ID");
            entity.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("STATUS");
            entity.Property(e => e.UpdatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime")
                .HasColumnName("UPDATED_AT");
            entity.Property(e => e.Username)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("USERNAME");
        });

        modelBuilder.Entity<GiftCodeLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("GiftCodeLog_pkey");

            entity.ToTable("GiftCodeLog");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Character)
                .IsRequired()
                .HasMaxLength(16)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("CHARACTER");
            entity.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(16)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("CODE");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("CREATED_AT");
            entity.Property(e => e.ExpiredAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("EXPIRED_AT");
            entity.Property(e => e.Reward)
                .IsRequired()
                .HasMaxLength(255)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("REWARD");
            entity.Property(e => e.Status).HasColumnName("STATUS");
        });

        modelBuilder.Entity<Item>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ITEM");

            entity.HasIndex(e => e.Id, "ITEM_ID_key").IsUnique();

            entity.Property(e => e.CategoryId).HasColumnName("CATEGORY_ID");
            entity.Property(e => e.FldDays)
                .HasDefaultValue(0)
                .HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldDesc)
                .HasMaxLength(1000)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("FLD_DESC");
            entity.Property(e => e.FldLock)
                .HasDefaultValue(0)
                .HasColumnName("FLD_LOCK");
            entity.Property(e => e.FldMagic1)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldName)
                .IsRequired()
                .HasMaxLength(255)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNumber)
                .HasDefaultValue(1)
                .HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPhaiChangKhoaLai)
                .HasDefaultValue(0)
                .HasColumnName("FLD_PhaiChangKhoaLai");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPrice).HasColumnName("FLD_PRICE");
            entity.Property(e => e.FldPriceOld).HasColumnName("FLD_PRICE_OLD");
            entity.Property(e => e.FldReturn).HasColumnName("FLD_RETURN");
            entity.Property(e => e.FldSoCapPhuHon)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SoCapPhuHon");
            entity.Property(e => e.FldTienHoa)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TienHoa");
            entity.Property(e => e.FldTrungCapPhuHon)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TrungCapPhuHon");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.Order)
                .HasDefaultValue(0)
                .HasColumnName("ORDER");
            entity.Property(e => e.ProductCode)
                .HasMaxLength(16)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("PRODUCT_CODE");

            entity.HasOne(d => d.Category).WithMany()
                .HasForeignKey(d => d.CategoryId)
                .OnDelete(DeleteBehavior.SetNull)
                .HasConstraintName("ITEM_CATEGORY_ID_fkey");
        });

        modelBuilder.Entity<ItemVongQuay>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ITEM_VONG_QUAY");

            entity.Property(e => e.FldDays)
                .HasDefaultValue(0)
                .HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldDesc)
                .HasMaxLength(4000)
                .HasColumnName("FLD_DESC");
            entity.Property(e => e.FldMagic1)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldName)
                .HasMaxLength(255)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNumber)
                .HasDefaultValue(1)
                .HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPercentage)
                .HasDefaultValue(0)
                .HasColumnName("FLD_percentage");
            entity.Property(e => e.FldPhaiChangKhoaLai)
                .HasDefaultValue(0)
                .HasColumnName("FLD_PhaiChangKhoaLai");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPrice).HasColumnName("FLD_PRICE");
            entity.Property(e => e.FldReturn).HasColumnName("FLD_RETURN");
            entity.Property(e => e.FldSoCapPhuHon)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SoCapPhuHon");
            entity.Property(e => e.FldTienHoa)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TienHoa");
            entity.Property(e => e.FldTrungCapPhuHon)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TrungCapPhuHon");
            entity.Property(e => e.FldType)
                .HasMaxLength(4000)
                .HasDefaultValue("")
                .HasColumnName("FLD_TYPE");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.ShowHide)
                .HasDefaultValue(0)
                .HasColumnName("Show_Hide");
        });

        modelBuilder.Entity<Itemsell>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ITEMSELL");

            entity.Property(e => e.FldDesc)
                .HasMaxLength(4000)
                .HasColumnName("FLD_DESC");
            entity.Property(e => e.FldName)
                .HasMaxLength(4000)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNumber)
                .HasDefaultValue(1)
                .HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPrice).HasColumnName("FLD_PRICE");
            entity.Property(e => e.FldSubtype)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SUBTYPE");
            entity.Property(e => e.FldSubtypename).HasColumnName("FLD_SUBTYPENAME");
            entity.Property(e => e.FldTag)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("FLD_TAG");
            entity.Property(e => e.FldType)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TYPE");
            entity.Property(e => e.FldTypename).HasColumnName("FLD_TYPENAME");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<ItemsellCopy>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ITEMSELL_copy");

            entity.Property(e => e.FldDays)
                .HasDefaultValue(0)
                .HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldDesc)
                .HasMaxLength(4000)
                .HasColumnName("FLD_DESC");
            entity.Property(e => e.FldMagic1)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldName)
                .HasMaxLength(255)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNumber)
                .HasDefaultValue(1)
                .HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPhaiChangKhoaLai)
                .HasDefaultValue(0)
                .HasColumnName("FLD_PhaiChangKhoaLai");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPrice).HasColumnName("FLD_PRICE");
            entity.Property(e => e.FldReturn).HasColumnName("FLD_RETURN");
            entity.Property(e => e.FldSoCapPhuHon)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SoCapPhuHon");
            entity.Property(e => e.FldSubtype)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SUBTYPE");
            entity.Property(e => e.FldSubtypename).HasColumnName("FLD_SUBTYPENAME");
            entity.Property(e => e.FldTag)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("FLD_TAG");
            entity.Property(e => e.FldTienHoa)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TienHoa");
            entity.Property(e => e.FldTrungCapPhuHon)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TrungCapPhuHon");
            entity.Property(e => e.FldType)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TYPE");
            entity.Property(e => e.FldTypename).HasColumnName("FLD_TYPENAME");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<ItemsellCopy1>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ITEMSELL_copy1");

            entity.Property(e => e.FldDesc)
                .HasMaxLength(4000)
                .HasColumnName("FLD_DESC");
            entity.Property(e => e.FldName)
                .HasMaxLength(4000)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNumber)
                .HasDefaultValue(1)
                .HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPrice).HasColumnName("FLD_PRICE");
            entity.Property(e => e.FldSubtype)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SUBTYPE");
            entity.Property(e => e.FldSubtypename).HasColumnName("FLD_SUBTYPENAME");
            entity.Property(e => e.FldTag)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("FLD_TAG");
            entity.Property(e => e.FldType)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TYPE");
            entity.Property(e => e.FldTypename).HasColumnName("FLD_TYPENAME");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<ItemsellTestAoChoang>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ITEMSELL_test_ao_choang");

            entity.Property(e => e.FldDays)
                .HasDefaultValue(0)
                .HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldDesc)
                .HasMaxLength(4000)
                .HasColumnName("FLD_DESC");
            entity.Property(e => e.FldMagic1)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldName)
                .HasMaxLength(255)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNumber)
                .HasDefaultValue(1)
                .HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPhaiChangKhoaLai)
                .HasDefaultValue(0)
                .HasColumnName("FLD_PhaiChangKhoaLai");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPrice).HasColumnName("FLD_PRICE");
            entity.Property(e => e.FldReturn).HasColumnName("FLD_RETURN");
            entity.Property(e => e.FldSoCapPhuHon)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SoCapPhuHon");
            entity.Property(e => e.FldTienHoa)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TienHoa");
            entity.Property(e => e.FldTrungCapPhuHon)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TrungCapPhuHon");
            entity.Property(e => e.FldType)
                .HasDefaultValue(1)
                .HasColumnName("FLD_TYPE");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<Itmeclss>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ITMECLSS");

            entity.Property(e => e.FldBd).HasColumnName("FLD_BD");
            entity.Property(e => e.FldDays).HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldFjNj).HasColumnName("FLD_FJ_NJ");
            entity.Property(e => e.FldFjThucTinh).HasColumnName("FLD_FJ_ThucTinh");
            entity.Property(e => e.FldFjTienHoa).HasColumnName("FLD_FJ_TienHoa");
            entity.Property(e => e.FldFjTrungCapPhuHon).HasColumnName("FLD_FJ_TrungCapPhuHon");
            entity.Property(e => e.FldMagic0).HasColumnName("FLD_MAGIC0");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5).HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldReside).HasColumnName("FLD_RESIDE");
            entity.Property(e => e.FldSql)
                .HasMaxLength(500)
                .HasColumnName("FLD_SQL");
            entity.Property(e => e.FldType).HasColumnName("FLD_TYPE");
            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<MailCod>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("MailCod_pkey");

            entity.ToTable("MailCod");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("CREATED_AT");
            entity.Property(e => e.Description)
                .IsRequired()
                .HasMaxLength(200)
                .HasColumnName("DESCRIPTION");
            entity.Property(e => e.ExpiredAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("EXPIRED_AT");
            entity.Property(e => e.ItemName)
                .IsRequired()
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasDefaultValue("")
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("ITEM_NAME");
            entity.Property(e => e.Itembyte)
                .IsRequired()
                .HasMaxLength(100)
                .HasColumnName("ITEMBYTE");
            entity.Property(e => e.Paid).HasColumnName("PAID");
            entity.Property(e => e.Price).HasColumnName("PRICE");
            entity.Property(e => e.Receiver)
                .IsRequired()
                .HasMaxLength(16)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("RECEIVER");
            entity.Property(e => e.Sender)
                .IsRequired()
                .HasMaxLength(16)
                .IsUnicode(false)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("SENDER");
            entity.Property(e => e.Status).HasColumnName("STATUS");
        });

        modelBuilder.Entity<Marketplace>(entity =>
        {
            entity.ToTable("MARKETPLACE");

            entity.HasIndex(e => e.Id, "MARKETPLACE_ID_key").IsUnique();

            entity.HasIndex(e => e.ProductCode, "MARKETPLACE_PRODUCT_CODE_key").IsUnique();

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BaseAmount).HasColumnName("BASE_AMOUNT");
            entity.Property(e => e.BasePrice).HasColumnName("BASE_PRICE");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("CREATED_AT");
            entity.Property(e => e.CurrentAmount).HasColumnName("CURRENT_AMOUNT");
            entity.Property(e => e.ExpiredAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("EXPIRED_AT");
            entity.Property(e => e.Filter1).HasColumnName("FILTER_1");
            entity.Property(e => e.Filter2).HasColumnName("FILTER_2");
            entity.Property(e => e.Filter3).HasColumnName("FILTER_3");
            entity.Property(e => e.Filter4).HasColumnName("FILTER_4");
            entity.Property(e => e.Filter5).HasColumnName("FILTER_5");
            entity.Property(e => e.FldPrice).HasColumnName("FLD_PRICE");
            entity.Property(e => e.Item)
                .IsRequired()
                .HasMaxLength(100)
                .HasColumnName("ITEM");
            entity.Property(e => e.ItemName)
                .IsRequired()
                .HasMaxLength(255)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("ITEM_NAME");
            entity.Property(e => e.ProductCode)
                .IsRequired()
                .HasMaxLength(16)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("PRODUCT_CODE");
            entity.Property(e => e.SellerId)
                .IsRequired()
                .HasMaxLength(16)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("SELLER_ID");
            entity.Property(e => e.SellerName)
                .IsRequired()
                .HasMaxLength(16)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("SELLER_NAME");
            entity.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(1000)
                .HasDefaultValue("PENDING")
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("STATUS");
        });

        modelBuilder.Entity<OrderDetail>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ORDER_DETAIL");

            entity.HasIndex(e => e.Id, "ORDER_DETAIL_ID_key").IsUnique();

            entity.Property(e => e.Amount).HasColumnName("AMOUNT");
            entity.Property(e => e.Buyyer)
                .IsRequired()
                .HasMaxLength(1000)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("BUYYER");
            entity.Property(e => e.CreatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("CREATED_AT");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.MarketplaceId).HasColumnName("MARKETPLACE_ID");
            entity.Property(e => e.Message)
                .HasMaxLength(1000)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("MESSAGE");
            entity.Property(e => e.Price).HasColumnName("PRICE");
            entity.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(1000)
                .HasDefaultValue("PENDING")
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("STATUS");
            entity.Property(e => e.UpdatedAt)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime")
                .HasColumnName("UPDATED_AT");

            entity.HasOne(d => d.Marketplace).WithMany()
                .HasForeignKey(d => d.MarketplaceId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("ORDER_DETAIL_MARKETPLACE_ID_fkey");
        });

        modelBuilder.Entity<Shopcategory>(entity =>
        {
            entity.ToTable("SHOPCATEGORY");

            entity.HasIndex(e => e.Id, "SHOPCATEGORY_ID_key").IsUnique();

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Displayorder).HasColumnName("DISPLAYORDER");
            entity.Property(e => e.Name)
                .HasMaxLength(1000)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("NAME");
            entity.Property(e => e.Parentid).HasColumnName("PARENTID");
        });

        modelBuilder.Entity<VongQuayResult>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("vong_quay_result");

            entity.Property(e => e.CreatedAt)
                .HasPrecision(0)
                .HasDefaultValueSql("(NULL)")
                .HasColumnName("created_at");
            entity.Property(e => e.FldPid)
                .HasDefaultValueSql("(NULL)")
                .HasColumnName("FLD_PID");
            entity.Property(e => e.FldStatus)
                .HasDefaultValueSql("(NULL)")
                .HasColumnName("FLD_STATUS");
            entity.Property(e => e.FldUserId)
                .HasDefaultValueSql("(NULL)")
                .HasColumnName("FLD_USER_ID");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("id");
            entity.Property(e => e.UpdatedAt)
                .HasPrecision(0)
                .HasDefaultValueSql("(NULL)")
                .HasColumnName("updated_at");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
