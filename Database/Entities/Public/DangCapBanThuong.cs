﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class DangCapBanThuong
{
    public int Id { get; set; }

    public int? DangCap { get; set; }

    public int? VoHuan { get; set; }

    public int? <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }

    public int? TienBac { get; set; }

    public int? SinhMenh { get; set; }

    public int? CongKich { get; set; }

    public int? PhongNgu { get; set; }

    public int? NeTranh { get; set; }

    public int? TrungDich { get; set; }

    public int? NoiCong { get; set; }

    public int? SetId { get; set; }

    public string? GoiVatPham { get; set; }
}
