﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblXwwlDropG
{
    public int FldLevel1 { get; set; }

    public int FldLevel2 { get; set; }

    public int FldPid { get; set; }

    public string FldName { get; set; } = null!;

    public int FldMagic0 { get; set; }

    public int FldMagic1 { get; set; }

    public int FldMagic2 { get; set; }

    public int FldMagic3 { get; set; }

    public int FldMagic4 { get; set; }

    public int FldSoCapPhuHon { get; set; }

    public int FldTrungCapPhuHon { get; set; }

    public int FldTienHoa { get; set; }

    public int FldKhoaLai { get; set; }

    public int FldPp { get; set; }

    public string? FldSunx { get; set; }

    public int? CoMoThongBao { get; set; }
}
