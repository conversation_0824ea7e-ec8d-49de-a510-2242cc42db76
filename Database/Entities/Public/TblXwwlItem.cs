﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblXwwlItem
{
    public int FldPid { get; set; }

    public string? FldName { get; set; }

    public int? FldReside1 { get; set; }

    public int? FldReside2 { get; set; }

    public int? FldSex { get; set; }

    public int? FldLevel { get; set; }

    public int? FldUpLevel { get; set; }

    public int? FldRecycleMoney { get; set; }

    public int? FldSaleMoney { get; set; }

    public int? FldQuestitem { get; set; }

    public int? FldNj { get; set; }

    public int? FldDf { get; set; }

    public int? FldAt1 { get; set; }

    public int? FldAt2 { get; set; }

    public int? FldAp { get; set; }

    public int? FldJobLevel { get; set; }

    public int? FldZx { get; set; }

    public int? FldEl { get; set; }

    public int? FldWx { get; set; }

    public int? FldWxjd { get; set; }

    public int? FldMoney { get; set; }

    public int? FldWeight { get; set; }

    public int? FldType { get; set; }

    public int? FldNeedMoney { get; set; }

    public int? FldNeedFightexp { get; set; }

    public int? FldMagic1 { get; set; }

    public int? FldMagic2 { get; set; }

    public int? FldMagic3 { get; set; }

    public int? FldMagic4 { get; set; }

    public int? FldMagic5 { get; set; }

    public int? FldSide { get; set; }

    public int? FldSellType { get; set; }

    public int? FldLock { get; set; }

    public int? FldSeries { get; set; }

    public int? FldIntegration { get; set; }

    public string? FldDes { get; set; }

    public int? FldHeadWear { get; set; }
}
