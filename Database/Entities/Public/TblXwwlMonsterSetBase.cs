﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblXwwlMonsterSetBase
{
    public int FldIndex { get; set; }

    public int? FldPid { get; set; }

    public double? FldX { get; set; }

    public double? FldZ { get; set; }

    public double? FldY { get; set; }

    public double? FldFace0 { get; set; }

    public double? FldFace { get; set; }

    public int? FldMid { get; set; }

    public string? FldName { get; set; }

    public long? FldHp { get; set; }

    public long? FldAt { get; set; }

    public long? FldDf { get; set; }

    public int? FldNpc { get; set; }

    public int? FldNewtime { get; set; }

    public int? FldLevel { get; set; }

    public int? FldExp { get; set; }

    public int FldAuto { get; set; }

    public int FldBoss { get; set; }

    public int FldGold { get; set; }

    public int FldAccuracy { get; set; }

    public int FldEvasion { get; set; }

    public int FldQitemDrop { get; set; }

    public int FldQdropPp { get; set; }

    public int FldFreeDrop { get; set; }

    public int FldAmount { get; set; }

    public int FldAoe { get; set; }

    public int FldActive { get; set; }
}
