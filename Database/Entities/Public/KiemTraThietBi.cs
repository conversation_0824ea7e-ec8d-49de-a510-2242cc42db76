﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class KiemTraThietBi
{
    public int Id { get; set; }

    public int VatPhamLoaiHinh { get; set; }

    public int? VatPhamCaoNhatCongKichGiaTri { get; set; }

    public int? VatPhamCaoNhatPhongNguGiaTri { get; set; }

    public int? VatPhamCaoNhatHpgiaTri { get; set; }

    public int? VatPhamCaoNhatNoiCongGiaTri { get; set; }

    public int? VatPhamCaoNhatTrungDichGiaTri { get; set; }

    public int? VatPhamCaoNhatNeTranhGiaTri { get; set; }

    public int? VatPhamCaoNhatCongKichVoCongGiaTri { get; set; }

    public int? VatPhamCaoNhatKhiCongGiaTri { get; set; }

    public int? VatPhamCaoNhatPhuHonGiaTri { get; set; }
}
