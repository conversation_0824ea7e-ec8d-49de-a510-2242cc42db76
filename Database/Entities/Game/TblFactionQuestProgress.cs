﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Game;

public partial class TblFactionQuestProgress
{
    public int Id { get; set; }

    public int FactionId { get; set; }

    public int QuestId { get; set; }

    public int CurrentCount { get; set; }

    public byte Status { get; set; }

    public DateTime AcceptedTime { get; set; }

    public DateTime LastUpdateTime { get; set; }

    public DateTime? CompletedTime { get; set; }

    public DateTime? CancelledTime { get; set; }

    public DateTime LastResetTime { get; set; }

    public DateTime? LastCompletedTime { get; set; }

    public virtual TblGroupQuest Quest { get; set; } = null!;
}
