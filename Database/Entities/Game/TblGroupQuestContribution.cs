﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Game;

public partial class TblGroupQuestContribution
{
    public int Id { get; set; }

    public int QuestId { get; set; }

    public int ProgressId { get; set; }

    public int PlayerId { get; set; }

    public string PlayerName { get; set; } = null!;

    public int? GuildId { get; set; }

    public int? FactionId { get; set; }

    public int ActionType { get; set; }

    public int? TargetId { get; set; }

    public string? TargetName { get; set; }

    public int ContributionCount { get; set; }

    public DateTime ContributionTime { get; set; }

    public bool HasReceivedReward { get; set; }

    public virtual TblGroupQuest Quest { get; set; } = null!;

    public virtual ICollection<TblGroupQuestContributionLog> TblGroupQuestContributionLogs { get; set; } = new List<TblGroupQuestContributionLog>();
}
