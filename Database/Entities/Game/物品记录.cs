﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Game;

public partial class 物品记录
{
    public int Id { get; set; }

    public string? UserId { get; set; }

    public string? UserName { get; set; }

    public string? ToUserId { get; set; }

    public string? ToUserName { get; set; }

    public string? 全局id { get; set; }

    public string? 物品id { get; set; }

    public string? 物品名 { get; set; }

    public int? 物品数量 { get; set; }

    public string? 物品属性 { get; set; }

    public long? 钱数 { get; set; }

    public string? 类型 { get; set; }

    public DateTime? 时间 { get; set; }
}
