
using System;

namespace HeroYulgang.Utils
{
    public class Crypto
    {
        private static int key16 = 5761;
    internal short headerKey;

    public static uint ComputeKeySegment(uint value)
    {
	    var eax = value;
	    var ecx = eax ^ 0x3D0000;
	    ecx = (ecx >> 16) ^ eax;
	    ecx = (ecx + ecx * 8) & 0xFFFFFFFF;
	    eax = ecx;
	    eax = (eax >> 4) ^ ecx;
	    eax = (eax * 0x73C2EB2D) & 0xFFFFFFFF;
	    var ebx = eax;
	    ebx = (ebx >> 15) ^ eax;
	    return ebx;
    }

    public static byte[] DecryptPacketData(byte[] encryptedData, uint key)
    {
        try
        {
            if (encryptedData.Length < 1)
                return encryptedData;

            var keySegment = ComputeKeySegment(key);
            var decryptedData = (byte[])encryptedData.Clone();
            var packetSize = encryptedData.Length;

            if (packetSize >= 4)
            {
                var fullBlocks = packetSize / 4;
                var remainder = packetSize % 4;
                for (var i = 0; i < fullBlocks; i++)
                {
                    var offset = i * 4;
                    var segment = BitConverter.ToUInt32(decryptedData, offset);
                    var decryptedSegment = segment ^ keySegment;
                    BitConverter.GetBytes(decryptedSegment).CopyTo(decryptedData, offset);
                }

                packetSize = remainder;
            }

            if (packetSize == 1)
            {
                decryptedData[decryptedData.Length - 1] ^= (byte)(keySegment & 0xFF);
            }
            else if (packetSize == 2)
            {
                var segment = BitConverter.ToUInt16(decryptedData, decryptedData.Length - 2);
                var decryptedSegment = (ushort)(segment ^ (keySegment & 0xFFFF));
                BitConverter.GetBytes(decryptedSegment).CopyTo(decryptedData, decryptedData.Length - 2);
            }
            else if (packetSize == 3)
            {
                var lastThreeBytes = new byte[4];
                Array.Copy(decryptedData, decryptedData.Length - 3, lastThreeBytes, 0, 3);
                var segment = BitConverter.ToUInt32(lastThreeBytes, 0);
                var keySegment24 = keySegment & 0xFFFFFF;
                var decryptedSegment = segment ^ keySegment24;
                var decryptedBytes = BitConverter.GetBytes(decryptedSegment);
                Array.Copy(decryptedBytes, 0, decryptedData, decryptedData.Length - 3, 3);
            }

            return decryptedData;
        }
        catch (Exception Ex)
        {
            Console.WriteLine("Exception error DecryptPacketData: " + Ex.Message);
            return new byte[0];
        }
    }
    public static void EncryptPacketData2(byte[] data, uint key, int size)
    {
	    if (data == null || size <= 0 || size > data.Length)
		    throw new ArgumentException("Invalid data or size.");

	    var ebx = ComputeKeySegment(key);

	    if (size >= 4)
	    {
		    var iterations = size / 4;
		    var index = 0;
		    while (iterations-- > 0)
		    {
			    var dataVal = BitConverter.ToUInt32(data, index);
			    dataVal ^= ebx;
			    Buffer.BlockCopy(BitConverter.GetBytes(dataVal), 0, data, index, 4);
			    index += 4;
		    }
	    }

	    if (size % 4 != 0)
	    {
		    var index = size - size % 4;
		    for (var i = 0; i < size % 4; i++)
		    {
			    data[index] ^= (byte)(ebx >> (8 * i));
			    index++;
		    }
	    }
    }
    public byte[] EncryptPacket(byte[] packet)
    {
        try
        {
            if (packet[0] != 0xaa || packet[1] != 0x55 || packet[packet.Length - 2] != 0x55 ||
                packet[packet.Length - 1] !=
                0xaa)
            {
                Console.WriteLine( BitConverter.ToString(packet));
                Console.WriteLine( "EncryptPacket Error length");
                return packet;
            }

            var packet_data = new byte[packet.Length - 6];
            Buffer.BlockCopy(packet, 4, packet_data, 0, packet.Length - (4 + 2));
            EncryptPacketData2(packet_data, (uint)key16, packet_data.Length);
            //Console.WriteLine(88, "encrypted_data : " + BitConverter.ToString(BitConverter.GetBytes((ushort)key16)) + " : " + BitConverter.ToString(packet_data));
            BitConverter.ToString(packet_data);
            var encrypted_packet = new byte[packet_data.Length + 8];

            // Copy start marker, serverid, length
            Buffer.BlockCopy(packet, 0, encrypted_packet, 0, 2);
            // Copy end marker
            Buffer.BlockCopy(packet, packet.Length - 2, encrypted_packet, encrypted_packet.Length - 2, 2);
            // Copy encrypted data length
            Buffer.BlockCopy(BitConverter.GetBytes(packet_data.Length + 2), 0, encrypted_packet, 2, 2);
            // Copy the key (first 2 bytes only, 16-bit key)
            Buffer.BlockCopy(BitConverter.GetBytes((ushort)key16), 0, encrypted_packet, 4, 2);
            // Copy encrypted data
            Buffer.BlockCopy(packet_data, 0, encrypted_packet, 6, packet_data.Length);
            //Console.WriteLine(88, "encrypted_packet : " + BitConverter.ToString(encrypted_packet));
            return encrypted_packet;
        }
        catch (Exception Ex)
        {
            Console.WriteLine("Exception error EncryptPacket: " + Ex.Message);
            return new byte[0];
        }
    }
	public static byte[] DecryptPacket(byte[] packet)
	{
		try
		{
			if (packet[0] != 0xaa || packet[1] != 0x55 || packet[packet.Length - 2] != 0x55 ||
			    packet[packet.Length - 1] !=
			    0xaa)
			{
				Console.WriteLine( ":Length<22");
				return packet;
			}

			// Convert packet to hex string
			key16 = BitConverter.ToInt16(packet, 4);
			var encryptedData = new byte[packet.Length - 8];
			Buffer.BlockCopy(packet, 6, encryptedData, 0, packet.Length - 8);

			var decryptedData = DecryptPacketData(encryptedData, (uint)key16);
			var packet_new = new byte[decryptedData.Length + 6];

			Buffer.BlockCopy(packet, 0, packet_new, 0, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(decryptedData.Length), 0, packet_new, 2, 2);

			Buffer.BlockCopy(decryptedData, 0, packet_new, 4, decryptedData.Length);

			Buffer.BlockCopy(packet, packet.Length - 2, packet_new, packet_new.Length - 2, 2);

			return packet_new;
		}
		catch (Exception ex)
		{
			Console.WriteLine("Exception error DecryptPacket: " + ex.Message);
			return [];
		}
	}
    }
}
