public static class ItemDef
{
	public static class Item
	{
		public const int 金刚石 = 800000001;

		public const int 寒玉石 = 800000002;

		public const int 强化石 = 800000006;

		public const int 神秘金刚石 = 800000011;

		public const int 神秘寒玉石 = 800000012;

		public const int 热血石 = 800000013;

		public const int 混元金刚石 = 800000023;

		public const int 冰魄寒玉石 = 800000024;

		public const int 神秘金刚石_追伤 = 800000025;

		public const int 神秘金刚石_武功 = 800000026;

		public const int 属性原石 = 800000027;

		public const int 属性石 = 800000028;

		public const int 初级奇玉石 = 800000046;

		public const int 中级奇玉石 = 800000047;

		public const int 高级奇玉石 = 800000048;

		public const int 最高级奇玉石 = 800000049;

		public const int 高级强化石 = 800000060;

		public const int 乾坤金刚石 = 800000061;

		public const int 凝霜寒玉石 = 800000062;
	}

	public static class RESIDE1
	{
		public const int 刀客 = 1;

		public const int 剑客 = 2;

		public const int 枪客 = 3;

		public const int 弓手 = 4;

		public const int 医生 = 5;

		public const int 宠物公用 = 6;

		public const int 龙猫 = 7;

		public const int 雕 = 8;

		public const int 豹 = 9;

		public const int 老虎 = 10;

		public const int 刺客 = 11;

		public const int 琴师 = 12;

		public const int 韩ThuBay官 = 13;

		public const int 谭花灵 = 14;

		public const int 狼 = 15;

		public const int 格斗家 = 16;

		public const int 梅柳真 = 17;

		public const int 卢风郎 = 18;

		public const int 东陵神女 = 19;

		public const int 龙 = 20;
	}

	public static class RESIDE2
	{
		public const int 衣服 = 1;

		public const int 护手 = 2;

		public const int 武器 = 3;

		public const int 鞋子 = 4;

		public const int 内甲 = 5;

		public const int 项链 = 6;

		public const int 耳环 = 7;

		public const int 戒指 = 8;

		public const int 披风 = 9;

		public const int 石头 = 10;

		public const int 幸运符 = 11;

		public const int 强化石天机石 = 12;

		public const int 武功书 = 13;

		public const int QigongBook = 14;

		public const int 触发器VatPham = 15;

		public const int 盒子 = 17;

		public const int 属性石 = 19;

		public const int 箭 = 20;

		public const int 门甲 = 21;

		public const int 宠物 = 22;

		public const int 宠物用品1 = 23;

		public const int 宠物用品2 = 24;

		public const int 宠物用品3 = 25;

		public const int 奇玉石 = 27;

		public const int 焚魂石分解石 = 28;

		public const int 灵宠 = 30;

		public const int 花珠 = 31;

		public const int 宝珠武器 = 32;

		public const int 宝珠衣服 = 33;

		public const int 宝珠内甲 = 34;

		public const int 宝珠护手 = 35;

		public const int 宝珠鞋子 = 36;
	}

	public struct MyItem
	{
		public string Text;

		public int Value;

		public MyItem(string text, int value)
		{
			Text = text;
			Value = value;
		}

		public override string ToString()
		{
			return Text;
		}
	}

	
	public static bool TrangBi_Position_CoChinhXacKhong(int FLD_RESIDE2, int 目标Position)
	{
		switch (FLD_RESIDE2)
		{
		case 1:
			if (目标Position == 0)
			{
				break;
			}
			return false;
		case 2:
			if (目标Position == 1 || 目标Position == 2)
			{
				break;
			}
			return false;
		case 3:
			if (目标Position == 3)
			{
				break;
			}
			return false;
		case 4:
			if (目标Position == 4)
			{
				break;
			}
			return false;
		case 5:
			if (目标Position == 5)
			{
				break;
			}
			return false;
		case 6:
			if (目标Position == 6)
			{
				break;
			}
			return false;
		case 7:
			if (目标Position == 7 || 目标Position == 8)
			{
				break;
			}
			return false;
		case 8:
			if (目标Position == 9 || 目标Position == 10)
			{
				break;
			}
			return false;
		case 9:
			if (目标Position == 11)
			{
				break;
			}
			return false;
		case 10:
		case 11:
		case 12:
		case 13:
		case 14:
		case 15:
		case 16:
		case 17:
		case 18:
		case 19:
		case 23:
		case 24:
		case 25:
		case 26:
		case 27:
		case 28:
		case 29:
			return false;
		case 20:
			if (目标Position == 12)
			{
				break;
			}
			return false;
		case 21:
			if (目标Position == 13)
			{
				break;
			}
			return false;
		case 22:
			if (目标Position == 14)
			{
				break;
			}
			return false;
		case 30:
			if (目标Position == 15)
			{
				break;
			}
			return false;
		case 31:
			if (目标Position == 16)
			{
				break;
			}
			return false;
		case 32:
			if (目标Position == 3)
			{
				break;
			}
			return false;
		case 33:
			if (目标Position == 0)
			{
				break;
			}
			return false;
		case 34:
			if (目标Position == 5)
			{
				break;
			}
			return false;
		case 35:
			if (目标Position == 1 || 目标Position == 2)
			{
				break;
			}
			return false;
		case 36:
			if (目标Position == 4)
			{
				break;
			}
			return false;
		default:
			return false;
		}
		return true;
	}

	public static bool VatPhamNgheNghiep_CoTrungKhopKhong(int PlayerJob, int RESIDE1, int RESIDE2, int pid)
	{
		switch (PlayerJob)
		{
		case 1:
		case 2:
		case 3:
		case 4:
		case 5:
			if (PlayerJob == RESIDE1 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 6:
			if (RESIDE1 == 11 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 7:
			if (RESIDE1 == 12 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 8:
			if (RESIDE1 == 13 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 9:
			if (RESIDE1 == 14 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 10:
			if (RESIDE1 == 16 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 11:
			if (RESIDE1 == 17)
			{
				return true;
			}
			switch (RESIDE2)
			{
			case 9:
				return true;
			case 6:
				if (pid == 1100005 || pid == 1100006)
				{
					return true;
				}
				break;
			case 30:
				return true;
			case 20:
				return true;
			case 21:
				return true;
			case 22:
				return true;
			case 32:
				return true;
			case 33:
				return true;
			case 34:
				return true;
			case 35:
				return true;
			case 36:
				return true;
			}
			break;
		case 12:
			if (RESIDE1 == 18 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		case 13:
			if (RESIDE1 == 19 || RESIDE1 == 0)
			{
				return true;
			}
			break;
		}
		return false;
	}
}
