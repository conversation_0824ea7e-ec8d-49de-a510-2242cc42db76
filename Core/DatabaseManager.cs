using System;
using Microsoft.EntityFrameworkCore;
using HeroYulgang.Services;
using HeroYulgang.Database.Entities.Account;
using HeroYulgang.Database.Entities.Game;
using HeroYulgang.Database.Entities.Public;
using HeroYulgang.Database.Entities.BBG;

namespace HeroYulgang.Core
{
    public class DatabaseManager
    {
        private static DatabaseManager? _instance;
        private readonly ConfigManager _configManager;

        private AccountDbContext? _accountDbContext;
        private GameDbContext? _gameDbContext;
        private PublicDbContext? _publicDbContext;
        private BBGDbContext? _bbgDbContext;

        public static DatabaseManager Instance => _instance ??= new DatabaseManager();

        public AccountDbContext AccountDb => _accountDbContext ?? throw new InvalidOperationException("AccountDbContext chưa được khởi tạo");
        public GameDbContext GameDb => _gameDbContext ?? throw new InvalidOperationException("GameDbContext chưa được khởi tạo");
        public PublicDbContext PublicDb => _publicDbContext ?? throw new InvalidOperationException("PublicDbContext chưa được khởi tạo");
        public BBGDbContext BbgDbContext => _bbgDbContext ?? throw new InvalidOperationException("BBGDbContext chưa được khởi tạo");

        private DatabaseManager()
        {
            _configManager = ConfigManager.Instance;
        }

        public bool Initialize()
        {
            try
            {
                // Khởi tạo kết nối đến Account Database
                var accountOptions = new DbContextOptionsBuilder<AccountDbContext>()
                    .UseSqlServer(_configManager.ConnectionStrings.AccountDb)
                    .Options;
                _accountDbContext = new AccountDbContext(accountOptions);

                // Khởi tạo kết nối đến Game Database
                var gameOptions = new DbContextOptionsBuilder<GameDbContext>()
                    .UseSqlServer(_configManager.ConnectionStrings.GameDb)
                    .Options;
                _gameDbContext = new GameDbContext(gameOptions);

                // Khởi tạo kết nối đến Public Database
                var publicOptions = new DbContextOptionsBuilder<PublicDbContext>()
                    .UseSqlServer(_configManager.ConnectionStrings.PublicDb)
                    .Options;
                _publicDbContext = new PublicDbContext(publicOptions);

                var bbgOptions = new DbContextOptionsBuilder<BBGDbContext>()
                    .UseSqlServer(_configManager.ConnectionStrings.BBGDb)
                    .Options;
                _bbgDbContext = new BBGDbContext(bbgOptions);

                // Kiểm tra kết nối
                if (TestConnections())
                {
                    Logger.Instance.Info("Đã khởi tạo kết nối đến tất cả cơ sở dữ liệu thành công");
                    return true;
                }
                else
                {
                    Logger.Instance.Error("Không thể kết nối đến một hoặc nhiều cơ sở dữ liệu");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi tạo kết nối cơ sở dữ liệu: {ex.Message}");
                return false;
            }
        }

        private bool TestConnections()
        {
            try
            {
                // Kiểm tra kết nối đến Account Database
                _accountDbContext?.Database.CanConnect();
                Logger.Instance.Info("Kết nối đến Account Database thành công");

                // Kiểm tra kết nối đến Game Database
                _gameDbContext?.Database.CanConnect();
                Logger.Instance.Info("Kết nối đến Game Database thành công");

                // Kiểm tra kết nối đến Public Database
                _publicDbContext?.Database.CanConnect();
                Logger.Instance.Info("Kết nối đến Public Database thành công");

                _bbgDbContext?.Database.CanConnect();
                Logger.Instance.Info("Kết nối đến BBG Database thành công");

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi kiểm tra kết nối cơ sở dữ liệu: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            try
            {
                _accountDbContext?.Dispose();
                _gameDbContext?.Dispose();
                _publicDbContext?.Dispose();
                _bbgDbContext?.Dispose();

                Logger.Instance.Info("Đã đóng tất cả kết nối cơ sở dữ liệu");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi đóng kết nối cơ sở dữ liệu: {ex.Message}");
            }
        }
    }
}
