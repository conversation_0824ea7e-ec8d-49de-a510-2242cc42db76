using System;
using System.Net;
using Akka.Actor;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// <PERSON><PERSON><PERSON> trữ thông tin về phiên kết n<PERSON>i của client
    /// </summary>
    public class ClientSession(int sessionId, EndPoint remoteEndPoint, IActorRef connection)
    {
        public int SessionId { get; } = sessionId;
        public IPEndPoint RemoteEndPoint { get; } = (IPEndPoint)remoteEndPoint;
        public DateTime ConnectedTime { get; } = DateTime.Now;
        public DateTime LastActivityTime { get; private set; } = DateTime.Now;
        public bool IsAuthenticated { get; set; } = false;
        public string? AccountId { get; set; }
        public string? CharacterId { get; set; }
        public IActorRef Connection { get; } = connection;

        public void UpdateActivity()
        {
            LastActivityTime = DateTime.Now;
        }
    }
}
