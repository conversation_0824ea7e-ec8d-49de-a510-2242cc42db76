using System;
using System.Net;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Actors;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using RxjhServer;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace HeroYulgang.Core.Network
{
    /// <summary>
    /// Lớp ActorNetState thay thế cho NetState, sử dụng Akka.NET để gửi packet
    /// </summary>
    public class ActorNetState
    {
        private readonly IActorRef _connection;
        private readonly int _sessionId;
        private readonly IPEndPoint _remoteEndPoint;
        private bool _running = true;
        private bool _treoMay = false;
        private bool _online = false;

        private bool _login = false;
        public bool Login
        {
            get => _login;
            set => _login = value;
        }
        public bool Online
        {
            get => _online;
            set => _online = value;
        }
        private Players? _player;

        /// <summary>
        /// Khởi tạo một ActorNetState mới
        /// </summary>
        /// <param name="connection">Tham chiếu đến actor kết nối</param>
        /// <param name="sessionId">ID phiên kết nối</param>
        /// <param name="remoteEndPoint">Địa chỉ IP và port của client</param>
        public ActorNetState(IActorRef connection, int sessionId, IPEndPoint remoteEndPoint)
        {
            _connection = connection;
            _sessionId = sessionId;
            _remoteEndPoint = remoteEndPoint;
            World.list[sessionId] = this;
        }

        /// <summary>
        /// Lấy hoặc đặt trạng thái treo máy
        /// </summary>
        public bool TreoMay
        {
            get => _treoMay;
            set => _treoMay = value;
        }

        /// <summary>
        /// Lấy hoặc đặt ID thế giới
        /// </summary>
        public int WorldId
        {
            get => _sessionId;
        }

        /// <summary>
        /// Lấy hoặc đặt tham chiếu đến đối tượng Player
        /// </summary>
        public Players? Player
        {
            get => _player;
            set => _player = value;
        }

        /// <summary>
        /// Kiểm tra xem kết nối có đang chạy không
        /// </summary>
        public bool Running => _running;

        public bool BindAccount { get; internal set; }

        /// <summary>
        /// Gửi dữ liệu đến client
        /// </summary>
        /// <param name="data">Dữ liệu cần gửi</param>
        /// <param name="length">Độ dài dữ liệu</param>
        public void Send(byte[] data, int length)
        {
            try
            {
                if (!_running) return;

                // Mã hóa dữ liệu trước khi gửi
                var encryptedData = Crypto.EncryptPacket(data);

                // Gửi dữ liệu thông qua actor
                var tcpManager = ActorSystemManager.Instance.ActorSystem.ActorSelection("/user/tcpManager");
                tcpManager.Tell(new SendPacket(_connection, encryptedData));

                // Ghi log nếu cần
                if (RxjhServer.World.Debug > 0)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Gửi dữ liệu đến client {_sessionId}: {RxjhServer.HelperTools.Converter.ToString(data)}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu đến client {_sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi dữ liệu bản đồ đến client
        /// </summary>
        /// <param name="data">Dữ liệu cần gửi</param>
        /// <param name="length">Độ dài dữ liệu</param>
        public void Send_Map_Data(byte[] data, int length)
        {
            Send(data, length);
        }

        /// <summary>
        /// Gửi nhiều gói tin đến client
        /// </summary>
        /// <param name="data">Dữ liệu cần gửi</param>
        /// <param name="length">Độ dài dữ liệu</param>
        public void SendMultiplePackage(byte[] data, int length)
        {
            Send(data, length);
        }

        /// <summary>
        /// Gửi gói tin đến client
        /// </summary>
        /// <param name="pak">Gói tin cần gửi</param>
        /// <param name="id">ID của gói tin</param>
        /// <param name="wordid">ID thế giới</param>
        public void SendPak(SendingClass pak, int id = 0, int wordid = 0)
        {
            try
            {
                if (!_running) return;

                // Chuyển đổi SendingClass thành mảng byte
                byte[] data = pak.ToArray(id, wordid);

                // Gửi dữ liệu
                Send(data, data.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi gói tin đến client {_sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Đóng kết nối và giải phóng tài nguyên
        /// </summary>
        public void Dispose()
        {
            _running = false;

            // Thông báo cho TcpManagerActor để đóng kết nối
            try
            {
                if (World.list[_sessionId] != null)
                {
                    World.list.Remove(_sessionId);
                }
                var tcpManager = ActorSystemManager.Instance.ActorSystem.ActorSelection("/user/tcpManager");
                tcpManager.Tell(new CloseConnection(_connection));
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi đóng kết nối client {_sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Chuyển đổi thành chuỗi
        /// </summary>
        public override string ToString()
        {
            return _remoteEndPoint.ToString();
        }
    }
}
