using System;
using System.IO;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using HeroYulgang.Services;

namespace HeroYulgang.Core
{
    public class ServerConfig
    {
        public string ServerName { get; set; } = "HeroGSPorted";
        public int ServerId { get; set; } = 1;
        public int GameServerPort { get; set; } = 7000;
        public int MaximumOnline { get; set; } = 1000;
        public int AutomaticConnectionTime { get; set; } = 60;
    }

    public class LoginServerConfig
    {
        public string LoginServerIP { get; set; } = "127.0.0.1";
        public int LoginServerGrpcPort { get; set; } = 6999;
        public int ClusterId { get; set; } = 1;
    }

    public class AppSettingsConfig
    {
        public bool ShowPacketData { get; set; } = true;
        public string Environment { get; set; } = "Development";
    }

    public class DatabaseConfig
    {
        public string AccountDb { get; set; } = string.Empty;
        public string GameDb { get; set; } = string.Empty;
        public string PublicDb { get; set; } = string.Empty;
        public string? BBGDb { get; set; } = null;
    }

    public class ConfigManager
    {
        private static ConfigManager? _instance;
        private IConfiguration _configuration = null!;

        public ServerConfig ServerSettings { get; private set; }
        public DatabaseConfig ConnectionStrings { get; private set; }
        public LoginServerConfig LoginServerSettings { get; private set; }
        public AppSettingsConfig AppSettings { get; private set; }

        public static ConfigManager Instance => _instance ??= new ConfigManager();

        private ConfigManager()
        {
            // Khởi tạo cấu hình mặc định
            ServerSettings = new ServerConfig();
            ConnectionStrings = new DatabaseConfig();
            LoginServerSettings = new LoginServerConfig();
            AppSettings = new AppSettingsConfig();

            try
            {
                // Đọc cấu hình từ appsettings.json
                _configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                // Đọc cấu hình server
                var serverSection = _configuration.GetSection("ServerSettings");
                if (serverSection.Exists())
                {
                    ServerSettings.ServerName = serverSection["ServerName"] ?? ServerSettings.ServerName;
                    ServerSettings.ServerId = int.TryParse(serverSection["ServerID"], out int serverId) ? serverId : ServerSettings.ServerId;
                    ServerSettings.GameServerPort = int.TryParse(serverSection["GameServerPort"], out int port) ? port : ServerSettings.GameServerPort;
                    ServerSettings.MaximumOnline = int.TryParse(serverSection["MaximumOnline"], out int maxOnline) ? maxOnline : ServerSettings.MaximumOnline;
                    ServerSettings.AutomaticConnectionTime = int.TryParse(serverSection["AutomaticConnectionTime"], out int autoTime) ? autoTime : ServerSettings.AutomaticConnectionTime;
                }

                // Đọc cấu hình kết nối cơ sở dữ liệu
                var connectionSection = _configuration.GetSection("ConnectionStrings");
                if (connectionSection.Exists())
                {
                    ConnectionStrings.AccountDb = connectionSection["AccountDb"] ?? ConnectionStrings.AccountDb;
                    ConnectionStrings.GameDb = connectionSection["GameDb"] ?? ConnectionStrings.GameDb;
                    ConnectionStrings.PublicDb = connectionSection["PublicDb"] ?? ConnectionStrings.PublicDb;
                    ConnectionStrings.BBGDb = connectionSection["BBGDb"];
                }

                // Đọc cấu hình login server
                var loginServerSection = _configuration.GetSection("LoginServerSettings");
                if (loginServerSection.Exists())
                {
                    LoginServerSettings.LoginServerIP = loginServerSection["LoginServerIP"] ?? LoginServerSettings.LoginServerIP;
                    LoginServerSettings.LoginServerGrpcPort = int.TryParse(loginServerSection["LoginServerGrpcPort"], out int grpcPort) ? grpcPort : LoginServerSettings.LoginServerGrpcPort;
                    LoginServerSettings.ClusterId = int.TryParse(loginServerSection["ClusterId"], out int clusterId) ? clusterId : LoginServerSettings.ClusterId;
                }

                // Đọc cấu hình ứng dụng
                var appSettingsSection = _configuration.GetSection("AppSettings");
                if (appSettingsSection.Exists())
                {
                    AppSettings.ShowPacketData = bool.TryParse(appSettingsSection["ShowPacketData"], out bool showPacketData) ? showPacketData : AppSettings.ShowPacketData;
                    AppSettings.Environment = appSettingsSection["Environment"] ?? AppSettings.Environment;
                }

                Logger.Instance.Info("Đã tải cấu hình từ appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải cấu hình: {ex.Message}");
            }
        }

        public void SaveConfig()
        {
            try
            {
                var configData = new
                {
                    ConnectionStrings = ConnectionStrings,
                    ServerSettings = ServerSettings,
                    LoginServerSettings = LoginServerSettings,
                    AppSettings = AppSettings
                };

                string json = JsonSerializer.Serialize(configData, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                File.WriteAllText("appsettings.json", json);
                Logger.Instance.Info("Đã lưu cấu hình vào appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lưu cấu hình: {ex.Message}");
            }
        }
    }
}
