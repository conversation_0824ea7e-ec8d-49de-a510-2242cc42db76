<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:services="using:HeroYulgang.Services"
             mc:Ignorable="d" d:DesignWidth="300" d:DesignHeight="100" Name="StatusIndicator"
             x:CompileBindings="False"
             x:Class="HeroYulgang.Controls.ServerStatusIndicator">
  <Border BorderBrush="Gray" BorderThickness="1" CornerRadius="5" Padding="10">
    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto">
      <Ellipse Grid.Row="0" Grid.Column="0" Width="20" Height="20" Margin="0,0,10,0"
               Fill="{Binding StatusColor}"
               VerticalAlignment="Center"/>

      <TextBlock Grid.Row="0" Grid.Column="1"
                 Text="{Binding StatusMessage}"
                 FontWeight="Bold" FontSize="16" VerticalAlignment="Center"/>

      <TextBlock Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,10,0,0"
                 Text="{Binding DetailsMessage}"
                 FontSize="12" Foreground="DarkGray"/>

      <TextBlock Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,10,0,0"
                 Text="Thay đổi trạng thái gần nhất:"/>

      <TextBlock Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2"
                 Text="{Binding LastStateChange, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}"/>
    </Grid>
  </Border>
</UserControl>
