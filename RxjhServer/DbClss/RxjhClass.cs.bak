using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using RxjhServer.HelperTools;

namespace RxjhServer.DbClss;

public class RxjhClass
{
	private static ItmesIDClass itmesIDClass_0 = new();

	public static string md5(string string_0)
	{
		return BitConverter.ToString(new MD5CryptoServiceProvider().ComputeHash(Encoding.ASCII.GetBytes(string_0))).Replace("-", string.Empty).ToLower();
	}

	 public static int UpdateBachBaoNewRecord(int id, string status)
    {
        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Update Cash {id} - {status}");
        var query = $"UPDATE CASH_SHOP_LOG SET STATUS = '{status}' WHERE ID = {id}";
        return DBA.ExeSqlCommand(query, "BBG");
    }
	 public static int InsertCashShopLog(string status, string message, string buyer, int amount, double price,
        int marketId, string productId)
    {
        var query = @"INSERT INTO CASH_SHOP_LOG (STATUS, MESSAGE, USERNAME, AMOUNT, PRICE, ITEM_ID, PRODUCT_ID) 
              VALUES (@Status, @Message, @Username, @Amount, @Price, @ItemId, @ProductId)";

        var parameters = new[]
        {
            new SqlParameter("@Status", status),
            new SqlParameter("@Message", message),
            new SqlParameter("@Username", buyer),
            new SqlParameter("@Amount", amount),
            new SqlParameter("@Price", price),
            new SqlParameter("@ItemId", marketId),
            new SqlParameter("@ProductId", productId)
        };

        var insertedId = DBA.ExeSqlCommand(query, "BBG", parameters);
        return insertedId;
    }

	public static void Update_Gold_Extra(string string_0, int int_0)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_MONEYEXTRALEVEL={1}  WHERE  FLD_NAME='{0}'", string_0, int_0));
	}

	public static void UpdateVoHuanMoiNgay(string FLD_NAME, string daterecieve, int luongvohuan)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  VOHUAN_GIOIHAN_THEONGAY={1}, VOHUAN_TIME='{2}'  WHERE  FLD_NAME='{0}'", FLD_NAME, luongvohuan, daterecieve));
	}

	public static int CheckSoLuongVoHuanMoiNgay(string FLD_NAME, string daterecieve)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  VOHUAN_GIOIHAN_THEONGAY  FROM  TBL_XWWL_Char  WHERE VOHUAN_TIME = @daterec and  FLD_NAME=@name", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, FLD_NAME),
			SqlDBA.MakeInParam("@daterec", SqlDbType.VarChar, 30, daterecieve)
		});
		if (dBToDataTable == null)
		{
			return 0;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return int.Parse(dBToDataTable.Rows[0][0].ToString());
		}
		return 0;
	}

	public static long CreateItemSeries()
	{
		return itmesIDClass_0.AcquireBuffer();
	}

	public static void SetUserOnline(string id, string lanip)
	{
		DBA.ExeSqlCommand("UPDATE TBL_ACCOUNT SET FLD_ONLINE= 1, FLD_LANIP = '" + lanip + "' WHERE FLD_ID = '" + id + "'", "rxjhaccount");
	}

	public static void SetChar_Online(string id, string username)
	{
		var string_ = "UPDATE TBL_XWWL_CHAR SET FLD_ONLINE= 1 WHERE FLD_ID = '" + id + "'AND FLD_NAME= '" + username + "'";
		DBA.ExeSqlCommand(string_, "GameServer");
	}

	public static void SetChar_Offline(string id, string username)
	{
		var string_ = "UPDATE TBL_XWWL_CHAR SET FLD_ONLINE= 0 WHERE FLD_ID = '" + id + "'AND FLD_NAME= '" + username + "'";
		DBA.ExeSqlCommand(string_, "GameServer");
	}

	public static void SetNhanQuaLanDau(string FLD_NAME)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  NHANQUALANDAU='{1}'  WHERE  FLD_NAME='{0}'", FLD_NAME, true));
	}

	public static int CheckNhanQuaLanDau(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Char  WHERE NHANQUALANDAU = 'true' and  FLD_NAME=@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return 0;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return 1;
		}
		return 0;
	}

	public static void Clear_FLD_CHECKIP(string id)
	{
		DBA.ExeSqlCommand("UPDATE TBL_ACCOUNT SET FLD_ONLINE= 0, FLD_CHECKLOGIN = 'false', FLD_CHECKIP = null, FLD_LANIP = null  WHERE FLD_ID = '" + id + "'", "rxjhaccount");
	}

	public static void SetUser_Offline(string id)
	{
		DBA.ExeSqlCommand("UPDATE TBL_ACCOUNT SET FLD_ONLINE= 0, FLD_CHECKLOGIN = 'false', FLD_CHECKIP = null  WHERE FLD_ID = '" + id + "'", "rxjhaccount");
	}

	public static void SetUserHoldRefresh()
	{
		DBA.ExeSqlCommand("UPDATE TBL_ACCOUNT SET FLD_CHECKLOGIN='false',  FLD_CHECKIP = null WHERE FLD_CHECKLOGIN = 'true'", "rxjhaccount");
	}

	public static void ChangeDoorService(int int_0, int int_1, int int_2)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Guild  SET  MonPhucWord={1},MonPhucMauSac={2}  WHERE  ID='{0}'", int_0, int_1, int_2));
	}

	public static void msglog(string string_0, string string_1, string string_2, string string_3, int int_0)
	{
		DBA.ExeSqlCommand($"INSERT  INTO  MsgLog  (userid,username,ToUserName,msg,msgType)  VALUES      ('{string_0}','{string_1}','{string_2}','{string_3}',{int_0})");
	}

	public static void DropRecord(string string_0, string string_1, long long_0, int int_0, string string_2, int int_1, int int_2, int int_3, int int_4, int int_5, int int_6, int int_7, int int_8, string string_3)
	{
		if (World.DropRecord == 1)
		{
			var sql = "INSERT  INTO  DropRecord  (FLD_ID,FLD_NAME,FLD_QJID,FLD_PID,FLD_INAME,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_MAP,FLD_X,FLD_Y,FLD_TYPE)      VALUES      (@UserId,@UserName,@qjid,@pid,@iname,@magic0,@magic1,@magic2,@magic3,@magic4,@map,@x,@y,@type)";
			var prams = new SqlParameter[14]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@qjid", SqlDbType.Int, 4, long_0),
				SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
				SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, int_1),
				SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, int_2),
				SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, int_3),
				SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, int_4),
				SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, int_5),
				SqlDBA.MakeInParam("@map", SqlDbType.Int, 4, int_6),
				SqlDBA.MakeInParam("@x", SqlDbType.Int, 4, int_7),
				SqlDBA.MakeInParam("@y", SqlDbType.Int, 4, int_8),
				SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void StoreRecord(string string_0, string string_1, int int_0, string string_2, string string_3, int int_1, long long_0, int int_2, int int_3, int int_4, int int_5, int int_6)
	{
		if (World.StoreRecord == 1)
		{
			var sql = "INSERT  INTO  StoreRecord  (FLD_ID,FLD_NAME,FLD_PID,FLD_INAME,FLD_TYPE,FLD_NUM,FLD_PRICE,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4)      VALUES      (@UserId,@UserName,@pid,@iname,@type,@number,@price,@magic0,@magic1,@magic2,@magic3,@magic4)";
			var prams = new SqlParameter[12]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
				SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@number", SqlDbType.Int, 4, int_1),
				SqlDBA.MakeInParam("@price", SqlDbType.VarChar, 50, long_0.ToString()),
				SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, int_2),
				SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, int_3),
				SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, int_4),
				SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, int_5),
				SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, int_6)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void DrugRecord(string string_0, string string_1, int int_0, string string_2, int int_1)
	{
		if (World.DrugRecord == 1)
		{
			var sql = "INSERT  INTO  DrugRecord(FLD_ID,FLD_NAME,FLD_PID)  VALUES  (@UserId,@UserName,@pid)";
			var prams = new SqlParameter[5]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
				SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@number", SqlDbType.Int, 4, int_1)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void SyntheticRecord(string string_0, string string_1, string string_2, int int_0, string string_3, string string_4, X_Vat_Pham_Loai VatPhamCLass_0)
	{
		if (World.SyntheticRecord == 1)
		{
			var sql = "INSERT  INTO  SyntheticRecord(FLD_ID,FLD_NAME,FLD_QJID,FLD_PID,FLD_INAME,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_TYPE,FLD_CZID,FLD_SUCCESS,FLD_QHJD)      VALUES      (@UserId,@UserName,@qjid,@pid,@iname,@magic0,@magic1,@magic2,@magic3,@magic4,@type,@czid,@success,@qhjd)";
			var prams = new SqlParameter[14]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@qjid", SqlDbType.Int, 4, (int)VatPhamCLass_0.GetItemGlobal_ID),
				SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, (int)VatPhamCLass_0.GetVatPham_ID),
				SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, VatPhamCLass_0.GetItemName()),
				SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC0),
				SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC1),
				SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC2),
				SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC3),
				SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC4),
				SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@czid", SqlDbType.Int, 4, int_0),
				SqlDBA.MakeInParam("@success", SqlDbType.VarChar, 30, string_4),
				SqlDBA.MakeInParam("@qhjd", SqlDbType.Int, 4, VatPhamCLass_0.FLD_CuongHoaSoLuong)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void BachBaoCacRecord(string string_0, string string_1, double double_0, string string_2, int int_0, int int_1)
	{
		DBA.ExeSqlCommand($"INSERT  INTO  BachBaoCacRecord (UserId,UserName,VatPham_ID,VatPhamTen,VatPhamSoLuong,NguyenBaoSoLuong)  VALUES  ('{string_0}','{string_1}','{double_0}','{string_2}',{int_0},{int_1})");
	}

	public static void LoginRecord(string string_0, string string_1, string string_2, string string_3, string string_4)
	{
		if (World.LoginRecord == 1)
		{
			var sql = "INSERT  INTO  LoginRecord  (UserId,UserName,UserIp,LoaiHinh, Mac_Address)  VALUES  (@UserId,@UserName,@UserIp,@LoaiHinh,@Mac_Address)";
			var prams = new SqlParameter[5]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@UserIp", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@LoaiHinh", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@Mac_Address", SqlDbType.VarChar, 50, string_4)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static int Check_LoginByMAC(string mac_addr, string id)
	{
		var string_ = "select * from View_CheckLogin  where   Mac_Address='" + mac_addr + "'";
		var dBToDataTable = DBA.GetDBToDataTable(string_);
		DBA.ExeSqlCommand(string_);
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return -1;
		}
		var result = int.Parse(dBToDataTable.Rows[0]["TotalLogin"].ToString());
		dBToDataTable.Dispose();
		return result;
	}

	public static void LoginRecordMac(string string_0, string string_1, string string_2, string string_3, string string_4, int serverid)
	{
		if (World.LoginRecord == 1)
		{
			var sql = "INSERT  INTO  LoginRecord_MAC  (UserId,UserName,UserIp,LoaiHinh, Mac_Address, ServerID)  VALUES  (@UserId,@UserName,@UserIp,@LoaiHinh,@Mac_Address, @ServerID)";
			var prams = new SqlParameter[6]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@UserIp", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@LoaiHinh", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@Mac_Address", SqlDbType.VarChar, 50, string_4),
				SqlDBA.MakeInParam("@ServerID", SqlDbType.Int, 50, serverid)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void Delete_Recovery_MAC_Address_Logout(string username)
	{
		DBA.ExeSqlCommand($"DELETE FROM LoginRecord_MAC WHERE UserName  ='{username}' and LoaiHinh = 'Login'");
	}

	public static void Delete_Recovery_MAC_Address_All_tren_Form1(int serverid)
	{
		DBA.ExeSqlCommand($"DELETE FROM LoginRecord_MAC WHERE LoaiHinh = 'Login' and ServerID = {serverid}");
	}

	public static void TangThemVoHuan_Record(string UserID, string UserName, int WuXun, string 说明)
	{
		if (WuXun != 0)
		{
			DBA.ExeSqlCommand($"INSERT  INTO  武勋记录  (UserId,UserName,WuXun,ShuoMing)  VALUES  ('{UserID}','{UserName}',{WuXun},'{说明}')", "GameLog");
		}
	}

	public static void ItemRecord(string string_0, string string_1, string string_2, string string_3, double double_0, int int_0, string string_4, int int_1, string string_5, int int_2, string string_6)
	{
		if (World.ItemRecord == 1)
		{
			var sql = "INSERT  INTO  ItemRecord(UserId,UserName,ToUserId,ToUserName,Global_ID,VatPham_ID,VatPhamTen,VatPhamSoLuong,VatPhamThuocTinh,SoTien,LoaiHinh)      VALUES      (@UserId,@UserName,@ToUserId,@ToUserName,@Global_ID,@VatPham_ID,@VatPhamTen,@VatPhamSoLuong,@VatPham_ThuocTinh,@SoTien,@LoaiHinh)";
			var prams = new SqlParameter[11]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@ToUserId", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@ToUserName", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@Global_ID", SqlDbType.VarChar, 30, double_0),
				SqlDBA.MakeInParam("@VatPham_ID", SqlDbType.VarChar, 30, int_0),
				SqlDBA.MakeInParam("@VatPhamTen", SqlDbType.VarChar, 30, string_4),
				SqlDBA.MakeInParam("@VatPhamSoLuong", SqlDbType.Int, 4, int_1),
				SqlDBA.MakeInParam("@VatPham_ThuocTinh", SqlDbType.VarChar, 100, string_5),
				SqlDBA.MakeInParam("@SoTien", SqlDbType.Int, 4, int_2),
				SqlDBA.MakeInParam("@LoaiHinh", SqlDbType.VarChar, 10, string_6)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void Open_Record(string string_0, string string_1, string string_2, string string_3, double double_0, int int_0, string string_4, int int_1, string string_5, int int_2, string string_6)
	{
		if (World.ItemRecord == 1)
		{
			var sql = "INSERT  INTO  ItemRecord(UserId,UserName,ToUserId,ToUserName,Global_ID,VatPham_ID,VatPhamTen,VatPhamSoLuong,VatPhamThuocTinh,SoTien,LoaiHinh)      VALUES      (@UserId,@UserName,@ToUserId,@ToUserName,@Global_ID,@VatPham_ID,@VatPhamTen,@VatPhamSoLuong,@VatPham_ThuocTinh,@SoTien,@LoaiHinh)";
			var prams = new SqlParameter[11]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@ToUserId", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@ToUserName", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@Global_ID", SqlDbType.VarChar, 30, double_0),
				SqlDBA.MakeInParam("@VatPham_ID", SqlDbType.VarChar, 30, int_0),
				SqlDBA.MakeInParam("@VatPhamTen", SqlDbType.VarChar, 30, string_4),
				SqlDBA.MakeInParam("@VatPhamSoLuong", SqlDbType.Int, 4, int_1),
				SqlDBA.MakeInParam("@VatPham_ThuocTinh", SqlDbType.VarChar, 100, string_5),
				SqlDBA.MakeInParam("@SoTien", SqlDbType.Int, 4, int_2),
				SqlDBA.MakeInParam("@LoaiHinh", SqlDbType.VarChar, 10, string_6)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static int Update_MonPhai_LienMinh_MinhChu(string Ten_BangPhai, string Ten_BangChu, int ThongBaoCongThanh_Status)
	{
		var string_ = $"UPDATE TBL_XWWL_Guild SET LienMinh_MinhChu='{Ten_BangChu}',ThongBao_CongThanh={ThongBaoCongThanh_Status} WHERE G_Name='{Ten_BangPhai}'";
		return DBA.ExeSqlCommand(string_);
	}

	public static int DoiMoi_LienMinh_ThongBao_CongThanh_TrangThai(string Ten_MinhChu, int ThongBaoCongThanh_Status)
	{
		var string_ = $"UPDATE TBL_XWWL_Guild SET ThongBao_CongThanh={ThongBaoCongThanh_Status} WHERE LienMinh_MinhChu='{Ten_MinhChu}'";
		return DBA.ExeSqlCommand(string_);
	}

	public static DataTable Load_DuLieu_LienMinh_MinhChu(string Ten_BangPhai)
	{
		if (Ten_BangPhai == "")
		{
			return null;
		}
		var string_ = "SELECT * FROM TBL_XWWL_Guild WHERE LienMinh_MinhChu = @name";
		var sqlParameter_ = new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, Ten_BangPhai) };
		var dBToDataTable = DBA.GetDBToDataTable(string_, sqlParameter_);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static DataTable DatDuoc_ThienMa_ThanCu_ChiemLinh_TinTuc()
	{
		var string_ = "SELECT * FROM ThienMaThanCung_DanhSach";
		var dBToDataTable = DBA.GetDBToDataTable(string_);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static int CapNhat_ThienMaThanCung_TinTuc(string ChiemLinhMonPhai, string ChiemLinhNgay, int CuaThanh_CuongHoa_Level)
	{
		return DBA.ExeSqlCommand($"UPDATE ThienMaThanCung_DanhSach SET Bang_Chiem_Thanh='{ChiemLinhMonPhai}',Ngay_Chiem_Thanh={ChiemLinhNgay},Cong_Thanh_CuongHoa_Level={CuaThanh_CuongHoa_Level},ThoiGian_LamMoi_CongThanh='{DateTime.Now.ToString()}'");
	}

	public static string DatDuoc_MonPhai_LienMinh_MinhChu(string 门派名)
	{
		var string_ = "select * from TBL_XWWL_Guild where G_Name=@name";
		var sqlParameter_ = new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, 门派名) };
		var dBToDataTable = DBA.GetDBToDataTable(string_, sqlParameter_);
		if (dBToDataTable == null)
		{
			return "";
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return "";
		}
		var result = dBToDataTable.Rows[0]["LienMinh_MinhChu"].ToString();
		dBToDataTable.Dispose();
		return result;
	}

	public static DataTable Load_ThongTinLienMinh()
	{
		var string_ = "select * from TBL_XWWL_Guild where LienMinh_MinhChu !='' and LienMinh_MinhChu=G_Name";
		var dBToDataTable = DBA.GetDBToDataTable(string_);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return null;
		}
		return dBToDataTable;
	}

	public static void BangChien_TienDatCuoc(string string_0, string string_1, int int_0, int int_1)
	{
		DBA.ExeSqlCommand($"INSERT  INTO  BangChien_TienDatCuoc  (UserId,UserName,BangPhaiID,NguyenBaoSoLuong)  VALUES  ('{string_0}','{string_1}',{int_0},{int_1})");
	}

	public static void BangChien_TienDatCuoc_XoaBo(string string_0, string string_1, int int_0, int int_1)
	{
		DBA.ExeSqlCommand($"DELETE  FROM  BangChien_TienDatCuoc  WHERE  UserId='{string_0}'  and  UserName='{string_1}'  and  UserName='{int_0}'");
		switch (int_1)
		{
		case -1:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  Thua=Thua+1  WHERE  ID='{int_0}'");
			break;
		case 0:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  Hoa=Hoa+1  WHERE  ID='{int_0}'");
			break;
		case 1:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  Thang=Thang+1  WHERE  ID='{int_0}'");
			break;
		}
	}

	public static int GuildTotalCompletedQuest(int int_0)
	{
		var string_ = $"SELECT  count(*)  FROM  TBL_GUILD_QUEST_PROGRESS  WHERE  GuildID={int_0}  AND  Status=3";
		var dT = DBA.GetDBToDataTable(string_);
		if (dT == null)
		{
			return 0;
		}
		if (dT.Rows.Count == 0)
		{
			dT.Dispose();
			return 0;
		}
		var result = dT.Rows.Count;
		dT.Dispose();
		return result;
	}

	public static int GuildTotalCompletedQuestInThisWeek(int int_0)
	{
		// Lấy ngày thứ Hai của tuần hiện tại
		var today = DateTime.Today;
		var dayDiff = today.DayOfWeek == DayOfWeek.Sunday ? 6 : (int)today.DayOfWeek - 1;
		var monday = today.AddDays(-dayDiff);
		
		// Lấy ngày Chủ Nhật (cuối tuần)
		var sunday = monday.AddDays(6);
		
		// Tạo câu truy vấn SQL
		var string_ = $"SELECT COUNT(*) FROM TBL_GUILD_QUEST_PROGRESS WHERE GuildID={int_0} AND Status=3 AND CompletedTime >= @Monday AND CompletedTime < @SundayNextDay";
		
		// Sử dụng tham số để tránh SQL injection
		
		var parameters =new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Monday", SqlDbType.DateTime, 0, monday),
			SqlDBA.MakeInParam("@SundayNextDay", SqlDbType.DateTime, 0, sunday.AddDays(1))
		};
		
		var dT = DBA.GetDBToDataTable(string_, parameters);
		if (dT == null || dT.Rows.Count == 0)
		{
			dT?.Dispose();
			return 0;
		}
		
		var result = Convert.ToInt32(dT.Rows[0][0]);
		dT.Dispose();
		return result;
	}


	public static void ApplyForDoorBadge(int int_0, byte[] byte_0)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Guild  SET  MonHuy={1}  WHERE  ID='{0}'", int_0, Converter.ToString1(byte_0)));
	}

	public static byte[] GetTheDoorBadge(int int_0)
	{
		try
		{
			var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_Guild  WHERE  ID  =  {int_0}");
			if (dBToDataTable == null)
			{
				return null;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				dBToDataTable.Dispose();
				return null;
			}
			if (Buffer.IsEquals(dBToDataTable.Rows[0]["MonHuy"].GetType().ToString(), "System.DBNull"))
			{
				dBToDataTable.Dispose();
				return null;
			}
			var array = (byte[])dBToDataTable.Rows[0]["MonHuy"];
			if (array != null)
			{
				dBToDataTable.Dispose();
				return array;
			}
			dBToDataTable.Dispose();
			return null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DatDuocMonHuy error " + ex.Message);
			return null;
		}
	}

	public static void BangPhaiAssignAPosition(int int_0, string string_0)
	{
		DBA.ExeSqlCommand("UPDATE  TBL_XWWL_GuildMember  SET  Leve=@zw  WHERE  FLD_NAME=@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@zw", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_0)
		});
	}

	public static void AddGuildPoint(int point, string name)
	{
		DBA.ExeSqlCommand("UPDATE TBL_XWWL_GuildMember SET FLD_GuildPoint=FLD_GuildPoint+@point,  FLD_NewGuildPoint=FLD_NewGuildPoint+@point  WHERE FLD_NAME=@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@point", SqlDbType.Int, 0, point),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, name)
		});
	}

	public static void Add_Point_Event(int point, string name)
	{
		DBA.ExeSqlCommand("UPDATE TBL_Open_Box_Event SET STT_Hop_Event=STT_Hop_Event+@point, Usage_Count=Usage_Count+1 WHERE FLD_NAME=@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@point", SqlDbType.Int, 0, point),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, name)
		});
	}

	public static void ChuyenBangChu_ChucVi(string string_0, string string_1, string string_2)
	{
		var string_3 = "UPDATE  TBL_XWWL_GuildMember  SET  Leve=@zw  WHERE  FLD_NAME=@Username";
		var sqlParameter_ = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@zw", SqlDbType.Int, 0, 5),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1)
		};
		var string_4 = "UPDATE  TBL_XWWL_Guild  SET  G_Master=@Uname  WHERE  G_Name=@Gname";
		var sqlParameter_2 = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Uname", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@Gname", SqlDbType.VarChar, 30, string_2)
		};
		DBA.ExeSqlCommand(string_3, sqlParameter_);
		DBA.ExeSqlCommand(string_4, sqlParameter_2);
	}

	public static int TaoMoi_BangPhai_XacNhan(string string_0)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_SELECT_Guild_DATA  @bpnamea", new SqlParameter[1] { SqlDBA.MakeInParam("@bpnamea", SqlDbType.VarChar, 30, string_0) });
	}

	public static int TaoMoi_BangPhai(string string_0, string string_1, int int_0)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_INT_Guild_DATA_New  @name,  @bpname,@leve", new SqlParameter[3]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_0)
		});
	}

	public static int GiaNhapBangPhai(string string_0, string string_1, int int_0)
	{
		return (int)DBA.GetDBValue_3(string.Format("EXEC  XWWL_JR_Guild_DATA_New  @name,  @bpname,@leve", string_0, string_1, int_0), new SqlParameter[3]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_0)
		});
	}

	public static int RoiKhoiBangPhai(string string_0)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_Out_Guild_DATA  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
	}

	public static int TrucXuatBangPhai(string string_0, string string_1)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_OutBz_Guild_DATA  @name,  @bpname", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_1)
		});
	}

	public static int GetUserName(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  FLD_NAME  FROM  TBL_XWWL_Char  WHERE  FLD_NAME=@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return 1;
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static DataTable DatDuocBangPhai_SoLuongMember(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_GuildMember  WHERE  G_Name  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static DataTable BangPhai_Mem_Guild_Point(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_GuildMember  WHERE  FLD_GuildPoint  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static DataTable DatDuocBangPhaiSoLieu(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Guild  WHERE  G_Name  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static DataTable DatDuoc_MonChienBangPhaiSoLieu(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_GuildPVP  WHERE  BangPhai  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static DataTable GetUserNameBp(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("EXEC  XWWL_LOAD_Guild  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static DataTable GetUserWarehouse(string string_0, string string_1)
	{
		var dBToDataTable = DBA.GetDBToDataTable("select  *  from    [TBL_XWWL_Warehouse]    where  FLD_ID=@Userid  and  FLD_NAME  =@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		DBA.ExeSqlCommand("EXEC  XWWL_CREATE_USER_BANK  @Userid,@Username,@aa,@zb", new SqlParameter[4]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@aa", SqlDbType.Int, 0, 0),
			SqlDBA.MakeInParam("@zb", SqlDbType.VarBinary, World.Item_Db_Byte_Length * 60, new byte[World.Item_Db_Byte_Length * 60])
		});
		var dBToDataTable2 = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_Warehouse]  where  FLD_ID=@Userid  and  FLD_NAME  =@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1)
		});
		if (dBToDataTable2 == null)
		{
			return null;
		}
		if (dBToDataTable2.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable2;
	}

	public static DataTable GetUserPublicWarehouse(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_PublicWarehouse]  where  FLD_ID=@Userid", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		Converter.ToString1(new byte[World.Item_Db_Byte_Length * 60]);
		Converter.ToString1(new byte[60]);
		DBA.ExeSqlCommand("EXEC  XWWL_CREATE_ID_BANK      @Userid,@aaa,@ck,@ck1", new SqlParameter[4]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@aaa", SqlDbType.Int, 0, 0),
			SqlDBA.MakeInParam("@ck", SqlDbType.VarBinary, World.Item_Db_Byte_Length * 60, new byte[World.Item_Db_Byte_Length * 60]),
			SqlDBA.MakeInParam("@ck1", SqlDbType.VarBinary, 50, new byte[50])
		});
		var dBToDataTable2 = DBA.GetDBToDataTable($"select  *  from  [TBL_XWWL_PublicWarehouse]  where  FLD_ID='{string_0}'", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable2 == null)
		{
			return null;
		}
		if (dBToDataTable2.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable2;
	}

	public static string GenerateGiftcode()
	{
		var text = "";
		Random random = new();
		for (var i = 1; i <= 10; i++)
		{
			var num = random.Next(1, 3);
			var text2 = "";
			if (num == 1)
			{
				text2 = Convert.ToString(char.ToUpper((char)random.Next(65, 90)));
			}
			if (num == 2)
			{
				text2 = Convert.ToString(random.Next(1, 10));
			}
			text += text2;
		}
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT Giftcode FROM GIFTCODE WHERE Giftcode='{text}'");
		if (dBToDataTable.Rows.Count > 0)
		{
			return "";
		}
		return text;
	}

	public static string Tao_ra_giftcode(string Username, int Cash, int Bonus, string NoiDung)
	{
		try
		{
			var text = GenerateGiftcode();
			if (text == "")
			{
				Tao_ra_giftcode(Username, Cash, Bonus, NoiDung);
			}
			DBA.ExeSqlCommand($"INSERT INTO GIFTCODE (Username,Giftcode,Cash,Bonus,Noi_dung) VALUES ('{Username}','{text}',{Cash},{Bonus},'{NoiDung}')");
			return text;
		}
		catch
		{
			return "";
		}
	}

	public static string Tao_ra_giftcode_2(string Username, int Cash, string NoiDung)
	{
		try
		{
			var text = GenerateGiftcode();
			if (text == "")
			{
				Tao_ra_giftcode_2(Username, Cash, NoiDung);
			}
			DBA.ExeSqlCommand(string.Format("INSERT INTO TBL_ADDCASH (account,Cash,GhiChu) VALUES ('{0}','{1}','{2}')", new object[3] { Username, Cash, NoiDung }), "rxjhaccount");
			return text;
		}
		catch
		{
			return "";
		}
	}

	public static int[] Su_dung_giftcode(string Username, string PlayerUsed, string Giftcode, string MAC_Address)
	{
		var array = new int[2];
		try
		{
			var dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM GIFTCODE WHERE Username='{Username}' AND Giftcode='{Giftcode}'");
			if (dBToDataTable != null)
			{
				var num = Convert.ToInt32(dBToDataTable.Rows[0]["Da_su_dung"]);
				if (num == 1)
				{
					return null;
				}
				array.SetValue(Convert.ToInt32(dBToDataTable.Rows[0]["Cash"]), 0);
				array.SetValue(Convert.ToInt32(dBToDataTable.Rows[0]["Bonus"]), 1);
				var dateTime = Convert.ToDateTime(DateTime.Now.ToString());
				DBA.ExeSqlCommand(string.Format("UPDATE GIFTCODE SET Da_su_dung='1',Nguoi_su_dung='{1}' WHERE Giftcode='{0}'", Giftcode, PlayerUsed));
				return array;
			}
			return null;
		}
		catch
		{
			return null;
		}
	}

	public static string Md5(string strmm)
	{
		MD5 mD = new MD5CryptoServiceProvider();
		return BitConverter.ToString(mD.ComputeHash(Encoding.ASCII.GetBytes(strmm))).Replace("-", "").ToLower();
	}

	public static int SetUserName(string string_0, string string_1, int int_0, byte[] byte_0)
	{
		var array = new byte[76];
		var array2 = new byte[1095];
		var array3 = new byte[2736];
		var array4 = new byte[381];
		var bytes = BitConverter.GetBytes(CreateItemSeries());
		var src = new byte[4];
		var bytes2 = BitConverter.GetBytes(1);
		switch (int_0)
		{
		case 1:
			src = BitConverter.GetBytes(100200002);
			break;
		case 2:
			src = BitConverter.GetBytes(200200002);
			break;
		case 3:
			src = BitConverter.GetBytes(300200002);
			break;
		case 4:
			src = BitConverter.GetBytes(400200002);
			break;
		case 5:
			src = BitConverter.GetBytes(500200002);
			break;
		case 6:
			src = BitConverter.GetBytes(700200002);
			break;
		case 7:
			src = BitConverter.GetBytes(800200001);
			break;
		case 8:
			src = BitConverter.GetBytes(100204001);
			break;
		case 9:
			src = BitConverter.GetBytes(200204001);
			break;
		case 10:
			src = BitConverter.GetBytes(900200001);
			break;
		case 11:
			src = BitConverter.GetBytes(400204001);
			break;
		case 12:
			src = BitConverter.GetBytes(300204001);
			break;
		case 13:
			src = BitConverter.GetBytes(500204001);
			break;
		}
		System.Buffer.BlockCopy(bytes, 0, array, 0, 4);
		System.Buffer.BlockCopy(src, 0, array, 8, 4);
		System.Buffer.BlockCopy(bytes2, 0, array, 12, 4);
		System.Buffer.BlockCopy(array, 0, array3, 0, 76);
		var num = 0;
		var dBToDataTable = DBA.GetDBToDataTable("Select FLD_INDEX FROM TBL_XWWL_Char Where FLD_ID=@FLD_ID", new SqlParameter[1] { SqlDBA.MakeInParam("@FLD_ID", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable.Rows.Count >= 4)
		{
			dBToDataTable.Dispose();
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			num = 0;
		}
		else
		{
			List<int> list = new();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				var item = (int)dBToDataTable.Rows[i]["FLD_INDEX"];
				list.Add(item);
			}
			for (var j = 0; j < 4; j++)
			{
				if (!list.Contains(j))
				{
					num = j;
					break;
				}
			}
		}
		dBToDataTable.Dispose();
		var num2 = 0;
		var num3 = 0;
		switch (int_0)
		{
		case 4:
			num2 = 124;
			num3 = 116;
			break;
		case 6:
			num2 = 130;
			num3 = 114;
			break;
		case 7:
			num2 = 124;
			num3 = 136;
			break;
		case 1:
		case 8:
			num2 = 145;
			num3 = 116;
			break;
		case 10:
			num2 = 145;
			num3 = 116;
			break;
		case 11:
			num2 = 124;
			num3 = 116;
			break;
		case 2:
		case 3:
		case 5:
		case 9:
		case 12:
			num2 = 133;
			num3 = 118;
			break;
		case 13:
			num2 = 118;
			num3 = 136;
			break;
		}
		if (DBA.ExeSqlCommand("EXEC XWWL_INT_USER_DATA @FLD_ID,@name,@rwid,@zy,@hp,@mp,@coue,@xrwhex,@xrwhex2", new SqlParameter[9]
		{
			SqlDBA.MakeInParam("@FLD_ID", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@rwid", SqlDbType.Int, 0, num),
			SqlDBA.MakeInParam("@zy", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@hp", SqlDbType.Int, 0, num2),
			SqlDBA.MakeInParam("@mp", SqlDbType.Int, 0, num3),
			SqlDBA.MakeInParam("@coue", SqlDbType.VarBinary, 10, byte_0),
			SqlDBA.MakeInParam("@xrwhex", SqlDbType.VarBinary, array2.Length, array2),
			SqlDBA.MakeInParam("@xrwhex2", SqlDbType.VarBinary, array3.Length, array3)
		}) == -1)
		{
			return -1;
		}
		return 1;
	}

	public static int GetCwUserName(string string_0, string string_1, int int_0, long long_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  Name  FROM  TBL_XWWL_Cw  WHERE  Name=@name", string_0), new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			if (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_Cw_DATA  @zrname,@name,@id,@type,@zb1,@zb2", string_1, string_0, long_0, int_0, Converter.ToString(new byte[292]), Converter.ToString(new byte[1168])), new SqlParameter[6]
			{
				SqlDBA.MakeInParam("@zrname", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@id", SqlDbType.Int, 0, long_0),
				SqlDBA.MakeInParam("@type", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@zb1", SqlDbType.VarBinary, 5 * World.Item_Db_Byte_Length, new byte[5 * World.Item_Db_Byte_Length]),
				SqlDBA.MakeInParam("@zb2", SqlDbType.VarBinary, 16 * World.Item_Db_Byte_Length, new byte[16 * World.Item_Db_Byte_Length])
			}) != -1)
			{
				return 1;
			}
			return -1;
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static void DoiMoiBangPhai_VinhDu(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		try
		{
			var prams = new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
				SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
			};
			DbPoolClass obj = new()
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = "UPDATE_menpai_DATA_New"
			};
			World.SqlPool.Enqueue(obj);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "SaveBangPhai xếp hạng SoLieu error " + ex.Message);
		}
	}

	public static int Set_NguoiVinhDu_SoLieu(int int_0, string string_0, int int_1, int int_2, int int_3, string string_1, string string_2, int int_4)
	{
		var dataTable = DatDuocBangPhaiSoLieu(string_1);
		if (dataTable != null)
		{
			if (dataTable.Rows.Count > 0)
			{
				string_2 = dataTable.Rows[0]["G_Master"].ToString();
			}
			dataTable.Dispose();
		}
		var dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  FLD_TenNhanVat  FROM  TBL_VinhDuHeThong  WHERE  FLD_TenNhanVat=@name  and  FLD_TYPE=@lx", string_0, int_0), new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, int_0)
		});
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			if (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_RY_DATA  @lx,@name,@job,@level,@zx,@bpname,@mzname,@jf", int_0, string_0, int_1, int_2, int_3, string_1, string_2, int_4), new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_3),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@mzname", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@jf", SqlDbType.Int, 0, int_4)
			}) != -1)
			{
				return 1;
			}
			return -1;
		}
		dBToDataTable.Dispose();
		if (DBA.ExeSqlCommand($"UPDATE  TBL_VinhDuHeThong  SET  FLD_DiemSo  =FLD_DiemSo+{int_4}  WHERE  FLD_TenNhanVat='{string_0}'  and    FLD_TYPE={int_0}", "GameServer") != -1)
		{
			return 1;
		}
		return -1;
	}

	public static int SetBangPhaiVinhDuSoLieu(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3)
	{
		var dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  *  FROM  TBL_VinhDuHeThong  WHERE  FLD_TYPE  =  3  and  FLD_BangPhai=@mpname", string_0), new SqlParameter[1] { SqlDBA.MakeInParam("@mpname", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			var empty = string.Empty;
			if (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_RY_DATA  @lx,@name,@job,@level,@zx,@bpname,@mzname,@jf", 3, empty, int_1, int_0, int_2, string_0, string_1, int_3), new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, 3),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, empty),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@mzname", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@jf", SqlDbType.Int, 0, int_3)
			}) != -1)
			{
				return 1;
			}
			return -1;
		}
		dBToDataTable.Dispose();
		if (DBA.ExeSqlCommand($"UPDATE  TBL_VinhDuHeThong  SET  FLD_DiemSo  =FLD_DiemSo+1  WHERE  FLD_BangPhai='{string_0}'  and  FLD_TYPE=      3", "GameServer") != -1)
		{
			return 1;
		}
		return -1;
	}

	public static DataTable DatDuocDanhSach_TruyenThu(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_TruyenThuHeThong  WHERE  NguoiNhanThu_NhatVatTen  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		dBToDataTable.Dispose();
		return null;
	}

	public static void SetTruyenThuDaXem(int int_0, int int_1)
	{
		DBA.ExeSqlCommand("UPDATE  TBL_TruyenThuHeThong  SET  DanhDauDaXem=@rd  WHERE  ID=@id", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@rd", SqlDbType.Int, 0, int_1),
			SqlDBA.MakeInParam("@id", SqlDbType.Int, 30, int_0)
		});
	}

	public static void ThayDoiHonNhan_TrangThai(string string_0, int int_0)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_MARITAL_STATUS={1}  WHERE  FLD_NAME='{0}'", string_0, int_0));
	}

	public static void LyHonTrangThai(string string_0)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_QlNAME='{1}',FLD_QlDu={2},FLD_LOVE_WORD='{3}',FLD_MARITAL_STATUS={4},FLD_MARRIED={5}  WHERE  FLD_NAME='{0}'", string_0, string.Empty, 0, string.Empty, 0, 0));
	}

	public static void CreateBiography(string string_0, string string_1, int int_0, string string_2, int int_1)
	{
		DBA.GetDBValue_3("EXEC  INT_CS_DATA_New  @fname,  @sname,  @msg,  @npcid,@type", new SqlParameter[5]
		{
			SqlDBA.MakeInParam("@fname", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@sname", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@msg", SqlDbType.VarChar, 2000, string_2),
			SqlDBA.MakeInParam("@npcid", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@type", SqlDbType.Int, 0, int_1)
		});
	}

	public static DataTable DatDuocTenNhanVatWord(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Char  WHERE  FLD_NAME  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static DataTable DatDuocNhanVatBangPhaiOnline(string string_0)
	{
		var format = "SELECT  *  FROM  TBL_XWWL_Char  WHERE  FLD_NAME  =  @name and FLD_ONLINE = 1";
		var dBToDataTable = DBA.GetDBToDataTable(string.Format(format), new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static void TaoMoi_BangPhaiVinhDu(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		DBA.ExeSqlCommand("EXEC  INT_menpai_DATA_New  @rwname,  @bpname,@zx,  @leve,@job,@jobleve,@rongyu,@fq", new SqlParameter[8]
		{
			SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
			SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
			SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
			SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
			SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
		});
	}

	public static DataTable DatDuocBangPhaiVinhDuSoLieu(string string_0, string string_1)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  VinhDuBangPhaiXepHang  WHERE  FLD_BP  =  @name  and  FLD_FQ=@fq", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, string_1)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static DataTable DatDuocMasterData(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_SuDoSoLieu  WHERE  FLD_TNAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static DataTable DatDuocApprenticeData(string string_0)
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_SuDoSoLieu  WHERE  FLD_SNAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static int TaoMoiQuanHeSuDo(string string_0, string string_1, int int_0, int int_1)
	{
		var dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  FLD_TNAME  FROM  TBL_SuDoSoLieu  WHERE  FLD_TNAME=@name", string_0), new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			if (DBA.ExeSqlCommand(string.Format("EXEC  INT_St_DATA  @sname,@tname,@tlevel,@index", string_1, string_0, int_0, int_1), new SqlParameter[4]
			{
				SqlDBA.MakeInParam("@sname", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@tname", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@tlevel", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@index", SqlDbType.Int, 0, int_1)
			}) != -1)
			{
				return 1;
			}
			return -1;
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static int GiaiTruQuanHeThayTro(string string_0, string string_1)
	{
		if (DBA.ExeSqlCommand($"delete  [TBL_SuDoSoLieu]  WHERE  FLD_TNAME  ='{string_0}'  and  FLD_SNAME='{string_1}'", "GameServer") != -1)
		{
			return 1;
		}
		return -1;
	}

	public static int LayDuocCongHienKinhNghiem(string string_0, string string_1)
	{
		var num = 0;
		try
		{
			var dBToDataTable = DBA.GetDBToDataTable($"SELECT  FLD_SuPhu  from  [TBL_XWWL_Char]  where  FLD_NAME  ='{string_1}'");
			if (dBToDataTable == null)
			{
				return num;
			}
			if (dBToDataTable.Rows.Count > 0)
			{
				var array = new byte[28];
				var array2 = (byte[])dBToDataTable.Rows[0]["FLD_SuPhu"];
				System.Buffer.BlockCopy(array2, 0, array, 0, array2.Length);
				var array3 = new byte[16];
				System.Buffer.BlockCopy(array, 0, array3, 0, 16);
				if (Encoding.Default.GetString(array3).Replace("\0", string.Empty).Trim() == string_0)
				{
					var num2 = BitConverter.ToInt32(array, 18);
					if (num2 <= 0)
					{
						dBToDataTable.Dispose();
						return 0;
					}
					num += num2;
					array[18] = 0;
					array[19] = 0;
					array[20] = 0;
					array[21] = 0;
					DBA.ExeSqlCommand("UPDATE  [TBL_XWWL_Char]  set  FLD_SuPhu=@sf  where  FLD_NAME  =@name", new SqlParameter[2]
					{
						SqlDBA.MakeInParam("@sf", SqlDbType.VarBinary, 28, array),
						SqlDBA.MakeInParam("@name", SqlDbType.VarChar, string_1.Length, string_1)
					});
				}
			}
			dBToDataTable.Dispose();
			return num;
		}
		catch
		{
			return 0;
		}
	}

	public static void BackupDatabase(string Database, string Disk)
	{
		try
		{
			var string_ = "BACKUP DATABASE @Database TO DISK= @Disk";
			var sqlParameter_ = new SqlParameter[2]
			{
				SqlDBA.MakeInParam("@Database", SqlDbType.VarChar, 30, Database),
				SqlDBA.MakeInParam("@Disk", SqlDbType.VarChar, 100, Disk)
			};
			DBA.ExeSqlCommand(string_, sqlParameter_);
		}
		catch
		{
		}
	}
}
