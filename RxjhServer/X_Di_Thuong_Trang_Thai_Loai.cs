using System;
using System.Collections.Generic;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Di_Thuong_Trang_Thai_Loai : IDisposable
{
	public System.Timers.Timer npcyd;

	public System.Timers.Timer yczt;

	public double ycztsl;

	public DateTime time;

	public Players targetPlayer;

	public Players attackerPlayer;

	public NpcClass Npc;

	public int NpcPlayId;

	private int _FLD_PID;

	private double _FLD_NUM;

	public int TonThuong;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public double FLD_NUM
	{
		get
		{
			return _FLD_NUM;
		}
		set
		{
			_FLD_NUM = value;
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-Dispose");
		}
		if (npcyd != null)
		{
			npcyd.Enabled = false;
			npcyd.Close();
			npcyd.Dispose();
			npcyd = null;
		}
		if (yczt != null)
		{
			yczt.Enabled = false;
			yczt.Close();
			yczt.Dispose();
			yczt = null;
		}
		targetPlayer = null;
		Npc = null;
	}

	public X_Di_Thuong_Trang_Thai_Loai(Players Play_, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-NEW");
		}
		FLD_PID = DiThuong_ID;
		FLD_NUM = DiThuong_SoLuong;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		targetPlayer = Play_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
	}

	public void TrangThai_BatThuong_LoaiChayMau(double ycztsll)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass - TrangThai_BatThuong_LoaiChayMau");
		}
		ycztsl = ycztsll;
		yczt = new(1000.0);
		yczt.Elapsed += yczt_Elapsed;
		yczt.Enabled = true;
		yczt.AutoReset = true;
	}

	public void ThoiGianKetThucVanDe_ThucDayLuuThongMauHuyet()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass - ThoiGianKetThucSuKien");
		}
		try
		{
			if (targetPlayer != null)
			{
				targetPlayer.TrangThai_BatThuong.Remove(19);
				StatusEffect(19, 0, 0, 0);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "TrangThai_BatThuongClass ThoiGianKetThucSuKien error 33 ：[" + FLD_PID + "]" + ex);
		}
		finally
		{
			Dispose();
		}
	}

	public void ThoiGianKetThuc_NgocPhongTuong()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass - ThoiGianKetThucSuKien");
		}
		try
		{
			if (targetPlayer != null)
			{
				targetPlayer.TrangThai_BatThuong.Remove(20);
				StatusEffect(20, 0, 0, 0);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "TrangThai_BatThuongClass ThoiGianKetThucSuKien error 44 ：[" + FLD_PID + "]" + ex);
		}
		finally
		{
			Dispose();
		}
	}

	public void yczt_Elapsed(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "yczt_Elapsed");
		}
		if (targetPlayer != null)
		{
			if (targetPlayer.PlayerTuVong || targetPlayer.Exiting || targetPlayer.GiaoDich.GiaoDichBenTrong || targetPlayer.OpenWarehouse || targetPlayer.InTheShop)
			{
				return;
			}
			//targetPlayer.NhanVat_HP -= (int)ycztsl;
			var die = PlayerEvents.OnPlayerHpChanged(targetPlayer, (int)ycztsl,attackerPlayer,PlayerEvents.HpChangeType.Bleeding,null, lethal:true);
			if (die)
			{
				if (yczt != null)
				{
					yczt.Enabled = false;
					yczt.Close();
					yczt.Dispose();
					yczt = null;
				}
			}
		}
		else
		{
			if (Npc == null)
			{
				return;
			}
			Npc.UpdateHP('-', (int)ycztsl,targetPlayer.SessionID,targetPlayer.UserName,targetPlayer.Player_Level,targetPlayer.GuildName,targetPlayer.GuildId);
			if (Npc.Rxjh_HP <= 0)
			{
				Npc.GuiDiTuVongSoLieuWrapper(NpcPlayId);
				if (yczt != null)
				{
					yczt.Enabled = false;
					yczt.Close();
					yczt.Dispose();
					yczt = null;
				}
			}
		}
	}

	public X_Di_Thuong_Trang_Thai_Loai(NpcClass Play_, int _NpcPlayId, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong)
	{
		NpcPlayId = _NpcPlayId;
		FLD_PID = DiThuong_ID;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Npc = Play_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		FLD_NUM = DiThuong_SoLuong;
		StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
	}

	public X_Di_Thuong_Trang_Thai_Loai(NpcClass Play_, int _NpcPlayId, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong, Players _Playe)
	{
		NpcPlayId = _NpcPlayId;
		FLD_PID = DiThuong_ID;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Npc = Play_;
		npcyd = new(ThoiGian);
		npcyd.Elapsed += ThoiGianKetThucSuKien1;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		FLD_NUM = DiThuong_SoLuong;
		attackerPlayer = _Playe;
		StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "ThoiGianKetThucSuKien1");
		}
		ThoiGianKetThucSuKien();
	}

	public void ThoiGianKetThucSuKien()
	{
		var num = 0;
		try
		{
			if (npcyd != null)
			{
				num = 1;
				npcyd.Enabled = false;
				num = 2;
				npcyd.Close();
				num = 3;
				npcyd.Dispose();
				num = 4;
				npcyd = null;
			}
			num = 5;
			if (Npc != null)
			{
				switch (FLD_PID)
				{
				case 9:
					Npc.FLD_HieuUngGiamDef_Ninja = 0;
					break;
				case 44:
				{
					if (attackerPlayer == null)
					{
						break;
					}
					Random random = new();
					num = 51;
					var list = Npc.DanhNhieuMucTieu_TraTimPhamVi_Npc2_200MET_GAYDAME(attackerPlayer, 4);
					if (list == null || list.Count <= 0)
					{
						break;
					}
					if (attackerPlayer.dame_gayboi_thannu_khidanhbom > 0)
					{
						var num2 = attackerPlayer.dame_gayboi_thannu_khidanhbom / 2;
						Npc.GuiDi_DameLenNguoi(num2, -1, Npc.NPC_SessionID);
						Npc.Rxjh_HP -= num2;
						if (Npc.Rxjh_HP <= 0 && !Npc.NPCDeath)
						{
							double tienBac = (uint)Npc.ThuDuocTien(attackerPlayer);
							double num3 = Npc.ThuDuocKinhNghiem();
							double lichLuyen = Npc.ThuDuocLichLuyen(attackerPlayer);
							if (attackerPlayer.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= attackerPlayer.TrungCapPhuHon_KyDuyen)
							{
								num3 *= 2.0;
								attackerPlayer.ShowBigPrint(attackerPlayer.SessionID, 403);
							}
							attackerPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(attackerPlayer, Npc, num3, lichLuyen, tienBac, 0.0);
							Npc.GuiDiTuVongSoLieuWrapper(attackerPlayer.SessionID);
						}
						foreach (var item in list)
						{
							var num4 = random.Next(num2 - 15, num2 + 15);
							item.GuiDi_DameLenNguoi(num4, -1, item.NPC_SessionID);
							item.Rxjh_HP -= num4;
							if (item != null && item.Rxjh_HP <= 0 && !item.NPCDeath)
							{
								double tienBac2 = (uint)item.ThuDuocTien(attackerPlayer);
								double num5 = item.ThuDuocKinhNghiem();
								double lichLuyen2 = item.ThuDuocLichLuyen(attackerPlayer);
								if (attackerPlayer.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= attackerPlayer.TrungCapPhuHon_KyDuyen)
								{
									num5 *= 2.0;
									attackerPlayer.ShowBigPrint(attackerPlayer.SessionID, 403);
								}
								attackerPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(attackerPlayer, item, num5, lichLuyen2, tienBac2, 0.0);
								item.GuiDiTuVongSoLieuWrapper(attackerPlayer.SessionID);
							}
						}
						attackerPlayer.dame_gayboi_thannu_laynhiembom = attackerPlayer.dame_gayboi_thannu_khidanhbom;
						attackerPlayer.dame_gayboi_thannu_khidanhbom = 0;
					}
					if (Npc.dame_gayboi_thannu_khidanhbom != 1)
					{
						break;
					}
					var num6 = attackerPlayer.dame_gayboi_thannu_laynhiembom / 4;
					Npc.GuiDi_DameLenNguoi(num6, -1, Npc.NPC_SessionID);
					Npc.Rxjh_HP -= num6;
					if (Npc.Rxjh_HP <= 0)
					{
						double tienBac3 = (uint)Npc.ThuDuocTien(attackerPlayer);
						double num7 = Npc.ThuDuocKinhNghiem();
						double lichLuyen3 = Npc.ThuDuocLichLuyen(attackerPlayer);
						if (attackerPlayer.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= attackerPlayer.TrungCapPhuHon_KyDuyen)
						{
							num7 *= 2.0;
							attackerPlayer.ShowBigPrint(attackerPlayer.SessionID, 403);
						}
						attackerPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(attackerPlayer, Npc, num7, lichLuyen3, tienBac3, 0.0);
						Npc.GuiDiTuVongSoLieuWrapper(attackerPlayer.SessionID);
					}
					foreach (var item2 in list)
					{
						var num8 = random.Next(num6 - 15, num6 + 15);
						item2.GuiDi_DameLenNguoi(num8, -1, item2.NPC_SessionID);
						item2.Rxjh_HP -= num8;
						if (item2.Rxjh_HP <= 0)
						{
							double tienBac4 = (uint)item2.ThuDuocTien(attackerPlayer);
							double num9 = item2.ThuDuocKinhNghiem();
							double lichLuyen4 = item2.ThuDuocLichLuyen(attackerPlayer);
							if (attackerPlayer.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= attackerPlayer.TrungCapPhuHon_KyDuyen)
							{
								num9 *= 2.0;
								attackerPlayer.ShowBigPrint(attackerPlayer.SessionID, 403);
							}
							attackerPlayer.PhanPhoiKinhNghiemLichLuyenTienTai(attackerPlayer, item2, num9, lichLuyen4, tienBac4, 0.0);
							item2.GuiDiTuVongSoLieuWrapper(attackerPlayer.SessionID);
						}
					}
					Npc.dame_gayboi_thannu_khidanhbom = 0;
					attackerPlayer.dame_gayboi_thannu_laynhiembom = attackerPlayer.dame_gayboi_thannu_khidanhbom;
					break;
				}
				}
				if (Npc.TrangThai_BatThuong != null)
				{
					Npc.TrangThai_BatThuong.Remove(FLD_PID);
				}
				StatusEffect(FLD_PID, 0, 0, 0);
				Dispose();
			}
			else if (targetPlayer != null)
			{
				num = 11;
				switch (FLD_PID)
				{
				case 242:
					targetPlayer.FLD_NhanVat_ThemVao_CongKich -= 15;
					targetPlayer.FLD_NhanVat_ThemVao_PhongNgu -= 15;
					targetPlayer.CharactersToAddMax_HP -= 300;
					targetPlayer.CharactersToAddMax_MP -= 300;
					targetPlayer.FLD_NhanVat_ThemVao_KinhNghiem_KetHon = 0.0;
					targetPlayer.FLD_KetHonLeVat_ThemVaoThuocTinhThach = 0;
					targetPlayer.UpdateMartialArtsAndStatus();
					targetPlayer.CapNhat_HP_MP_SP();
					break;
				case 17:
					targetPlayer.NhanVatKhoa_Chat = false;
					break;
				case 1:
					targetPlayer.addFLD_ThemVaoTiLePhanTram_Attack(FLD_NUM * 0.01);
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 2:
					targetPlayer.addFLD_ThemVaoTiLePhanTram_PhongNgu(FLD_NUM * 0.01);
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 3:
					targetPlayer.Poisoning = false;
					break;
				case 4:
					targetPlayer.NhanVatKhoa_Chat = false;
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 8:
					targetPlayer.NhanVatKhoa_Chat = false;
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 23:
					targetPlayer.NhanVatKhoa_Chat = false;
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 24:
					targetPlayer.NhanVatKhoa_Chat = false;
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 9:
					targetPlayer.FLD_HieuUngGiamDef_PK = 0.0;
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 25:
					targetPlayer.addFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 13:
					targetPlayer.FLD_CamSuHieuUngGiamDame_PK = 0.0;
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 22:
					targetPlayer.addFLD_ThemVaoTiLePhanTram_Attack(0.05);
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 26:
					targetPlayer.NhanVatKhoa_Chat = false;
					targetPlayer.TrangThai_BatThuong.Remove(FLD_PID);
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 27:
					targetPlayer.NhanVatKhoa_Chat = false;
					targetPlayer.TrangThai_BatThuong.Remove(FLD_PID);
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				case 28:
					targetPlayer.NhanVatKhoa_Chat = false;
					targetPlayer.TrangThai_BatThuong.Remove(FLD_PID);
					targetPlayer.UpdateMartialArtsAndStatus();
					break;
				}
				num = 22;
				if (targetPlayer.TrangThai_BatThuong != null)
				{
					num = 23;
					targetPlayer.TrangThai_BatThuong.Remove(FLD_PID);
				}
				num = 24;
				targetPlayer.UpdateCharacterData(targetPlayer);
				StatusEffect(FLD_PID, 0, 0, 0);
				num = 25;
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Hiệu ứng debuff bị lỗi !! num:[" + num + "] - ID:[" + FLD_PID + "]" + ex);
		}
		finally
		{
			Dispose();
		}
	}

	public void TrangThaiHieuQua2(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-TrangThaiHieuQua");
		}
		var array = Converter.HexStringToByte("AA553E00250040153800000000002500000034000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
		if (targetPlayer != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, array, 4, 2);
			if (targetPlayer.Client != null)
			{
				targetPlayer.Client.Send_Map_Data(array, array.Length);
			}
			targetPlayer.SendCurrentRangeBroadcastData(array, array.Length);
		}
	}

	public void StatusEffect(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-TrangThaiHieuQua");
		}
		var array = Converter.HexStringToByte("AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 58, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 22, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 62, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(ThoiGian), 0, array, 38, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_SoLuong), 0, array, 42, 4);
		if (targetPlayer != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(targetPlayer.SessionID), 0, array, 4, 2);
			if (targetPlayer.Client != null)
			{
				targetPlayer.Client.SendMultiplePackage(array, array.Length);
			}
			targetPlayer.Send_Nhieu_Packet_PhamVi_HienTai(array, array.Length);
		}
		else if (Npc != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.NPC_SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.NPC_SessionID), 0, array, 4, 2);
			Npc.QuangBaSoLieu(array, array.Length);
		}
	}
}
