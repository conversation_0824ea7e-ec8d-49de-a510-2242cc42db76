﻿using System;

namespace RxjhServer;

public class DamageContribute
{
    public int WorldID;
    public int SessionID;

    public string PlayerName;
    public int AttackCount;
    private long _damage; 
    public long Damage
    {
    get => _damage < 0 ? 0 : _damage; // Ensure damage is never negative when accessed
    set => _damage = value < 0 ? 0 : value; // Ensure damage is never set to a negative value
    }


    public DamageContribute (int worldID, int sessionID,string playerName, long damage, int attackCount)
    {
        WorldID = worldID;
        SessionID = sessionID;
        AttackCount = attackCount;
        Damage = damage;
        PlayerName = playerName;
    }
}
