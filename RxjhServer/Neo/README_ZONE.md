# Hướng dẫn sử dụng Thiên cơ các Zone trong Yulgang

## Giới thiệu

Thiên cơ các Zone cho phép tách người chơi trong cùng một kênh (channel) thành các vùng khác nhau. Mỗi zone sẽ chỉ thấy được người chơi, NPC, và vật phẩm trong zone của mình. Zone 0 là zone đặc biệt (mặc định) có thể nhìn thấy tất cả các zone khác nhưng không thể tương tác.

### Tính năng chính

1. **Phân chia không gian**: Tách người chơi thành các nhóm riêng biệt
2. **Hỗ trợ boss bang hội**: <PERSON>hi một bang hội triệu hồi boss, chỉ thành viên bang hội mới có thể đánh
3. **Giới hạn không gian theo vùng**: Zone có thể giới hạn trong một phạm vi nhất định xung quanh boss
4. **Quy tắc tương tác**: 
   - Zone 0 (mặc định): Có thể nhìn thấy tất cả các zone khác nhưng không thể tương tác
   - Các zone khác: Chỉ nhìn thấy zone của mình và zone 0
5. **Vật phẩm trên mặt đất**: Vật phẩm rơi ra chỉ hiển thị cho người chơi của cùng zone hoặc zone 0

## Cài đặt

1. Thêm các file sau vào thư mục `RxjhServer/Neo/`:
   - `Zone.cs`: Định nghĩa class Zone
   - `ZoneManager.cs`: Quản lý các zone
   - `PlayerZoneExtensions.cs`: Mở rộng class Players với chức năng liên quan đến zone

2. Khởi tạo ZoneManager trong phương thức khởi động server:
```csharp
// Khởi tạo ZoneManager
ZoneManager.Instance.Initialize();
```

## Cách sử dụng

### 1. Tạo zone mới

```csharp
// Tạo zone thông thường
int zoneId = ZoneManager.Instance.CreateZone();

// Tạo zone cho bang hội
ZoneOwner guildOwner = new ZoneOwner
{
    PID = guildId,
    type = 1, // 1 = Guild
    name = guildId
};
int guildZoneId = ZoneManager.Instance.CreateZone(guildOwner);
```

### 2. Tạo zone đặc biệt hình tròn (giới hạn phạm vi)

```csharp
// Tạo zone hình tròn cho boss bang hội
int guildBossZoneId = ZoneManager.Instance.CreateGuildBossZone(
    guildId: 123,          // ID bang hội
    guildName: "Thiên Long", // Tên bang hội
    mapId: 101,            // ID bản đồ
    centerX: 500,          // Tọa độ X trung tâm
    centerY: 500,          // Tọa độ Y trung tâm
    radius: 200            // Bán kính
);
```

### 3. Triệu hồi boss bang hội vào zone

```csharp
// Triệu hồi boss bang hội
ZoneManager.Instance.SpawnGuildBoss(
    guildId: 123,          // ID bang hội
    guildName: "Thiên Long", // Tên bang hội
    mapId: 101,            // ID bản đồ
    x: 500,                // Tọa độ X
    y: 500,                // Tọa độ Y
    bossId: 15424,         // ID của boss
    durationMinutes: 30    // Thời gian tồn tại (phút)
);
```

### 4. Di chuyển người chơi giữa các zone

```csharp
// Di chuyển người chơi vào zone
ZoneManager.Instance.MovePlayerToZone(player, zoneId);

// Đưa người chơi về zone mặc định
ZoneManager.Instance.MovePlayerToZone(player, 0);
```

### 5. Kiểm tra khả năng tương tác

```csharp
// Kiểm tra xem người chơi có thể tấn công người chơi khác
bool canAttack = player.CanAttackPlayer(otherPlayer);

// Kiểm tra xem người chơi có thể tấn công NPC
bool canAttackNpc = player.CanAttackNpc(npc);

// Kiểm tra xem người chơi có thể nhặt vật phẩm
bool canPickup = player.CanPickupItem(item);
```

### 6. Quản lý vật phẩm trên mặt đất theo zone

```csharp
// Kiểm tra trước khi cho phép nhặt vật phẩm
if (!item.CanPlayerPickupItem(player))
{
    player.HeThongNhacNho("Bạn không thể nhặt vật phẩm này!");
    return;
}

// Khi tạo vật phẩm mới, nó sẽ tự động thêm vào zone của người chơi tạo ra nó
X_Mat_Dat_Vat_Pham_Loai item = new X_Mat_Dat_Vat_Pham_Loai(
    VatPham_byte_,
    player.NhanVatToaDo_X, player.NhanVatToaDo_Y, player.NhanVatToaDo_Z,
    player.NhanVatToaDo_BanDo,
    player, // Người chơi tạo ra vật phẩm - quyết định zone
    noiRotRa
);

// Nếu cần thay đổi zone của vật phẩm
item.SetZone(newZone);
```

## Tích hợp vào game

### 1. Hook vào sự kiện di chuyển người chơi

Thêm đoạn code sau vào phương thức xử lý di chuyển để kiểm tra khi người chơi đi vào hoặc ra khỏi khu vực zone:

```csharp
// Trong phương thức xử lý di chuyển
ZoneManager.Instance.CheckPlayerZonePosition(player);
```

### 2. Hook vào sự kiện tấn công

Thêm kiểm tra trước khi xử lý tấn công:

```csharp
// Kiểm tra tấn công người chơi
if (!attacker.CanAttackPlayer(defender))
{
    // Từ chối tấn công và thông báo
    attacker.HeThongNhacNho("Bạn không thể tấn công người chơi này");
    return;
}

// Kiểm tra tấn công NPC
if (!attacker.CanAttackNpc(npc))
{
    // Từ chối tấn công và thông báo
    attacker.HeThongNhacNho("Bạn không thể tấn công NPC này");
    return;
}
```

### 3. Hook vào sự kiện nhặt vật phẩm

Thêm kiểm tra trước khi cho phép nhặt vật phẩm:

```csharp
// Kiểm tra nhặt vật phẩm sử dụng extension method
if (!player.CanPickupItem(item))
{
    // Từ chối nhặt và thông báo
    player.HeThongNhacNho("Bạn không thể nhặt vật phẩm này");
    return;
}

// HOẶC sử dụng phương thức trực tiếp từ vật phẩm
if (!item.CanPlayerPickupItem(player))
{
    player.HeThongNhacNho("Bạn không thể nhặt vật phẩm này");
    return;
}
```

### 4. Sửa đổi xử lý tìm vật phẩm xung quanh

Khi hiển thị danh sách vật phẩm xung quanh người chơi, đảm bảo chỉ hiển thị những vật phẩm thuộc zone mà người chơi có thể nhìn thấy:

```csharp
// Code ví dụ khi quét vật phẩm xung quanh người chơi
List<X_Mat_Dat_Vat_Pham_Loai> visibleItems = new List<X_Mat_Dat_Vat_Pham_Loai>();

foreach (var item in World.ItmeTeM.Values)
{
    // Kiểm tra khoảng cách
    if (CalculateDistance(player.NhanVatToaDo_X, player.NhanVatToaDo_Y, item.Rxjh_X, item.Rxjh_Y) <= 400)
    {
        // Kiểm tra zone - chỉ hiển thị vật phẩm mà người chơi có thể nhìn thấy
        if (item.CanPlayerSeeItem(player))
        {
            visibleItems.Add(item);
        }
    }
}
```

## Lưu ý quan trọng

1. **Tạo zone boss đặc biệt**: Cần gọi `SpawnGuildBoss` thay vì tạo boss trực tiếp để đảm bảo boss được đặt trong zone đúng

2. **Cập nhật visibility**: Khi người chơi thay đổi zone, cần gọi `RefreshZoneVisibility` để cập nhật danh sách đối tượng có thể nhìn thấy

3. **Xử lý vật phẩm rơi**: Vật phẩm sẽ tự động được gán vào zone của người chơi tạo ra nó. Nếu không có người chơi tạo ra, sẽ được gán vào zone mặc định (zone 0)

4. **Liên kết Hooks**:
   - Hook sự kiện di chuyển của người chơi với `CheckPlayerZonePosition`
   - Hook sự kiện tấn công với `CanAttackPlayer` và `CanAttackNpc`
   - Hook sự kiện nhặt đồ với `CanPickupItem` hoặc `CanPlayerPickupItem`

5. **Xử lý zone tự động**: Zone của boss bang hội sẽ tự động bị xóa sau khi boss chết hoặc hết thời gian

## Ví dụ hoàn chỉnh: Triệu hồi Boss Bang Hội

```csharp
// Xử lý lệnh triệu hồi boss bang hội từ guild master
void ProcessSummonGuildBoss(Players player)
{
    // Kiểm tra nếu là bang chủ
    if (!IsGuildMaster(player))
    {
        player.HeThongNhacNho("Chỉ có bang chủ mới có thể triệu hồi Boss Bang Hội");
        return;
    }
    
    // Lấy thông tin bang hội
    int guildId = GetPlayerGuildId(player);
    string guildName = GetGuildName(guildId);
    
    // Kiểm tra điều kiện triệu hồi (ví dụ: đồng tiền bang hội)
    if (!CheckGuildCondition(guildId))
    {
        player.HeThongNhacNho("Bang hội không đủ điều kiện để triệu hồi boss");
        return;
    }
    
    // Triệu hồi boss
    ZoneManager.Instance.SpawnGuildBoss(
        guildId: guildId,
        guildName: guildName,
        mapId: player.NhanVatToaDo_BanDo,
        x: player.NhanVatToaDo_X,
        y: player.NhanVatToaDo_Y,
        bossId: 15424,     // ID boss
        durationMinutes: 30 // Thời gian sống
    );
    
    // Trừ tài nguyên bang hội
    DeductGuildResource(guildId);
}
```

## Gỡ lỗi và giám sát

- Để theo dõi các zone hiện có: `ZoneManager.Instance.GetAllZones()`
- Để tìm người chơi theo tên: `ZoneManager.Instance.FindPlayerByName(userName)`
- Để xóa zone thủ công: `ZoneManager.Instance.RemoveZone(zoneId)` 