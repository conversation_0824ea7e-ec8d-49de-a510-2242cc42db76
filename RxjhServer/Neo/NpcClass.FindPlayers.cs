using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer;

namespace RxjhServer
{
    public partial class NpcClass
    {
        /// <summary>
        /// Kiểm tra xem một người chơi có ở trong phạm vi và có thể nhìn thấy theo quy tắc Zone
        /// </summary>
        /// <param name="far">K<PERSON>ảng cách tối đa</param>
        /// <param name="player">Người chơi cần kiểm tra</param>
        /// <returns>true nếu người chơi trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public bool FindPlayers(int far, Players player)
        {
            try
            {
                // Kiểm tra cơ bản về bản đồ
                if (player.NhanVatToaDo_BanDo != this.Rxjh_Map)
                {
                    return false;
                }
                
                // Kiểm tra khoảng cách
                var num = player.NhanVatToaDo_X - this.Rxjh_X;
                var num2 = player.NhanVatToaDo_Y - this.Rxjh_Y;
                bool isInRange = (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far;
                
                if (!isInRange)
                {
                    return false;
                }
                
                // Nếu NPC không có zone, mặc định cho phép nhìn thấy
                if (this.CurrentZone == null)
                {
                    return true;
                }
                
                // Nếu người chơi không có zone, mặc định cho phép nhìn thấy
                if (player.CurrentZone == null)
                {
                    return true;
                }
                
                // Kiểm tra theo quy tắc zone
                return this.CurrentZone.CanSeeZone(player.CurrentZone);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"NpcClass.FindPlayersZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Lấy danh sách người chơi xung quanh theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <returns>Danh sách người chơi trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public List<Players> GetPlayersInRangeZone(int far)
        {
            List<Players> result = new List<Players>();
            try
            {
                foreach (var player in World.allConnectedChars.Values)
                {
                    if (FindPlayers(far, player))
                    {
                        result.Add(player);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"NpcClass.GetPlayersInRangeZone error: {ex.Message}");
            }
            return result;
        }
        
        /// <summary>
        /// Kiểm tra xem NPC có thể tương tác với người chơi theo quy tắc Zone hay không
        /// </summary>
        /// <param name="player">Người chơi cần kiểm tra</param>
        /// <returns>true nếu NPC có thể tương tác với người chơi</returns>
        public bool CanInteractWithPlayerZone(Players player)
        {
            try
            {
                // Nếu NPC không có zone hoặc người chơi không có zone, mặc định cho phép tương tác
                if (this.CurrentZone == null || player.CurrentZone == null)
                {
                    return true;
                }
                
                // Kiểm tra theo quy tắc zone
                return this.CurrentZone.CanSeeZone(player.CurrentZone);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"NpcClass.CanInteractWithPlayerZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Kiểm tra xem NPC có thể nhìn thấy người chơi theo quy tắc Zone hay không
        /// </summary>
        /// <param name="player">Người chơi cần kiểm tra</param>
        /// <returns>true nếu NPC có thể nhìn thấy người chơi</returns>
        public bool CanSeePlayerZone(Players player)
        {
            try
            {
                // Nếu NPC không có zone hoặc người chơi không có zone, mặc định cho phép nhìn thấy
                if (this.CurrentZone == null || player.CurrentZone == null)
                {
                    return true;
                }
                
                // Kiểm tra theo quy tắc zone
                return this.CurrentZone.CanSeeZone(player.CurrentZone);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"NpcClass.CanSeePlayerZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Kiểm tra xem NPC có thể tấn công người chơi theo quy tắc Zone hay không
        /// </summary>
        /// <param name="player">Người chơi cần kiểm tra</param>
        /// <returns>true nếu NPC có thể tấn công người chơi</returns>
        public bool CanAttackPlayerZone(Players player)
        {
            try
            {
                // Nếu NPC không có zone hoặc người chơi không có zone, mặc định cho phép tấn công
                if (this.CurrentZone == null || player.CurrentZone == null)
                {
                    return true;
                }
                
                // Kiểm tra theo quy tắc zone
                return this.CurrentZone.CanSeeZone(player.CurrentZone);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"NpcClass.CanAttackPlayerZone error: {ex.Message}");
                return false;
            }
        }
    }
} 