using System;
using System.Collections.Generic;

namespace RxjhServer
{
     public partial class Players
    {
        private bool isSortAscending = true;
        private bool isSortAscending1 = true;
        private bool isSortAscending2 = true;
        private void RearrangeItem(byte[] array)
        {
            var bagType = array[10];
            switch (bagType)
            {
                case 0:
                    // TUi do
                    SortItems(Item_In_Bag, ref isSortAscending);
                    Init_Item_In_Bag();
                    break;
                case 1:
                    SortItems(PersonalWarehouse, ref isSortAscending1);
                    OpenPersonalWarehouse();
                    // Kho rieng
                    break;
                case 2:
                    SortItems(PublicWarehouse, ref isSortAscending2);
                    OpenTheComprehensiveWarehouse();
                    // <PERSON><PERSON>
                    break;
            }
        }
 
        private static readonly Comparer<X_Vat_Pham_Loai> AscendingComparer = Comparer<X_Vat_Pham_Loai>.Create((x, y) => {
            // Kiểm tra slot trống
            if (x.GetVatPham_ID == 0 && y.GetVatPham_ID != 0)
                return 1; // Di chuyển x xuống cuối nếu x trống
            if (x.GetVatPham_ID != 0 && y.GetVatPham_ID == 0)
                return -1; // Giữ y dưới cùng nếu y trống

            var result = x.FLD_RESIDE1.CompareTo(y.FLD_RESIDE1);
            if (result == 0)
            {
                result = x.FLD_RESIDE2.CompareTo(y.FLD_RESIDE2);
                if (result == 0)
                {
                    result = x.GetVatPham_ID.CompareTo(y.GetVatPham_ID); 
                }
            }
            return result;
        });

        private static readonly Comparer<X_Vat_Pham_Loai> DescendingComparer = Comparer<X_Vat_Pham_Loai>.Create((x, y) => {
            // Kiểm tra slot trống - luôn giữ chỗ trống ở cuối
            if (x.GetVatPham_ID == 0 && y.GetVatPham_ID != 0)
                return 1;
            if (x.GetVatPham_ID != 0 && y.GetVatPham_ID == 0)
                return -1;

            // Ngược lại so với AscendingComparer
            var result = y.FLD_RESIDE1.CompareTo(x.FLD_RESIDE1);
            if (result == 0)
            {
                result = y.FLD_RESIDE2.CompareTo(x.FLD_RESIDE2);
                if (result == 0)
                {
                    result = y.GetVatPham_ID.CompareTo(x.GetVatPham_ID); 
                }
            }
            return result;
        });

        private void SortItems(X_Vat_Pham_Loai[] items, ref bool isSortAscending)
        {
            // Đảo trạng thái sắp xếp
            isSortAscending = !isSortAscending;
            
            // Sử dụng comparer phù hợp với hướng sắp xếp
            Array.Sort(items, isSortAscending ? AscendingComparer : DescendingComparer);
        }
    }
}
