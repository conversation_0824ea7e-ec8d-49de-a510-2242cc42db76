using System;
using RxjhServer.Network;

namespace RxjhServer
{
    /// <summary>
    /// Lớp mở rộng kết nối Players với MarketPlace
    /// </summary>
    public partial class Players
    {
        private MarketPlace _marketPlace;

        /// <summary>
        /// Hệ thống chợ
        /// </summary>
        public MarketPlace MarketPlaceSystem => _marketPlace ??= new MarketPlace(this);

        #region MarketPlace Forwarding Methods

        /// <summary>
        /// Xử lý các yêu cầu từ chợ
        /// </summary>
        public void MarketPlace(byte[] data)
        {
            MarketPlaceSystem.MarketPlaceRequest(data);
        }

        /// <summary>
        /// Nhận tiền từ vật phẩm đã bán
        /// </summary>
        public void MarketPlace_Take_Profit(byte[] data, int offset)
        {
            MarketPlaceSystem.MarketPlace_Take_Profit(data, offset);
        }

        /// <summary>
        /// Hủy bán vật phẩm
        /// </summary>
        public void MarketPlace_Cancel_Selling(byte[] data, int offset)
        {
            MarketPlaceSystem.MarketPlace_Cancel_Selling(data, offset);
        }

        /// <summary>
        /// Mua vật phẩm
        /// </summary>
        public void MarKetPlace_Buy_Item(byte[] data, int offset)
        {
            MarketPlaceSystem.MarKetPlace_Buy_Item(data, offset);
        }

        /// <summary>
        /// Tạo chi tiết đơn hàng
        /// </summary>
        public int MarketPlace_CreateOrderDetail(Players player, int id, int amount, long price)
        {
            return MarketPlaceSystem.MarketPlace_CreateOrderDetail(player, id, amount, price);
        }

        /// <summary>
        /// Cập nhật trạng thái đơn hàng
        /// </summary>
        public bool MarketPlace_Update_Order(int orderId, string status)
        {
            return MarketPlaceSystem.MarketPlace_Update_Order(orderId, status);
        }

        /// <summary>
        /// Gửi phản hồi
        /// </summary>
        public void MarketPlace_Response(int type, int success)
        {
            MarketPlaceSystem.MarketPlace_Response(type, success);
        }

        /// <summary>
        /// Danh sách vật phẩm đã bán
        /// </summary>
        public void MarketPlace_List_Items_Sold(Players player, int offset)
        {
            MarketPlaceSystem.MarketPlace_List_Items_Sold(player, offset);
        }

        /// <summary>
        /// Ghi thông tin một vật phẩm
        /// </summary>
        public void MarketPlace_Write_One_Item(SendingClass packetDataClass, System.Data.DataRow row, string showPrice)
        {
            MarketPlaceSystem.MarketPlace_Write_One_Item(packetDataClass, row, showPrice);
        }

        /// <summary>
        /// Danh sách vật phẩm
        /// </summary>
        public void MarketPlace_List_Item(byte[] data, int offset)
        {
            MarketPlaceSystem.MarketPlace_List_Item(data, offset);
        }

        /// <summary>
        /// Đăng ký vật phẩm để bán
        /// </summary>
        public void MarketPlace_Register_Item(byte[] data, int offset)
        {
            MarketPlaceSystem.MarketPlace_Register_Item(data, offset);
        }

        /// <summary>
        /// Cập nhật tiền tệ
        /// </summary>
        public void MarketPlace_Currency_Update(long price, CurrencyOperation operation)
        {
            MarketPlaceSystem.MarketPlace_Currency_Update(price, operation);
        }

        /// <summary>
        /// Lịch sử giao dịch
        /// </summary>
        public void MarketPlace_History(Players player, byte[] data, int offset)
        {
            MarketPlaceSystem.MarketPlace_History(player, data, offset);
        }

        /// <summary>
        /// Cập nhật trạng thái vật phẩm theo ID
        /// </summary>
        public bool MarketPlace_Update_Status(int productId, string status)
        {
            return MarketPlaceSystem.MarketPlace_Update_Status(productId, status);
        }

        /// <summary>
        /// Cập nhật trạng thái vật phẩm theo mã sản phẩm
        /// </summary>
        public bool MarketPlace_Update_Status(string productCode, string status)
        {
            return MarketPlaceSystem.MarketPlace_Update_Status(productCode, status);
        }

        #endregion
    }
}