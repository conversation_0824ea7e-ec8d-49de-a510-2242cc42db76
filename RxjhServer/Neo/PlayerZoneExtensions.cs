using RxjhServer.ManageZone;
using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer
{
    public static class PlayerZoneExtensions
    {
        // Cập nhật visibility của người chơi dựa trên zone
        public static void RefreshZoneVisibility(this Players player)
        {
            if (player == null || player.CurrentZone == null)
                return;
                
            // Lấy ZoneManager
            var zoneManager = ZoneManager.Instance;
            
            // Lấy tất cả zone mà người chơi có thể nhìn thấy
            var allZones = zoneManager.GetAllZones();
            var visibleZones = allZones.Where(z => player.CurrentZone.CanSeeZone(z)).ToArray();
            
            // Cập nhật người chơi nhìn thấy
            UpdateVisiblePlayers(player, visibleZones);
            
            // Cập nhật NPC nhìn thấy
            UpdateVisibleNpcs(player, visibleZones);
            
            // Cập nhật vật phẩm nhìn thấy
            UpdateVisibleItems(player, visibleZones);
        }
        
        // Cập nhật người chơi nhìn thấy
        private static void UpdateVisiblePlayers(Players player, Zone[] visibleZones)
        {
            List<Players> visiblePlayers = new List<Players>();
            
            foreach (var zone in visibleZones)
            {
                // Thêm tất cả người chơi trong zone có thể nhìn thấy
                foreach (var otherPlayer in zone.Players)
                {
                    if (otherPlayer != player)
                    {
                        visiblePlayers.Add(otherPlayer);
                    }
                }
            }
            
            // TODO: Cập nhật danh sách người chơi mà player có thể nhìn thấy
            // Thực hiện cập nhật dựa trên game engine của bạn
            
            // Ví dụ:
            // player.UpdateVisiblePlayers(visiblePlayers);
            
            // Ghi log
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Player {player.UserName} có thể nhìn thấy {visiblePlayers.Count} người chơi khác");
        }
        
        // Cập nhật NPC nhìn thấy
        private static void UpdateVisibleNpcs(Players player, Zone[] visibleZones)
        {
            List<NpcClass> visibleNpcs = new List<NpcClass>();
            
            foreach (var zone in visibleZones)
            {
                // Thêm tất cả NPC trong zone có thể nhìn thấy
                visibleNpcs.AddRange(zone.NPCs);
            }
            
            // TODO: Cập nhật danh sách NPC mà player có thể nhìn thấy
            // Thực hiện cập nhật dựa trên game engine của bạn
            
            // Ví dụ:
            // player.UpdateVisibleNpcs(visibleNpcs);
            
            // Ghi log
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Player {player.UserName} có thể nhìn thấy {visibleNpcs.Count} NPC");
        }
        
        // Cập nhật vật phẩm nhìn thấy
        private static void UpdateVisibleItems(Players player, Zone[] visibleZones)
        {
            List<X_Mat_Dat_Vat_Pham_Loai> visibleItems = new List<X_Mat_Dat_Vat_Pham_Loai>();
            
            foreach (var zone in visibleZones)
            {
                // Thêm tất cả vật phẩm trong zone có thể nhìn thấy
                visibleItems.AddRange(zone.Items);
            }
            
            // TODO: Cập nhật danh sách vật phẩm mà player có thể nhìn thấy
            // Thực hiện cập nhật dựa trên game engine của bạn
            
            // Ví dụ:
            // player.UpdateVisibleItems(visibleItems);
            
            // Ghi log
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Player {player.UserName} có thể nhìn thấy {visibleItems.Count} vật phẩm");
        }
        
        // Hàm kiểm tra xem người chơi có thể tương tác với người chơi khác không
        public static bool CanInteractWithPlayer(this Players player, Players target)
        {
            return ZoneManager.Instance.CanInteract(player, target);
        }
        
        // Hàm kiểm tra xem người chơi có thể tương tác với NPC không
        public static bool CanInteractWithNpc(this Players player, NpcClass npc)
        {
            return ZoneManager.Instance.CanInteractWithNpc(player, npc);
        }
        
        // Phương thức kiểm tra xem người chơi có thể nhặt vật phẩm không
        public static bool CanPickupItem(this Players player, X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (player == null || item == null)
                    return false;
                
                // Nếu người chơi không có zone, mặc định cho phép nhặt
                if (player.CurrentZone == null)
                    return true;
                
                // Nếu vật phẩm không có zone, cho phép nhặt
                if (item.CurrentZone == null)
                    return true;
                
                // Nếu vật phẩm ở zone mặc định, mọi người đều có thể nhặt
                if (item.CurrentZone.ID == 0)
                    return true;
                
                // Nếu người chơi ở zone mặc định, không thể nhặt vật phẩm ở zone khác
                if (player.CurrentZone.ID == 0)
                    return false;
                
                // Nếu cùng zone, có thể nhặt
                return player.CurrentZone == item.CurrentZone;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"CanPickupItem error: {ex.Message}");
                return false;
            }
        }
        
        // Phương thức kiểm tra xem người chơi có thể tấn công người chơi khác không
        public static bool CanAttackPlayer(this Players attacker, Players target)
        {
            try
            {
                if (attacker == null || target == null)
                    return false;
                
                // Nếu một trong hai người chơi không có zone, mặc định cho phép tấn công
                if (attacker.CurrentZone == null || target.CurrentZone == null)
                    return true;
                
                //// Nếu một trong hai ở zone mặc định, không thể tấn công
                //if (attacker.CurrentZone.ID == 0 || target.CurrentZone.ID == 0)
                //    return false;
                
                // Nếu cùng zone, có thể tấn công
                return attacker.CurrentZone == target.CurrentZone;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"CanAttackPlayer error: {ex.Message}");
                return false;
            }
        }
        
        // Phương thức kiểm tra xem người chơi có thể tấn công NPC không
        public static bool CanAttackNpc(this Players attacker, NpcClass npc)
        {
            try
            {
                if (attacker == null || npc == null)
                    return false;
                
                // Nếu người chơi không có zone, mặc định cho phép tấn công
                if (attacker.CurrentZone == null)
                    return true;
                
                // Nếu không tìm thấy zone của NPC, cho phép tấn công
                if (npc.CurrentZone == null)
                    return true;
                
                // Nếu NPC ở zone mặc định, mọi người đều có thể tấn công
                if (npc.CurrentZone.ID == 0)
                    return true;
                 // Tìm zone chứa NPC

                if (npc.CurrentZone != attacker.CurrentZone)
                {
                    attacker.HeThongNhacNho("Đại hiệp không thể tấn công quái ở vị diện khác");
                    return false;
                }
                // Nếu người chơi ở zone mặc định, không thể tấn công NPC ở zone khác
                if (attacker.CurrentZone.ID == 0)
                {
                    attacker.HeThongNhacNho("Đại hiệp không thể tấn công quái ở vị diện khác");
                    return false;
                }
                
                // Nếu cùng zone, có thể tấn công
                return attacker.CurrentZone == npc.CurrentZone;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"CanAttackNpc error: {ex.Message}");
                return false;
            }
        }
        
        // Phương thức cập nhật vùng nhìn thấy của người chơi dựa trên zone
        public static void RefreshObjectsVisibility(this Players player)
        {
            try
            {
                if (player == null)
                    return;
                
                // Cập nhật danh sách các đối tượng nhìn thấy dựa theo zone
                ZoneExtensions.RefreshZoneVisibility(player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"RefreshObjectsVisibility error: {ex.Message}");
            }
        }
    }
} 