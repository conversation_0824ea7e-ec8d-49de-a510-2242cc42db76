using System;
using HeroYulgang.Helpers;
using RxjhServer.GroupQuest;
using YulgangServer;

namespace RxjhServer
{
    public partial class NpcClass
    {
        public bool deathHandled = false;

        /// <summary>
        /// Wrapper cho phương thức GuiDiTuVongSoLieu để hỗ trợ xử lý sự kiện khi NPC hết thời gian tự chết
        /// </summary>
        public void GuiDiTuVongSoLieuWrapper()
        {
            try
            {
                // Gọi phương thức gốc
                // Bị dispose = ko giết
                GuiDiTuVongSoLieu();
                //TaoCo();

                // Xử lý sự kiện sau khi NPC chết
                //if (IsWorldBoss)
                //{
                //    // Xử lý khi boss chết
                //    HandleWorldBossDeath();
                //}

                // Nếu NPC thuộc zone liên server, gửi thông tin đến server khác
                if (CurrentZone != null && CurrentZone.IsCrossServer)
                {
                    // G<PERSON>i thông tin NPC chết đến LoginServer
                    World.conn.SendCrossServerAction(
                        CurrentZone.ID,
                        "NPC_DEATH",
                        0, // Không cần sessionId của người chơi
                        NPC_SessionID,
                        WorldId
                    );
                }

                // Sau khi gọi xong, gọi hook để xử lý logic boss
                AfterGuiDiTuVongSoLieu(this);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi trong GuiDiTuVongSoLieuWrapper: {ex.Message}");
            }
        }

        /// <summary>
        /// Wrapper cho phương thức GuiDiTuVongSoLieu với sessionId để hỗ trợ xử lý sự kiện khi NPC chết
        /// </summary>
        /// <param name="sessionId">SessionId của người chơi</param>
        public void GuiDiTuVongSoLieuWrapper(int sessionId)
        {
            try
            {
                // Gọi phương thức gốc
                GuiDiTuVongSoLieu(sessionId);
                TaoCo();

                // Xử lý sự kiện sau khi NPC chết
                if (IsWorldBoss)
                {
                    // Xử lý khi boss chết
                    HandleWorldBossDeath();
                }

                // Nếu NPC thuộc zone liên server, gửi thông tin đến server khác
                if (CurrentZone != null && CurrentZone.IsCrossServer)
                {
                    // Gửi thông tin NPC chết đến LoginServer
                    World.conn.SendCrossServerAction(
                        CurrentZone.ID,
                        "NPC_DEATH",
                        sessionId,
                        NPC_SessionID,
                        WorldId
                    );
                }

                // Gọi sự kiện với người chơi cụ thể
                var player = World.FindPlayerBySession(sessionId);
                if (player != null)
                {
                    AfterGuiDiTuVongSoLieu(this, player);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gọi GuiDiTuVongSoLieuWrapper với sessionId: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý sau khi gửi dữ liệu tử vong của quái vật
        /// </summary>
        /// <param name="npc">Quái vật đã chết</param>
        public static void AfterGuiDiTuVongSoLieu(NpcClass npc)
        {
            try
            {
                // Không có người chơi cụ thể, không xử lý
                // LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"NPC {npc.FLD_PID} đã chết, không có người chơi cụ thể");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý AfterGuiDiTuVongSoLieu: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý sau khi gửi dữ liệu tử vong của quái vật với người chơi cụ thể
        /// </summary>
        /// <param name="npc">Quái vật đã chết</param>
        /// <param name="player">Người chơi đã giết quái vật</param>
        public static void AfterGuiDiTuVongSoLieu(NpcClass npc, Players player)
        {
            try
            {
                // Gọi sự kiện OnNpcDeath
                if (npc.CurrentZone != null && npc.CurrentZone.IsCrossServer && npc.WorldId != World.ServerID.ToString())
                {
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"NPC {npc.FLD_PID} đã chết, không xử lý death");
                   // Npc Clone không handle death
                   return;
                }

                npc.OnNpcDeath(player);

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"NPC {npc.FLD_PID} đã bị {player.UserName} tiêu diệt");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý AfterGuiDiTuVongSoLieu với player: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý sự kiện khi NPC chết
        /// </summary>
        /// <param name="player">Người chơi đã giết NPC</param>
        public void OnNpcDeath(Players player)
        {
            // Tránh xử lý nhiều lần
            if (deathHandled)
                return;

            deathHandled = true;

            try
            {
                if (IsWorldBoss)
                {
                    HandleWorldBossDeath();
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Boss Thế Giới {ID} đã bị tiêu diệt");
                }

                // Gọi sự kiện tiêu diệt quái vật
                GroupQuestEvent.Instance.RaiseMonsterKilled(this, player);
                QuangBa_NPCDeathSoLieu();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý sự kiện tử vong của NPC {ID}: {ex.Message}");
            }
        }
    }
}