using RxjhServer.ManageZone;
using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer
{
    public partial class Players
    {
        /// <summary>
        /// Kiểm tra xem một người chơi khác có ở trong phạm vi và có thể nhìn thấy theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <param name="player">Người chơi cần kiểm tra</param>
        /// <returns>true nếu người chơi trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public bool FindPlayers(int far, Players player)
        {
            try
            {
                // Kiểm tra cơ bản về bản đồ và khoảng cách
                if (!FindPlayerByRange(far, player))
                {
                    return false;
                }
                
                // Nếu một trong hai người chơi không có zone, mặc định cho phép nhìn thấy
                if (this.CurrentZone == null || player.CurrentZone == null)
                {
                    
                    return true;
                }
                // Kiểm tra theo quy tắc zone
                return this.CurrentZone.CanSeeZone(player.CurrentZone);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"FindPlayersZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Kiểm tra xem một NPC có ở trong phạm vi và có thể nhìn thấy theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <param name="npc">NPC cần kiểm tra</param>
        /// <returns>true nếu NPC trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public bool FindNpcZone(int far, NpcClass npc)
        {
            try
            {
                // Kiểm tra cơ bản về bản đồ
                if (npc.Rxjh_Map != this.NhanVatToaDo_BanDo)
                {
                    return false;
                }
                
                // Kiểm tra khoảng cách
                var num = npc.Rxjh_X - this.NhanVatToaDo_X;
                var num2 = npc.Rxjh_Y - this.NhanVatToaDo_Y;
                bool isInRange = (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far;
                
                if (!isInRange)
                {
                    return false;
                }
                
                // Nếu người chơi không có zone, mặc định cho phép nhìn thấy
                if (this.CurrentZone == null)
                {
                    return true;
                }
                
                // Tìm zone chứa NPC
                Zone npcZone = null;
                var allZones = ZoneManager.Instance.GetAllZones();
                foreach (var zone in allZones)
                {
                    if (zone.NPCs.Contains(npc))
                    {
                        npcZone = zone;
                        break;
                    }
                }
                
                // Nếu NPC không thuộc zone nào, mặc định cho phép nhìn thấy
                if (npcZone == null)
                {
                    return true;
                }
                
                // Kiểm tra theo quy tắc zone
                return this.CurrentZone.CanSeeZone(npcZone);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"FindNpcZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Kiểm tra xem một vật phẩm có ở trong phạm vi và có thể nhìn thấy theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <param name="item">Vật phẩm cần kiểm tra</param>
        /// <returns>true nếu vật phẩm trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public bool FindItemZone(int far, X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                // Kiểm tra cơ bản về bản đồ
                if (item.Rxjh_Map != this.NhanVatToaDo_BanDo)
                {
                    return false;
                }
                
                // Kiểm tra khoảng cách
                var num = item.Rxjh_X - this.NhanVatToaDo_X;
                var num2 = item.Rxjh_Y - this.NhanVatToaDo_Y;
                bool isInRange = (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= far;
                
                if (!isInRange)
                {
                    return false;
                }
                
                // Nếu người chơi không có zone, mặc định cho phép nhìn thấy
                if (this.CurrentZone == null)
                {
                    return true;
                }
                
                // Kiểm tra xem người chơi có thể nhìn thấy vật phẩm theo quy tắc zone không
                return item.CanPlayerSeeItem(this);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"FindItemZone error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Lấy danh sách người chơi xung quanh theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <returns>Danh sách người chơi trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public List<Players> GetPlayersInRangeZone(int far)
        {
            List<Players> result = new List<Players>();
            try
            {
                foreach (var player in World.allConnectedChars.Values)
                {
                    if (player != this && FindPlayers(far, player))
                    {
                        result.Add(player);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"GetPlayersInRangeZone error: {ex.Message}");
            }
            return result;
        }
        
        /// <summary>
        /// Lấy danh sách NPC xung quanh theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <returns>Danh sách NPC trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public List<NpcClass> GetNpcsInRangeZone(int far)
        {
            List<NpcClass> result = new List<NpcClass>();
            try
            {
                // Lấy tất cả NPC trên bản đồ hiện tại bằng MapClass
                var npcsInMap = MapClass.GetnpcTemplate(this.NhanVatToaDo_BanDo);
                
                if (npcsInMap != null)
                {
                    foreach (var npc in npcsInMap.Values)
                    {
                        if (FindNpcZone(far, npc))
                        {
                            result.Add(npc);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"GetNpcsInRangeZone error: {ex.Message}");
            }
            return result;
        }
        
        /// <summary>
        /// Lấy danh sách vật phẩm xung quanh theo quy tắc Zone
        /// </summary>
        /// <param name="far">Khoảng cách tối đa</param>
        /// <returns>Danh sách vật phẩm trong phạm vi và có thể nhìn thấy theo quy tắc Zone</returns>
        public List<X_Mat_Dat_Vat_Pham_Loai> GetItemsInRangeZone(int far)
        {
            List<X_Mat_Dat_Vat_Pham_Loai> result = new List<X_Mat_Dat_Vat_Pham_Loai>();
            try
            {
                foreach (var item in World.ItmeTeM.Values)
                {
                    if (FindItemZone(far, item))
                    {
                        result.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"GetItemsInRangeZone error: {ex.Message}");
            }
            return result;
        }
    }
} 