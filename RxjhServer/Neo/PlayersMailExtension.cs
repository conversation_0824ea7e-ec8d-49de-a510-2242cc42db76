using System;
using System.Data;

namespace RxjhServer
{
    /// <summary>
    /// Lớp mở rộng kết nối Players với MailSystem
    /// </summary>
    public partial class Players
    {
        private MailSystem _mailSystem;

        /// <summary>
        /// Hệ thống mail
        /// </summary>
        public MailSystem MailSystem => _mailSystem ??= new MailSystem(this);

        #region Mail System Forwarding Methods

        /// <summary>
        /// Chuyển tiếp yêu cầu danh sách mail (phiên bản test)
        /// </summary>
        public void RequestMailListTest(byte[] data)
        {
            MailSystem.RequestMailListTest(data);
        }

        /// <summary>
        /// Chuyển tiếp yêu cầu danh sách mail
        /// </summary>
        public void RequestMailList(byte[] data)
        {
            MailSystem.RequestMailList(data);
        }

        /// <summary>
        /// Chuyển tiếp gửi mail kèm vật phẩm
        /// </summary>
        public void SendMailCod(byte[] data)
        {
            MailSystem.SendMailCod(data);
        }

        /// <summary>
        /// Chuyển tiếp chấp nhận mail và nhận vật phẩm
        /// </summary>
        public void AcceptMailCod(byte[] data)
        {
            MailSystem.AcceptMailCod(data);
        }

        /// <summary>
        /// Chuyển tiếp cập nhật tiền tệ khi thực hiện giao dịch mail
        /// </summary>
        public void MailSystem_Currency_Update(long price, CurrencyOperation operation)
        {
            MailSystem.MailSystem_Currency_Update(price, operation);
        }

        /// <summary>
        /// Chuyển tiếp từ chối mail và trả lại vật phẩm cho người gửi
        /// </summary>
        public void RejectMailCod(byte[] data)
        {
            MailSystem.RejectMailCod(data);
        }

        /// <summary>
        /// Chuyển tiếp gửi thông báo có mail mới
        /// </summary>
        public void SendMailCodNotification(Players player)
        {
            MailSystem.SendMailCodNotification(player);
        }

        /// <summary>
        /// Chuyển tiếp gửi thông báo có mail mới từ admin
        /// </summary>
        public void SendMailCodNotificationByAdmin(int SessionID)
        {
            MailSystem.SendMailCodNotificationByAdmin(SessionID);
        }

        /// <summary>
        /// Chuyển tiếp gửi thông báo có mail mới với ID cụ thể
        /// </summary>
        public void SendNewCodIdNotification(Players player, int id)
        {
            MailSystem.SendNewCodIdNotification(player, id);
        }

        /// <summary>
        /// Chuyển tiếp kiểm tra xem người chơi có mail nào không
        /// </summary>
        public void CheckIfUserHaveMailCod(Players player)
        {
            MailSystem.CheckIfUserHaveMailCod(player);
        }

        #endregion
    }
}