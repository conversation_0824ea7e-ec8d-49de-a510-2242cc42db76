using System.Collections.Generic;

namespace RxjhServer;

public class X_Che_Tac_Vat_Pham_Loai
{
	public List<X_Che_Tac_Can_Vat_Pham_Loai> CanVatPham = new();

	public int VatPham_ID;

	public string VatPhamTen;

	public int VatPhamSoLuong;

	public int CheTaoLoaiHinh;

	public int CheTaoDangCap;

	public static List<int> GetCheTaoVatPhamDanhSachClass(int int_0, int int_1)
	{
		List<int> list = new();
		foreach (var value in World.CheTacVatPhamDanhSach.Values)
		{
			if (value.CheTaoLoaiHinh == int_0 && int_1 >= value.CheTaoDangCap)
			{
				list.Add(value.VatPham_ID);
			}
		}
		return list;
	}
}
