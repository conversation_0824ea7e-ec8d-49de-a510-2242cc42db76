﻿
using RxjhServer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Timers;
using YulgangServer;
using HeroYulgang.Helpers;


namespace RxjhServer.HeroBoss;

public partial class HeroWorldBossClass : IDisposable
{
    public HeroWorldBossClass()
    {
        try
        {
            // Không cần tạo instance mới, chỉ cần lấy instance hiện có
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, "Thiên cơ các Boss Thế Giới đã được khởi tạo");
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi khởi tạo HeroWorldBossClass: " + ex.Message);
        }
    }

    // Thông báo cho tất cả người chơi
    public void HeThongNhacNhoAllPlayer(string text)
    {
        foreach (var value in World.allConnectedChars.Values)
        {
            value.HeThongNhacNho(text);
        }
    }

    // Giải phóng tài nguyên
    public void Dispose()
    {
        try
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, "Boss Thế Giới Kết Thúc!!!!");

            // Dispose WorldBossManager singleton
            WorldBossManager.Instance.Dispose();

            // Đặt lại biến toàn cục
            World.WorldBossEvent = null;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi khi đóng HeroWorldBossClass: " + ex.Message);
        }
    }
    
        public void CheckSchedule()
        {
            WorldBossManager.Instance.CheckSchedule();
        }


    // Gọi boss ngay lập tức (cho lệnh admin)
    public void StartWorldBoss()
    {
        try
        {
            // Sử dụng singleton instance
            WorldBossManager.Instance.SpawnWorldBossNow();
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, "Đã gọi Boss Thế Giới ngay lập tức");
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi khi gọi Boss Thế Giới: " + ex.Message);
        }
    }

    // Thêm lịch trình boss mới
    public void AddBossSchedule(int hour, int minute)
    {
        try
        {
            WorldBossManager.Instance.AddBossSchedule(hour, minute);
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã thêm lịch trình boss vào {hour}:{minute:D2}");
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi thêm lịch trình boss: {ex.Message}");
        }
    }

    // Các phương thức dưới đây giữ lại cho tương thích ngược, nhưng sẽ không được sử dụng nữa

    public void WorldBossEvent_TimeWait(object sender, ElapsedEventArgs e)
    {
        // Phương thức này chỉ giữ lại để tương thích ngược, không thực hiện gì cả
    }

    public void WorldBossEvent_TimeStart(object sender, ElapsedEventArgs e)
    {
        // Phương thức này chỉ giữ lại để tương thích ngược, không thực hiện gì cả
    }

    public void WorldBossEvent_TimeExpired(object sender, ElapsedEventArgs e)
    {
        // Phương thức này chỉ giữ lại để tương thích ngược, không thực hiện gì cả
    }

    public void WorldBossEvent_KilledBoss(object sender, ElapsedEventArgs e)
    {
        // Phương thức này chỉ giữ lại để tương thích ngược, không thực hiện gì cả
    }

    // Phân phối phần thưởng cơ bản cho boss
    public void DistributeRewards(int bossID)
    {
        try
        {
            // Kiểm tra thông tin đóng góp
            if (!World.List_WorldBossContribute.TryGetValue(bossID, out var contribute))
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không thể phân phối phần thưởng cho boss {bossID}: Không tìm thấy thông tin đóng góp");
                return;
            }

            // Phân phối phần thưởng điểm
            World.WorldBoss_TraoThuongPoint(bossID);
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã phân phối phần thưởng điểm cho Boss Thế Giới ID: {bossID}");

            // Phân phối phần thưởng đặc biệt
            DistributeSpecialRewards(contribute);

            try
            {
                // Xóa boss khỏi danh sách đóng góp sau khi đã phân phối phần thưởng
                World.RemoveWorldBoss(bossID);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xóa boss {bossID} khỏi danh sách đóng góp: {ex.Message}");
                if (World.List_WorldBossContribute.ContainsKey(bossID))
                {
                    World.List_WorldBossContribute.Remove(bossID);
                }
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi phân phối phần thưởng: {ex.Message}");
        }
    }

    // Phân phối phần thưởng đặc biệt dựa trên loại boss
    private void DistributeSpecialRewards(WorldBossContributeClass contribute)
    {
        try
        {
            if (contribute == null)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi: contribute là null cho Boss");
                return;
            }

            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Tìm vật phẩm thưởng của Boss {contribute.ID} {contribute.Boss?.FLD_PID} loại {contribute.Boss?.BossType}");

            var bossConfig = BossConfigManager.GetBossConfig(contribute.Boss?.FLD_PID ?? 0, contribute.Boss?.BossType ?? BossType.WorldBoss);
            if (bossConfig == null)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi: Không tìm thấy cấu hình cho Boss {contribute.ID} {contribute.Boss?.FLD_PID} loại {contribute.Boss?.BossType}");
                return;
            }

            // Xử lý phần thưởng dựa trên đóng góp
            ProcessContributionRewards(bossConfig, contribute);

            // Xử lý phần thưởng ngẫu nhiên
            ProcessRandomRewards(bossConfig, contribute);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi phân phối phần thưởng đặc biệt: {ex.Message}\nStack Trace: {ex.StackTrace}");
        }
    }

    // Áp dụng thuộc tính điều chỉnh cho boss khi nó được tạo ra
    public void ApplyBossModifiers(NpcClass boss)
    {
        if (boss == null || !boss.IsWorldBoss)
            return;

        try
        {
            // Áp dụng các thuộc tính điều chỉnh từ cấu hình
            BossConfigManager.ApplyStatModifiers(boss);

            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã áp dụng thuộc tính điều chỉnh cho boss {boss.ID} ({boss.Name})");
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi áp dụng thuộc tính điều chỉnh cho boss: {ex.Message}");
        }
    }

    private void ProcessContributionRewards(BossRewardConfig bossConfig, WorldBossContributeClass contribute)
    {
        try
        {
            // Lấy danh sách vật phẩm dựa trên đóng góp
            var dropResults = bossConfig.GetDrops(RewardDistributionType.ContributionBased);
            if (dropResults == null || dropResults.Count == 0)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Boss {contribute.ID} không có vật phẩm đặc biệt nào rơi ra theo đóng góp");
                return;
            }

            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Tìm thấy {dropResults.Count} vật phẩm đặc biệt theo đóng góp");

            // Xử lý từng vật phẩm
            foreach (var dropResult in dropResults)
            {
                var item = World.CreateAnItem(dropResult.Item.ItemId, dropResult.Item.Count);
                if (item != null)
                {
                    DistributeItemByContribution(item, contribute);
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã phân phối vật phẩm {dropResult.Item.Name} theo đóng góp");
                }
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi trong ProcessContributionRewards: {ex.Message}");
        }
    }

    private void ProcessRandomRewards(BossRewardConfig bossConfig, WorldBossContributeClass contribute)
    {
        try
        {
            // Kiểm tra xem có người chơi tham gia không
            if (contribute.Contribute == null || contribute.Contribute.Count == 0)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Không có người chơi nào tham gia đánh Boss {contribute.ID}");
                return;
            }

            // Lấy danh sách vật phẩm với tỉ lệ bằng nhau
            var dropResults = bossConfig.GetDrops(RewardDistributionType.EqualChance);
            if (dropResults == null || dropResults.Count == 0)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Boss {contribute.ID} không có vật phẩm đặc biệt nào rơi ra với tỉ lệ bằng nhau");
                return;
            }

            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Tìm thấy {dropResults.Count} vật phẩm đặc biệt với tỉ lệ bằng nhau");

            // Xử lý từng vật phẩm
            foreach (var dropResult in dropResults)
            {
                var item = World.CreateAnItem(dropResult.Item.ItemId, dropResult.Item.Count);
                if (item != null)
                {
                    DistrubteItemByParticipants(item, contribute);
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã phân phối vật phẩm {dropResult.Item.Name} với tỉ lệ bằng nhau");
                }
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi trong ProcessRandomRewards: {ex.Message}");
        }
    }

    private void DistrubteItemByParticipants(X_Vat_Pham_Loai item, WorldBossContributeClass contribute)
    {
        try
        {
            // Lọc danh sách người chơi có đóng góp damage
            List<int> playerSession = contribute.Contribute.Values
                .Where(c => c.Damage > 0)
                .Select(c => c.SessionID)
                .ToList();

            if (playerSession.Count == 0)
                return;

            // Chọn ngẫu nhiên người chơi trong danh sách
            int selected = playerSession[RNG.Next(0, playerSession.Count -1)];

            var winner = contribute.Contribute.FirstOrDefault(c=>c.Key.Item2 == selected).Value;

            if (winner == null)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Không tìm thấy thông tin người chiến thắng");
                return;
            }

            // Nếu tìm thấy người chiến thắng, gửi vật phẩm
                // Gửi vật phẩm qua mail
            var description = $"Bạn đã nhận được phần thưởng: {item.GetItemName()} từ {GetBossTypeString(contribute.Boss?.BossType ?? BossType.WorldBoss)}. Vui lòng đến [Bát Quái Lão Nhân] để nhận thưởng";

            var byteDesc = System.Text.Encoding.GetEncoding(1252).GetBytes(UniTo1258(description));
            World.SendItemMail("[GM]", winner.PlayerName, byteDesc, 0, item, 30);
            World.SendMailCodNotificationByAdmin(winner.WorldID,winner.SessionID);

            // Thông báo cho người chơi
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi {item.GetItemName()} cho {winner.PlayerName}");
            World.ToanCucNhacNho($"Đại hiệp [{winner.PlayerName}] đã may mắn nhận được vật phẩm hiếm [{item.GetItemName()}] từ {GetBossTypeString(contribute.Boss?.BossType ?? BossType.WorldBoss)}. Phần thưởng ngẫu nhiên dành cho một trong tất cả các anh hùng tham gia!", 21, "Thiên cơ các");
           // winner.HeThongNhacNho(description);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi phân phối vật phẩm: {ex.Message}");
        }
    }

    // Phân phối vật phẩm dựa trên đóng góp
    private void DistributeItemByContribution(X_Vat_Pham_Loai item, WorldBossContributeClass contribute)
    {
        try
        {
            // Lọc danh sách người chơi có đóng góp damage
            List<int> playerSession = contribute.Contribute.Values
                .Where(c => c.Damage > 0)
                .Select(c => c.SessionID)
                .ToList();

            if (playerSession.Count == 0)
                return;

            // Tính tổng damage
            long totalDamage = contribute.Contribute.Values.Sum(c => c.Damage);

            // Chọn ngẫu nhiên người chơi dựa trên tỉ lệ damage
            long randomThreshold = RNG.Next(0, (int)totalDamage);
            long cumulativeDamage = 0;

            DamageContribute  winner = null;
            foreach (var session in playerSession)
            {
                var contr = contribute.Contribute.Values
                    .FirstOrDefault(c => c.SessionID == session);
                var playerDamage = contr?.Damage ?? 0;
                cumulativeDamage += playerDamage;

                if (cumulativeDamage >= randomThreshold)
                {
                    winner = contr;
                    break;
                }
            }

            // Nếu tìm thấy người chiến thắng, gửi vật phẩm
            if (winner != null)
            {
                // Gửi vật phẩm qua mail
                var description = $"Bạn đã nhận được phần thưởng: {item.GetItemName()} từ {GetBossTypeString(contribute.Boss?.BossType ?? BossType.WorldBoss)}. Vui lòng đến [Bát Quái Lão Nhân] để nhận thưởng";

                var byteDesc = System.Text.Encoding.GetEncoding(1252).GetBytes(UniTo1258(description));
                World.SendItemMail("[GM]", winner.PlayerName, byteDesc, 0, item, 30);
                World.SendMailCodNotificationByAdmin(winner.WorldID,winner.SessionID);

                // Thông báo cho người chơi
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi {item.GetItemName()} cho {winner.PlayerName}");
                World.ThongBaoGuiDi($"Đại hiệp [{winner.PlayerName}] đã xuất sắc nhận được phần thưởng [{item.GetItemName()}] từ {GetBossTypeString(contribute.Boss?.BossType ?? BossType.WorldBoss)} nhờ đóng góp vượt trội vào tổng sát thương. Càng gây nhiều sát thương, tỉ lệ nhận thưởng càng cao!", 0);
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi phân phối vật phẩm: {ex.Message}");
        }
    }

    // Lấy chuỗi mô tả loại boss
    private string GetBossTypeString(BossType bossType)
    {
        return bossType switch
        {
            BossType.WorldBoss => "Boss Thế Giới",
            BossType.GuildBoss => "Boss Bang Hội",
            BossType.SummonBoss => "Boss Triệu Hồi",
            _ => "Boss"
        };
    }

    // Hàm hỗ trợ chuyển đổi Unicode sang CP1258
    private string UniTo1258(string text)
    {
        return text; // Giả sử đã có hàm chuyển đổi trong NpcClass, ở đây chỉ trả về text gốc
    }
}


