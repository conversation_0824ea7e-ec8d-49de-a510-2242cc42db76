namespace RxjhServer;

public class NpcDropClass
{
	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	private int int_5;

	private int int_6;

	private int int_7;

	private int int_8;

	private string string_0;

	public int FLD_NPC_PID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int FLD_ITME_PID
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int FLD_LEVEL1
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int FLD_LEVEL2
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int FLD_MAGIC0
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return int_8;
		}
		set
		{
			int_8 = value;
		}
	}

	public string FLD_NAME
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}
}
