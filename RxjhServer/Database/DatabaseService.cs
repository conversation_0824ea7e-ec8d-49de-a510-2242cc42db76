using RxjhServer.Database.Repositories;
using System;
using HeroYulgang.Database.Entities.Account;
using HeroYulgang.Database.Entities.Game;
using HeroYulgang.Database.Entities.Public;

namespace RxjhServer.Database
{
    public class DatabaseService : IDisposable
    {
        private readonly AccountDbContext _accountDbContext;
        private readonly GameDbContext _gameDbContext;
        private readonly PublicDbContext _publicDbContext;

        public AccountRepository AccountRepository { get; }
        public CharacterRepository CharacterRepository { get; }
        public ItemRepository ItemRepository { get; }
        public GuildRepository GuildRepository { get; }
        public ServerInfoRepository ServerInfoRepository { get; }

        public DatabaseService()
        {
            _accountDbContext = new AccountDbContext();
            _gameDbContext = new GameDbContext();
            _publicDbContext = new PublicDbContext();

            AccountRepository = new AccountRepository(_accountDbContext);
            CharacterRepository = new CharacterRepository(_gameDbContext);
            ItemRepository = new ItemRepository(_gameDbContext);
            GuildRepository = new GuildRepository(_gameDbContext);
            ServerInfoRepository = new ServerInfoRepository(_publicDbContext);
        }

        public void Dispose()
        {
            _accountDbContext?.Dispose();
            _gameDbContext?.Dispose();
            _publicDbContext?.Dispose();
        }
    }
}
