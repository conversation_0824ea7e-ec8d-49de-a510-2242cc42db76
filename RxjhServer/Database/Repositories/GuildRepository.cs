using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Database.Entities.Game;
using Microsoft.EntityFrameworkCore;

namespace RxjhServer.Database.Repositories
{
    public class GuildRepository
    {
        private readonly GameDbContext _context;

        public GuildRepository(GameDbContext context)
        {
            _context = context;
        }

        public async Task<TblXwwlGuild?> GetGuildAsync(int guildId)
        {
            return await _context.TblXwwlGuilds.FindAsync(guildId);
        }

        public async Task<TblXwwlGuild> GetGuildByNameAsync(string name)
        {
            return await _context.TblXwwlGuilds.FirstOrDefaultAsync(g => g.GName == name);
        }

        public async Task<bool> CreateGuildAsync(TblXwwlGuild guild)
        {
            try
            {
                await _context.TblXwwlGuilds.AddAsync(guild);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateGuildAsync(TblXwwlGuild guild)
        {
            try
            {
                _context.TblXwwlGuilds.Update(guild);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteGuildAsync(int guildId)
        {
            try
            {
                var guild = await _context.TblXwwlGuilds.FindAsync(guildId);
                if (guild != null)
                {
                    _context.TblXwwlGuilds.Remove(guild);
                    await _context.SaveChangesAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<TblXwwlGuild>> GetAllGuildsAsync()
        {
            return await _context.TblXwwlGuilds.ToListAsync();
        }

        public async Task<List<TblXwwlGuildMember>> GetGuildMembersAsync(int guildId)
        {
            return await _context.TblXwwlGuildMembers.Where(m => m.Id == guildId).ToListAsync();
            // return await _context.TblXwwlGuildMembers
            //     .Where(m => m.ID == guildId)
            //     .ToListAsync();
        }

        public async Task<TblXwwlGuildMember> GetGuildMemberAsync(int guildId, int charId)
        {
            return await _context.TblXwwlGuildMembers
                .FindAsync(guildId, charId);
        }

        public async Task<bool> AddGuildMemberAsync(TblXwwlGuildMember member)
        {
            try
            {
                await _context.TblXwwlGuildMembers.AddAsync(member);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateGuildMemberAsync(TblXwwlGuildMember member)
        {
            try
            {
                _context.TblXwwlGuildMembers.Update(member);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RemoveGuildMemberAsync(int guildId, int charId)
        {
            try
            {
                var member = await _context.TblXwwlGuildMembers.FindAsync(guildId, charId);
                if (member != null)
                {
                    _context.TblXwwlGuildMembers.Remove(member);
                    await _context.SaveChangesAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> IsNameExistsAsync(string name)
        {
            return await _context.TblXwwlGuildMembers.AnyAsync(g => g.GName == name);
        }
    }
}
