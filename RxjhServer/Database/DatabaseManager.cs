using System;
using System.Threading.Tasks;

namespace RxjhServer.Database
{
    public static class DatabaseManager
    {
        private static DatabaseService _databaseService;

        public static DatabaseService DatabaseService
        {
            get
            {
                if (_databaseService == null)
                {
                    _databaseService = new DatabaseService();
                }
                return _databaseService;
            }
        }

        public static async Task InitializeAsync()
        {
            try
            {
                Console.WriteLine("Đang khởi tạo cơ sở dữ liệu...");

                // Khởi tạo cơ sở dữ liệu
                var initializer = new DatabaseInitializer();
                await initializer.InitializeAsync();

                // Khởi tạo dịch vụ cơ sở dữ liệu
                _databaseService = new DatabaseService();

                Console.WriteLine("Khởi tạo cơ sở dữ liệu thành công");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi khởi tạo cơ sở dữ liệu: {ex.Message}");
                throw;
            }
        }

        public static void Initialize()
        {
            InitializeAsync().Wait();
        }
    }
}
