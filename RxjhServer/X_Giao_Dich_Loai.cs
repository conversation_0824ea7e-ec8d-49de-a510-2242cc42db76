using System;
using System.Collections.Generic;

namespace RxjhServer;

public class X_Giao_Dich_Loai : IDisposable
{
	public Dictionary<long, X_Vat_Pham_Giao_Dich_Loai> GiaoDichVatPham1;

	public bool GiaoDichBenTrong;

	public long GiaoDichTien;

	private bool _GiaoDich_TiepNhan;

	private Players _NguoiGiaoDich;

	public bool GiaoDich_TiepNhan
	{
		get
		{
			return _GiaoDich_TiepNhan;
		}
		set
		{
			_GiaoDich_TiepNhan = value;
		}
	}

	public Players NguoiGiaoDich
	{
		get
		{
			return _NguoiGiaoDich;
		}
		set
		{
			_NguoiGiaoDich = value;
		}
	}

	public X_Giao_Dich_Loai()
	{
	}

	~X_Giao_Dich_Loai()
	{
	}

	public void Dispose()
	{
		try
		{
			GiaoDichBenTrong = false;
			if (NguoiGiaoDich != null)
			{
				NguoiGiaoDich.OpenWarehouse = false;
				NguoiGiaoDich.GiaoDich.NguoiGiaoDich.OpenWarehouse = false;
				NguoiGiaoDich.GiaoDich.GiaoDichBenTrong = false;
				NguoiGiaoDich.GiaoDich.NguoiGiaoDich = null;
				NguoiGiaoDich = null;
			}
		}
		catch (Exception)
		{
		}
	}

	public X_Giao_Dich_Loai(Players NguoiGiaoDich_)
	{
		NguoiGiaoDich = NguoiGiaoDich_;
		GiaoDich_TiepNhan = false;
		GiaoDichTien = 0L;
		GiaoDichVatPham1 = new();
	}

	public void CloseTransaction()
	{
		Dispose();
	}
}
