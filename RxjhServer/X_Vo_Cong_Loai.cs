using System;

namespace RxjhServer;

public class X_Vo_Cong_Loai : IDisposable
{
	private int _VoCong_DangCap;

	private int _FLD_PID;

	private string _FLD_NAME;

	private string _FLD_MoiCapNguyHai;

	private int _FLD_ZX;

	private int _FLD_JOB;

	private int _FLD_JOBLEVEL;

	private int _FLD_LEVEL;

	private int _FLD_MP;

	private int _FLD_NEEDEXP;

	private int _FLD_AT;

	public int FLD_TYPE;

	private int _FLD_EFFERT;

	private int _FLD_INDEX;

	private int _FLD_CongKichSoLuong;

	private int _FLD_VoCongLoaiHinh;

	private int _FLD_TIME;

	private int _FLD_DEATHTIME;

	private int _FLD_CDTIME;

	private int _FLD_VoCongToiCaoDangCap;

	private int _FLD_MoiCapThemMP;

	private int _FLD_MoiCapThemLichLuyen;

	private int _FLD_MoiCapThemNguyHai;

	private int _FLD_MoiCapVoCongDiemSo;

	private int _FLD_MoiCapThemTuLuyenDangCap;

	private int _Time_Animation;

	public int VoCong_DangCap
	{
		get
		{
			return _VoCong_DangCap;
		}
		set
		{
			_VoCong_DangCap = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public string FLD_NAME
	{
		get
		{
			return _FLD_NAME;
		}
		set
		{
			_FLD_NAME = value;
		}
	}

	public string FLD_MoiCapNguyHai
	{
		get
		{
			return _FLD_MoiCapNguyHai;
		}
		set
		{
			_FLD_MoiCapNguyHai = value;
		}
	}

	public int FLD_ZX
	{
		get
		{
			return _FLD_ZX;
		}
		set
		{
			_FLD_ZX = value;
		}
	}

	public int FLD_JOB
	{
		get
		{
			return _FLD_JOB;
		}
		set
		{
			_FLD_JOB = value;
		}
	}

	public int FLD_JOBLEVEL
	{
		get
		{
			return _FLD_JOBLEVEL;
		}
		set
		{
			_FLD_JOBLEVEL = value;
		}
	}

	public int FLD_LEVEL
	{
		get
		{
			return _FLD_LEVEL;
		}
		set
		{
			_FLD_LEVEL = value;
		}
	}

	public int FLD_MP
	{
		get
		{
			return _FLD_MP;
		}
		set
		{
			_FLD_MP = value;
		}
	}

	public int FLD_NEEDEXP
	{
		get
		{
			return _FLD_NEEDEXP;
		}
		set
		{
			_FLD_NEEDEXP = value;
		}
	}

	public int FLD_AT
	{
		get
		{
			return _FLD_AT;
		}
		set
		{
			_FLD_AT = value;
		}
	}

	public int FLD_EFFERT
	{
		get
		{
			return _FLD_EFFERT;
		}
		set
		{
			_FLD_EFFERT = value;
		}
	}

	public int FLD_INDEX
	{
		get
		{
			return _FLD_INDEX;
		}
		set
		{
			_FLD_INDEX = value;
		}
	}

	public int FLD_CongKichSoLuong
	{
		get
		{
			return _FLD_CongKichSoLuong;
		}
		set
		{
			_FLD_CongKichSoLuong = value;
		}
	}

	public int FLD_VoCongLoaiHinh
	{
		get
		{
			return _FLD_VoCongLoaiHinh;
		}
		set
		{
			_FLD_VoCongLoaiHinh = value;
		}
	}

	public int FLD_TIME
	{
		get
		{
			return _FLD_TIME;
		}
		set
		{
			_FLD_TIME = value;
		}
	}

	public int FLD_DEATHTIME
	{
		get
		{
			return _FLD_DEATHTIME;
		}
		set
		{
			_FLD_DEATHTIME = value;
		}
	}

	public int FLD_CDTIME
	{
		get
		{
			return _FLD_CDTIME;
		}
		set
		{
			_FLD_CDTIME = value;
		}
	}

	public int FLD_VoCongToiCaoDangCap
	{
		get
		{
			return _FLD_VoCongToiCaoDangCap;
		}
		set
		{
			_FLD_VoCongToiCaoDangCap = value;
		}
	}

	public int FLD_MoiCapThemMP
	{
		get
		{
			return _FLD_MoiCapThemMP;
		}
		set
		{
			_FLD_MoiCapThemMP = value;
		}
	}

	public int FLD_MoiCapThemLichLuyen
	{
		get
		{
			return _FLD_MoiCapThemLichLuyen;
		}
		set
		{
			_FLD_MoiCapThemLichLuyen = value;
		}
	}

	public int FLD_MoiCapThemNguyHai
	{
		get
		{
			return _FLD_MoiCapThemNguyHai;
		}
		set
		{
			_FLD_MoiCapThemNguyHai = value;
		}
	}

	public int FLD_MoiCapVoCongDiemSo
	{
		get
		{
			return _FLD_MoiCapVoCongDiemSo;
		}
		set
		{
			_FLD_MoiCapVoCongDiemSo = value;
		}
	}

	public int Time_Animation
	{
		get
		{
			return _Time_Animation;
		}
		set
		{
			_Time_Animation = value;
		}
	}

	public int FLD_MoiCapThemTuLuyenDangCap
	{
		get
		{
			return _FLD_MoiCapThemTuLuyenDangCap;
		}
		set
		{
			_FLD_MoiCapThemTuLuyenDangCap = value;
		}
	}

	public byte[] VoCongID_byte => BitConverter.GetBytes(FLD_PID);

	public X_Vo_Cong_Loai()
	{
	}

	public X_Vo_Cong_Loai(int FLD_PID_)
	{
		FLD_PID = FLD_PID_;
		KhoiTaoVoCong(FLD_PID);
	}

	public static int GetTimeAnimation(int pid)
	{
		if (World.MagicList.TryGetValue(pid, out var value))
		{
			return value.FLD_CDTIME;
		}
		return 0;
	}

	public void Dispose()
	{
	}

	public void KhoiTaoVoCong(int id)
	{
		if (World.MagicList.TryGetValue(id, out var value))
		{
			FLD_NAME = value.FLD_NAME;
			FLD_AT = value.FLD_AT;
			FLD_EFFERT = value.FLD_EFFERT;
			FLD_INDEX = value.FLD_INDEX;
			FLD_JOB = value.FLD_JOB;
			FLD_JOBLEVEL = value.FLD_JOBLEVEL;
			FLD_LEVEL = value.FLD_LEVEL;
			FLD_MP = value.FLD_MP;
			FLD_NEEDEXP = value.FLD_NEEDEXP;
			FLD_PID = value.FLD_PID;
			FLD_TYPE = value.FLD_TYPE;
			FLD_ZX = value.FLD_ZX;
			FLD_CongKichSoLuong = value.FLD_CongKichSoLuong;
			FLD_VoCongLoaiHinh = value.FLD_VoCongLoaiHinh;
			FLD_MoiCapThemMP = value.FLD_MoiCapThemMP;
			FLD_MoiCapThemLichLuyen = value.FLD_MoiCapThemLichLuyen;
			FLD_MoiCapThemNguyHai = value.FLD_MoiCapThemNguyHai;
			FLD_MoiCapVoCongDiemSo = value.FLD_MoiCapVoCongDiemSo;
			FLD_DEATHTIME = value.FLD_DEATHTIME;
			FLD_CDTIME = value.FLD_CDTIME;
			FLD_VoCongToiCaoDangCap = value.FLD_VoCongToiCaoDangCap;
			FLD_MoiCapThemTuLuyenDangCap = value.FLD_MoiCapThemTuLuyenDangCap;
			FLD_MoiCapNguyHai = value.FLD_MoiCapNguyHai;
		}
	}

	public int GetAt(int pid, int Level)
	{
		try
		{
			if (World.MagicList.TryGetValue(pid, out var value))
			{
				return int.Parse(value.FLD_MoiCapNguyHai.Split(';')[Level - 1]);
			}
		}
		catch
		{
		}
		return 0;
	}

	public static X_Vo_Cong_Loai GetWg(int NhanVatChinhTa, int NhanVatNgheNghiep, int FLD_VoCongLoaiHinh, int FLD_INDEX)
	{
		foreach (var value in World.MagicList.Values)
		{
			if (value.FLD_ZX == 0)
			{
				if (value.FLD_JOB == NhanVatNgheNghiep && value.FLD_INDEX == FLD_INDEX && value.FLD_VoCongLoaiHinh == FLD_VoCongLoaiHinh)
				{
					return value;
				}
			}
			else if (value.FLD_ZX == NhanVatChinhTa && value.FLD_JOB == NhanVatNgheNghiep && value.FLD_INDEX == FLD_INDEX && value.FLD_VoCongLoaiHinh == FLD_VoCongLoaiHinh)
			{
				return value;
			}
		}
		return null;
	}

	public static X_Vo_Cong_Loai GetWg(int VoCong_ID)
	{
		foreach (var value in World.MagicList.Values)
		{
			if (value.FLD_PID == VoCong_ID)
			{
				return value;
			}
		}
		return null;
	}

	public static X_Vo_Cong_Loai Getsfewg(Players Playe, int wgid)
	{
		if (World.MagicList.TryGetValue(wgid, out var value) && value != null && Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX] != null && Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX].FLD_PID == wgid)
		{
			return Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX];
		}
		return null;
	}

	public static bool GetsfeWg(Players Playe, int wgid)
	{
		if (World.MagicList.TryGetValue(wgid, out var value) && value != null)
		{
			var x_Vo_Cong_Loai = Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX];
			if (x_Vo_Cong_Loai != null)
			{
				return Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX].FLD_PID == wgid;
			}
			return false;
		}
		return false;
	}

	public static X_Vo_Cong_Loai GetWg2(Players Playe, int FLD_VoCongLoaiHinh, int FLD_INDEX)
	{
		foreach (var value in World.MagicList.Values)
		{
			if (value.FLD_JOB == Playe.Player_Job && value.FLD_INDEX == FLD_INDEX && value.FLD_VoCongLoaiHinh == FLD_VoCongLoaiHinh)
			{
				return value;
			}
		}
		return null;
	}

	public static bool KiemTra_DieuKienTuLuyen(Players Playe, int FLD_VoCongLoaiHinh, int FLD_INDEX)
	{
		var wg = GetWg(Playe.Player_Zx, Playe.Player_Job, FLD_VoCongLoaiHinh, FLD_INDEX);
		if (wg != null)
		{
			if ((wg.FLD_ZX != 0 && Playe.Player_Zx != wg.FLD_ZX) || (wg.FLD_JOB != 0 && Playe.Player_Job != wg.FLD_JOB) || (wg.FLD_JOBLEVEL != 0 && Playe.Player_Job_level < wg.FLD_JOBLEVEL) || (wg.FLD_LEVEL != 0 && Playe.Player_Level < wg.FLD_LEVEL))
			{
				return false;
			}
			if (Playe.VoCongMoi[FLD_VoCongLoaiHinh, FLD_INDEX] != null)
			{
				Playe.HeThongNhacNho("Bộ võ công " + wg.FLD_NAME + " đã được chân nhân tu luyện, không thể lặp lại. Hãy chọn bí kíp khác!", 10, "Thiên cơ các");
				return false;
			}
		}
		return true;
	}

	public static void LearnMartialArtsBook(Players Playe, int FLD_VoCongLoaiHinh, int FLD_INDEX)
	{
		var wg = GetWg(Playe.Player_Zx, Playe.Player_Job, FLD_VoCongLoaiHinh, FLD_INDEX);
		if (wg != null)
		{
			Playe.VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX] = new(wg.FLD_PID);
			if (wg.FLD_VoCongLoaiHinh == 3)
			{
				Playe.VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX].VoCong_DangCap = 1;
			}
			if ((Playe.Player_Job == 8 || Playe.Player_Job == 9) && (wg.FLD_VoCongLoaiHinh == 0 || wg.FLD_VoCongLoaiHinh == 3))
			{
				Playe.VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX].VoCong_DangCap = 1;
			}
		}
	}
}
