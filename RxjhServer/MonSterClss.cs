namespace RxjhServer;

public class MonSterClss
{
	private string string_0;

	private int int_0;

	private int int_1;

	private int int_2;

	private double double_0;

	private double double_1;

	private int int_3;

	private int int_4;

	private int int_5;

	private int int_6;

	private int int_7;

	private int int_8;

	private int int_9;

	private int int_10;

	private int int_11;

	public string Name
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int Level
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int Rxjh_HP
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public double FLD_AT
	{
		get
		{
			return double_0;
		}
		set
		{
			double_0 = value;
		}
	}

	public double FLD_DF
	{
		get
		{
			return double_1;
		}
		set
		{
			double_1 = value;
		}
	}

	public int Rxjh_Exp
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int FLD_AUTO
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int FLD_BOSS
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int FLD_NPC
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int FLD_QUEST
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public int FLD_QUESTID
	{
		get
		{
			return int_8;
		}
		set
		{
			int_8 = value;
		}
	}

	public int FLD_STAGES
	{
		get
		{
			return int_9;
		}
		set
		{
			int_9 = value;
		}
	}

	public int FLD_QUESTITEM
	{
		get
		{
			return int_10;
		}
		set
		{
			int_10 = value;
		}
	}

	public int FLD_PP
	{
		get
		{
			return int_11;
		}
		set
		{
			int_11 = value;
		}
	}
	public int FLD_GOLD { get; set; }

    public int FLD_Accuracy { get; set; }

    public int FLD_Evasion { get; set; }

    public int FLD_FreeDrop { get; set; }
}
