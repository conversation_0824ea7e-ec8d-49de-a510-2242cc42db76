using System.Collections.Generic;

namespace RxjhServer;

public class MapClass
{
	public Dictionary<int, NpcClass> npcTemplate = new();

	private int int_0;

	private int maxcall;

	public int MapID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public static int GetNpcConn()
	{
		var num = 0;
		foreach (var value in World.Map.Values)
		{
			num += value.npcTemplate.Count;
		}
		return num;
	}

	public static Dictionary<int, NpcClass> GetnpcTemplate(int int_1)
	{
		if (World.Map.TryGetValue(int_1, out var value))
		{
			return value.npcTemplate;
		}
		return new();
	}

	public static Dictionary<int, NpcClass> GetnpcPID(int mapid, int pid)
	{
		Dictionary<int, NpcClass> dictionary = new();
		if (World.Map.TryGetValue(mapid, out var value))
		{
			foreach (var value2 in value.npcTemplate.Values)
			{
				if (value2.FLD_PID == pid)
				{
					dictionary.Add(value2.NPC_SessionID, value2);
				}
			}
		}
		return dictionary;
	}

	public static NpcClass GetNpc(int int_1, int int_2)
	{
		if (!World.Map.TryGetValue(int_1, out var value))
		{
			return null;
		}
		if (value.npcTemplate.TryGetValue(int_2, out var value2))
		{
			return value2;
		}
		return null;
	}

	public static void delnpc(int int_1, int int_2)
	{
		if (World.Map.TryGetValue(int_1, out var value))
		{
			value.del(int_2);
		}
	}

	public void del(int int_1)
	{
		using (new Lock(npcTemplate, "MapClass-del"))
		{
			npcTemplate.Remove(int_1);
		}
	}

	public void add(NpcClass npcClass_0)
	{
		var num = 0;
		if (maxcall > 29998)
		{
			maxcall = 0;
		}
		num = ((maxcall != 0) ? (maxcall + 1) : (maxcall = 10000));
		while (true)
		{
			if (num < 30000)
			{
				if (!npcTemplate.ContainsKey(num))
				{
					break;
				}
				num++;
				continue;
			}
			return;
		}
		npcClass_0.NPC_SessionID = num;
		maxcall = num;
		if (!npcTemplate.ContainsKey(npcClass_0.NPC_SessionID))
		{
			npcTemplate.Add(npcClass_0.NPC_SessionID, npcClass_0);
		}
	}
}
