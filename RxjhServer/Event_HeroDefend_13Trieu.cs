using System;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using HeroYulgang.Helpers;

namespace RxjhServer;

public class Event_HeroDefend_13Trieu : IDisposable
{
	private string jlsqlzj = string.Empty;

	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private System.Timers.Timer ThoiGian3;

	private System.Timers.Timer ThoiGian4;

	private System.Timers.Timer ThoiGian5;

	private DateTime kssj;

	private DateTime kssjgj;

	private int kssjint;

	private List<NpcClass> NpcClasses;

	public Event_HeroDefend_13Trieu()
	{
		try
		{
			try
			{
				NpcClasses = new();
				World.Event_13Trieu_Progress = 1;
			}
			catch
			{
				World.EventTop.Clear();
			}
			kssj = DateTime.Now.AddMinutes(World.ThoiGianChuanBi_ChoTheLucChien);
			World.TheLucChien_Progress = 1;
			World.TheLucChien_ChinhPhai_DiemSo = 0;
			World.TheLucChien_TaPhai_DiemSo = 0;
			ThoiGian1 = new(5000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			ThoiGianKetThucSuKien1(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thế lực chiến EventClass Progress == 1 - " + ex);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
			kssjint = num;
			foreach (var value in World.DanhSachNhanVat_ThamGia_Event_HeroDefend.Values)
			{
				if (!value.Client.TreoMay && value.NhanVatToaDo_BanDo == 41001)
				{
					value.HeThongNhacNho("Bản đồ thủ thành sắp khai chiến, đại hiệp mau服 linh đan để sẵn sàng xuất trận!", 10, "Thiên cơ các");
					value.GuiDi_TheLucChien_DemNguoc(kssjint);
				}
			}
			if (kssjint > 0)
			{
				return;
			}
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			World.TheLucChien_Progress = 3;
			foreach (var value2 in World.DanhSachNhanVat_ThamGia_Event_HeroDefend.Values)
			{
				if (!value2.Client.TreoMay && value2.NhanVatToaDo_BanDo == 41001)
				{
					value2.HeThongNhacNho("Đại hiệp có tổng cộng: " + World.DanhSachNhanVat_ThamGia_Event_HeroDefend.Values.Count() + " hiệp khách tham gia thủ thành!", 10, "Thiên cơ các");
					value2.HeThongNhacNho("Quái vật X" + World.DanhSachNhanVat_ThamGia_Event_HeroDefend.Values.Count() + " xuất hiện tại mỗi cổng thành, đại hiệp hãy cẩn thận!", 10, "Thiên cơ các");
					value2.HeThongNhacNho("Đẳng cấp trung bình của đội là: " + getlevelteam() + " cấp, đại hiệp cần nâng cao võ công!", 10, "Thiên cơ các");
					value2.HeThongNhacNho("Tổng cộng có: " + World.Event_13Trieu_Total_Round + " hiệp đấu. Cứ 10 hiệp, đại ma đầu xuất hiện, tiêu diệt để đoạt bảo vật!", 10, "Thiên cơ các");
				}
			}
			kssjgj = DateTime.Now.AddMinutes(World.TheLucChien_TongThoiGian);
			ThoiGian2 = new(10000.0);
			ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
			ThoiGian2.Enabled = true;
			ThoiGian2.AutoReset = true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thế lực chiến ThoiGianKetThucSuKien1 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo != 801)
				{
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thế lực chiến ThoiGianKetThucSuKien2 error：" + ex);
		}
	}

	public int getlevelteam()
	{
		var num = 0;
		foreach (var value in World.DanhSachNhanVat_ThamGia_Event_HeroDefend.Values)
		{
			num += value.Player_Level;
		}
		return num / World.DanhSachNhanVat_ThamGia_Event_HeroDefend.Values.Count();
	}

	public void Dispose()
	{
		World.Event_13Trieu_Progress = 0;
		if (ThoiGian1 != null)
		{
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			ThoiGian1 = null;
		}
		if (ThoiGian2 != null)
		{
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
			ThoiGian2 = null;
		}
		if (ThoiGian3 != null)
		{
			ThoiGian3.Enabled = false;
			ThoiGian3.Close();
			ThoiGian3.Dispose();
			ThoiGian3 = null;
		}
		if (ThoiGian4 != null)
		{
			ThoiGian4.Enabled = false;
			ThoiGian4.Close();
			ThoiGian4.Dispose();
			ThoiGian4 = null;
		}
		if (ThoiGian5 != null)
		{
			ThoiGian5.Enabled = false;
			ThoiGian5.Close();
			ThoiGian5.Dispose();
			ThoiGian5 = null;
		}
		foreach (var value in World.allConnectedChars.Values)
		{
			if (value.NhanVatToaDo_BanDo == 41001)
			{
				value.Mobile(420f, 1740f, 15f, 101, 1);
			}
			value.GetUpdatedCharacterData(value);
		}
		World.delNpc(41001, 15136);
	}
}
