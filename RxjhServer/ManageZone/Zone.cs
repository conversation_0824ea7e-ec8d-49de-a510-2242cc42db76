﻿using System.Collections.Generic;


namespace RxjhServer.ManageZone;
    public class Zone
    {
        // ID của zone
        public int ID { get; private set; }

        // Chủ sở hữu zone
        public ZoneOwner Owner { get; set;}

        // Danh sách người chơi trong zone
        public List<Players> Players { get; set; } = new List<Players>();

        // Danh sách NPC trong zone
        public List<NpcClass> NPCs { get; set; } = new List<NpcClass>();

        // Danh sách vật phẩm trong zone
        public List<X_Mat_Dat_Vat_Pham_Loai> Items { get; set; } = new List<X_Mat_Dat_Vat_Pham_Loai>();

        // Có phải là zone liên server không
        public bool IsCrossServer { get; set; } = false;

        // Điều kiện truy cập zone (0: Không có điều kiện, 1: Cùng guild, 2: <PERSON><PERSON>ng phái, 3: Cùng party, 4: Chỉ owner)
        public int AccessCondition { get; set; } = 0;

        // ServerID của server chứa zone này (nếu là zone liên server)
        public string OriginServerID { get; set; } = "";

        public Zone(int id)
        {
            ID = id;
            Owner = null;
            OriginServerID = World.ServerID.ToString();
        }

        public void AddPlayer(Players player)
        {
            if (!Players.Contains(player))
            {
                Players.Add(player);
                player.CurrentZone = this;
            }
        }

        public void AddNpc(NpcClass npc)
        {
            if (!NPCs.Contains(npc))
            {
                NPCs.Add(npc);
            }
        }

        public void AddItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            if (!Items.Contains(item))
            {
                Items.Add(item);
            }
        }

        public void RemovePlayer(Players player)
        {
            if (Players.Contains(player))
            {
                Players.Remove(player);
                player.CurrentZone = null;
            }
        }

        public void RemoveNpc(NpcClass npc)
        {
            if (NPCs.Contains(npc))
            {
                NPCs.Remove(npc);
            }
        }

        public void RemoveItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            if (Items.Contains(item))
            {
                Items.Remove(item);
            }
        }

        public int GetPlayerCount()
        {
            return Players.Count;
        }

        public int GetNpcCount()
        {
            return NPCs.Count;
        }

        public int GetItemCount()
        {
            return Items.Count;
        }

        // Kiểm tra xem zone này có nhìn thấy zone khác không
        public bool CanSeeZone(Zone otherZone)
        {
            // Zone 0 có thể nhìn thấy tất cả các zone khác
            if (ID == 0)
                return true;

            // Tất cả các zone đều có thể nhìn thấy zone 0
            if (otherZone.ID == 0)
                return true;

            // Nếu cùng zone, có thể nhìn thấy
            if (ID == otherZone.ID)
                return true;

            // Nếu là zone liên server, kiểm tra điều kiện truy cập
            if (this.IsCrossServer || otherZone.IsCrossServer)
            {
                // Nếu không có điều kiện truy cập, cho phép nhìn thấy
                if (this.AccessCondition == 0 || otherZone.AccessCondition == 0)
                    return true;

                // Các điều kiện khác sẽ được kiểm tra trong ZoneManager.CanAccessZone
                return false;
            }

            // Mặc định không thể nhìn thấy
            return false;
        }

        // Lấy danh sách người chơi mà zone này có thể nhìn thấy
        public List<Players> GetVisiblePlayers(Zone[] allZones)
        {
            List<Players> visiblePlayers = new List<Players>();

            // Thêm người chơi trong zone hiện tại
            visiblePlayers.AddRange(Players);

            // Thêm người chơi từ các zone có thể nhìn thấy
            foreach (var zone in allZones)
            {
                if (zone != this && CanSeeZone(zone))
                {
                    visiblePlayers.AddRange(zone.Players);
                }
            }

            return visiblePlayers;
        }

        // Lấy danh sách NPC mà zone này có thể nhìn thấy
        public List<NpcClass> GetVisibleNpcs(Zone[] allZones)
        {
            List<NpcClass> visibleNpcs = new List<NpcClass>();

            // Thêm NPC trong zone hiện tại
            visibleNpcs.AddRange(NPCs);

            // Thêm NPC từ các zone có thể nhìn thấy
            foreach (var zone in allZones)
            {
                if (zone != this && CanSeeZone(zone))
                {
                    visibleNpcs.AddRange(zone.NPCs);
                }
            }

            return visibleNpcs;
        }

        // Lấy danh sách vật phẩm mà zone này có thể nhìn thấy
        public List<X_Mat_Dat_Vat_Pham_Loai> GetVisibleItems(Zone[] allZones)
        {
            List<X_Mat_Dat_Vat_Pham_Loai> visibleItems = new List<X_Mat_Dat_Vat_Pham_Loai>();

            // Thêm vật phẩm trong zone hiện tại
            visibleItems.AddRange(Items);

            // Thêm vật phẩm từ các zone có thể nhìn thấy
            foreach (var zone in allZones)
            {
                if (zone != this && CanSeeZone(zone))
                {
                    visibleItems.AddRange(zone.Items);
                }
            }

            return visibleItems;
        }
    }

    public class ZoneOwner
    {
        public int PID;

        public int type; // 0 = Player, 1 = Guild

        public int name;
    }
