using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer.ManageZone
{
    public class ZoneManager
    {
        private static ZoneManager _instance;

        // Danh sách tất cả các zone
        private Dictionary<int, Zone> _zones = new Dictionary<int, Zone>();

        // Các khu vực đặc biệt (zone theo phạm vi)
        private List<CircularZone> _circularZones = new List<CircularZone>();

        // Zone mặc định
        public Zone DefaultZone { get; private set; }

        // Singleton
        public static ZoneManager Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new ZoneManager();
                return _instance;
            }
        }

        // Lấy tất cả các zone
        public Zone[] GetAllZones()
        {
            return _zones.Values.ToArray();
        }

        // Lấy tất cả các zone đặc biệt
        public CircularZone[] GetAllCircularZones()
        {
            return _circularZones.ToArray();
        }

        public CircularZone FindCircularZone(int zoneId)
        {
            return _circularZones.FirstOrDefault(z => z.ZoneId == zoneId);
        }

        private ZoneManager()
        {
            // Khởi tạo zone mặc định (zone 0)
            DefaultZone = new Zone(0);
            _zones.Add(0, DefaultZone);
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, "ZoneManager đã được khởi tạo");
        }

        // Tạo zone mới và trả về ID
        public int CreateZone(ZoneOwner owner = null)
        {
            int newId = _zones.Keys.Max() + 1;
            var zone = new Zone(newId);

            if (owner != null)
                zone.Owner = owner;

            _zones.Add(newId, zone);
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã tạo zone mới với ID: {newId}");
            return newId;
        }

        // Lấy zone theo ID
        public Zone GetZone(int zoneId)
        {
            if (_zones.TryGetValue(zoneId, out var zone))
                return zone;

            return null;
        }

        // Xóa zone
        public bool RemoveZone(int zoneId)
        {
            if (zoneId == 0) // Không thể xóa zone mặc định
                return false;

            if (_zones.TryGetValue(zoneId, out var zone))
            {
                // Chuyển tất cả người chơi về zone mặc định
                foreach (var player in zone.Players.ToList())
                {
                    MovePlayerToZone(player, 0);
                }

                // Xóa tất cả NPC
                foreach (var npc in zone.NPCs.ToList())
                {
                    World.delNpc(npc.Rxjh_Map, npc.ID);
                }

                // Xóa tất cả item trên mặt đất
                foreach (var item in zone.Items.ToList())
                {
                    // Xóa item khỏi zone
                    zone.RemoveItem(item);

                    // Xử lý xóa item theo logic game
                    try
                    {
                        // Nếu có phương thức để xóa item, sử dụng nó
                        // Đây chỉ là ví dụ, cần thay thế bằng phương thức thực tế trong game
                        // World.DelMatDatItem(item);

                        // Log xóa item
                        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã xóa item {item.GetType().Name} từ zone {zoneId}");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xóa item: {ex.Message}");
                    }
                }

                // Xóa zone khỏi danh sách
                _zones.Remove(zoneId);

                // Xóa khỏi danh sách zone đặc biệt nếu có
                _circularZones.RemoveAll(z => z.ZoneId == zoneId);

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã xóa zone với ID: {zoneId}");
                return true;
            }

            return false;
        }

        // Tạo zone đặc biệt cho guild boss
        public int CreateGuildBossZone(int guildId, string guildName, int mapId, float centerX, float centerY, float radius)
        {
            // Tạo owner cho zone
            ZoneOwner owner = new ZoneOwner
            {
                PID = guildId,
                type = 1, // 1 = Guild
                name = guildId // dùng guildId
            };

            // Tạo zone mới
            int zoneId = CreateZone(owner);

            // Lấy zone vừa tạo
            Zone zone = GetZone(zoneId);

            // Thiết lập điều kiện truy cập
            zone.AccessCondition = 1; // 1 = Cùng guild

            // Tạo zone đặc biệt hình tròn
            CircularZone circularZone = new CircularZone
            {
                ZoneId = zoneId,
                MapId = mapId,
                CenterX = centerX,
                CenterY = centerY,
                Radius = radius,
                GuildId = guildId,
                GuildName = guildName
            };

            _circularZones.Add(circularZone);

            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã tạo zone đặc biệt cho guild {guildName} với ID: {zoneId}, bán kính: {radius}");

            return zoneId;
        }

        // Tạo zone liên server
        public int CreateCrossServerZone(ZoneOwner owner, int mapId, float centerX, float centerY, float radius, int accessCondition = 0, bool forceCreate = false)
        {
            // Kiểm tra xem đã có zone nào cho owner này chưa
            if (!forceCreate && owner != null)
            {
                // Tìm zone hiện có cho owner
                foreach (var existingZone in _zones.Values)
                {
                    if (existingZone.Owner != null &&
                        existingZone.Owner.type == owner.type &&
                        existingZone.Owner.PID == owner.PID &&
                        existingZone.IsCrossServer)
                    {
                        // Tìm thấy zone hiện có, cập nhật thông tin
                        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Tái sử dụng zone liên server hiện có với ID: {existingZone.ID} cho owner: {owner.PID} (type: {owner.type})");

                        // Cập nhật điều kiện truy cập
                        existingZone.AccessCondition = accessCondition;

                        // Tìm và cập nhật CircularZone tương ứng
                        var existingCircularZone = _circularZones.FirstOrDefault(cz => cz.ZoneId == existingZone.ID);
                        if (existingCircularZone != null)
                        {
                            existingCircularZone.MapId = mapId;
                            existingCircularZone.CenterX = centerX;
                            existingCircularZone.CenterY = centerY;
                            existingCircularZone.Radius = radius;

                            // Gửi thông tin zone đến LoginServer
                            SendZoneInfoToLoginServer(existingZone, existingCircularZone);
                        }
                        else
                        {
                            // Nếu không tìm thấy CircularZone, tạo mới
                            CircularZone newCircularZone = new CircularZone
                            {
                                ZoneId = existingZone.ID,
                                MapId = mapId,
                                CenterX = centerX,
                                CenterY = centerY,
                                Radius = radius,
                                GuildId = owner.type == 1 ? owner.PID : 0,
                                GuildName = owner.type == 1 ? GetGuildName(owner.PID) : ""
                            };

                            _circularZones.Add(newCircularZone);

                            // Gửi thông tin zone đến LoginServer
                            SendZoneInfoToLoginServer(existingZone, newCircularZone);
                        }

                        return existingZone.ID;
                    }
                }
            }

            // Nếu không tìm thấy zone hiện có hoặc forceCreate = true, tạo zone mới
            int zoneId = CreateZone(owner);

            // Lấy zone vừa tạo
            Zone zone = GetZone(zoneId);

            // Thiết lập là zone liên server
            zone.IsCrossServer = true;

            // Thiết lập điều kiện truy cập
            zone.AccessCondition = accessCondition;

            // Tạo zone đặc biệt hình tròn
            CircularZone circularZone = new CircularZone
            {
                ZoneId = zoneId,
                MapId = mapId,
                CenterX = centerX,
                CenterY = centerY,
                Radius = radius,
                GuildId = owner?.type == 1 ? owner.PID : 0,
                GuildName = owner?.type == 1 ? GetGuildName(owner.PID) : ""
            };

            _circularZones.Add(circularZone);

            // Gửi thông tin zone đến LoginServer
            SendZoneInfoToLoginServer(zone, circularZone);

            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã tạo zone liên server mới với ID: {zoneId}, bán kính: {radius}, điều kiện truy cập: {accessCondition}");

            return zoneId;
        }

        // Gửi thông tin zone đến LoginServer
        public void SendZoneInfoToLoginServer(Zone zone, CircularZone circularZone)
        {
            try
            {
                // Tạo chuỗi thông tin zone
                string zoneInfo = $"{zone.ID}|{zone.Owner?.PID ?? 0}|{zone.Owner?.type ?? 0}|{zone.AccessCondition}|{circularZone.MapId}|{circularZone.CenterX}|{circularZone.CenterY}|{circularZone.Radius}|{circularZone.GuildId}";

                // Gửi thông tin zone đến LoginServer
                World.conn.Transmit("CROSS_SERVER_ZONE_CREATE|" + World.ServerID + "|" + zoneInfo);

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi thông tin zone {zone.ID} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi thông tin zone đến LoginServer: {ex.Message}");
            }
        }

        // Lấy tên guild từ ID
        private string GetGuildName(int guildId)
        {
            try
            {
                // Tìm người chơi thuộc guild
                foreach (var player in World.allConnectedChars.Values)
                {
                    if (player.GuildId == guildId && !string.IsNullOrEmpty(player.GuildName))
                    {
                        return player.GuildName;
                    }
                }

                return $"Guild {guildId}";
            }
            catch
            {
                return $"Guild {guildId}";
            }
        }

        // Chuyển NPC vào zone
        public void MoveNpcToZone(NpcClass npc, int zoneId)
        {
            try
            {
                if (npc == null)
                    return;

                // Lấy zone hiện tại của NPC
                var currentZone = npc.CurrentZone;

                // Lấy zone đích
                var targetZone = GetZone(zoneId);
                if (targetZone == null)
                {
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone có ID: {zoneId} để chuyển NPC");
                    return;
                }

                // Nếu đã ở zone này rồi thì không làm gì
                if (currentZone == targetZone)
                {
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"NPC {npc.Name} (ID: {npc.NPC_SessionID}) đã ở Zone {zoneId} rồi");
                    return;
                }

                // Xóa khỏi zone hiện tại
                currentZone?.RemoveNpc(npc);

                // Thêm vào zone mới
                targetZone.AddNpc(npc);
                npc.CurrentZone = targetZone;

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã chuyển NPC {npc.Name} (ID: {npc.NPC_SessionID}) vào Zone {zoneId}");
                // Tạo clone của Npc ở CrossServer
                if (targetZone.IsCrossServer)
                {
                    World.conn.CreateCloneNpc(npc.NPC_SessionID, World.ServerID.ToString(), zoneId,npc);
                }

                // Cập nhật hiển thị NPC cho những người chơi trong zone
                foreach (var player in targetZone.Players)
                {
                    if (player.FindNpcZone(200, npc)) // Giả sử 200 là khoảng cách hiển thị
                    {
                        player.GetReviewScopeNpc();
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"MoveNpcToZone Error: {ex.Message}");
            }
        }

        // Chuyển người chơi vào zone
        public void MovePlayerToZone(Players player, int zoneId)
        {
            try
            {
                if (player == null)
                    return;

                // Lấy zone hiện tại của người chơi
                var currentZone = player.CurrentZone;

                // Lấy zone đích
                var targetZone = GetZone(zoneId);

                // Nếu đã ở zone này rồi thì không làm gì
                if (currentZone == targetZone)
                {
                    return;
                }

                // Xóa khỏi zone hiện tại
                currentZone?.RemovePlayer(player);

                // Thêm vào zone mới
                targetZone.AddPlayer(player);

                // Thông báo cho người chơi
                if (zoneId == 0)
                {
                    player.HeThongNhacNho("Bạn đã trở lại khu vực công cộng");

                }
                else
                {
                    // Thông báo chi tiết về zone
                    string zoneType = targetZone.IsCrossServer ? "liên server" : "đặc biệt";
                    player.HeThongNhacNho($"Bạn đã vào khu vực {zoneType} {zoneId}");

                    // Hiển thị thông tin về chủ sở hữu zone
                    if (targetZone.Owner != null)
                    {
                        string ownerInfo = "";
                        if (targetZone.Owner.type == 0) // Player
                        {
                            var ownerPlayer = FindPlayerById(targetZone.Owner.PID);
                            ownerInfo = ownerPlayer != null ? ownerPlayer.UserName : $"Người chơi ID {targetZone.Owner.PID}";
                        }
                        else if (targetZone.Owner.type == 1) // Guild
                        {
                            ownerInfo = $"Bang hội {GetGuildName(targetZone.Owner.PID)}";
                        }

                        if (!string.IsNullOrEmpty(ownerInfo))
                        {
                            player.HeThongNhacNho($"Khu vực này thuộc về {ownerInfo}");
                        }
                    }

                    // Hiển thị thông tin về điều kiện truy cập
                    if (targetZone.AccessCondition > 0)
                    {
                        string accessConditionText = "";
                        switch (targetZone.AccessCondition)
                        {
                            case 1:
                                accessConditionText = "cùng bang hội";
                                break;
                            case 2:
                                accessConditionText = "cùng phái";
                                break;
                            case 3:
                                accessConditionText = "cùng tổ đội";
                                break;
                            case 4:
                                accessConditionText = "chỉ chủ sở hữu";
                                break;
                        }

                        if (!string.IsNullOrEmpty(accessConditionText))
                        {
                            player.HeThongNhacNho($"Điều kiện truy cập: {accessConditionText}");
                        }
                    }

                    // Nếu là zone liên server, hiển thị thông tin về server gốc
                    if (targetZone.IsCrossServer && !string.IsNullOrEmpty(targetZone.OriginServerID))
                    {
                        player.HeThongNhacNho($"Khu vực này được tạo bởi server {targetZone.OriginServerID}");
                    }
                }

                // Refresh người chơi và các đối tượng xung quanh
                player.GetTheReviewRangePlayers();
                player.GetReviewScopeNpc();
                player.ThuThap_VatPham_Drop_PhamVi();

                // Gửi thông tin di chuyển đến LoginServer nếu là zone liên server
                if (targetZone.IsCrossServer && targetZone.OriginServerID != World.ServerID.ToString())
                {
                    // Gửi thông tin di chuyển đến LoginServer
                    World.conn.SendCrossServerAction(
                        targetZone.ID,
                        "ENTER_ZONE",
                        player.SessionID,
                        player.UserName,
                        player.Player_Level,
                        player.GuildId,
                        player.Player_Zx
                    );
                    // TODO : khi enter zone thì cần gửi thông tin tới các người chơi khác
                    player.GetTheReviewRangePlayersCrossServer();
                }

                // Ghi log
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Người chơi {player.UserName} đã vào zone {zoneId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "MovePlayerToZone Error " + ex.Message);
            }
        }

        // Kiểm tra xem người chơi có nằm trong khu vực zone đặc biệt không
        public void CheckPlayerZonePosition(Players player)
        {
            if (player == null || player.NhanVatToaDo_BanDo <= 0)
                return;

            // Nếu người chơi đang ở zone mặc định
            if (player.CurrentZone == DefaultZone || player.CurrentZone == null)
            {
                // Kiểm tra xem người chơi có nằm trong khu vực của zone đặc biệt nào không
                foreach (var circularZone in _circularZones)
                {
                    if (circularZone.MapId == player.NhanVatToaDo_BanDo)
                    {
                        float distance = CalculateDistance(
                            player.NhanVatToaDo_X, player.NhanVatToaDo_Y,
                            circularZone.CenterX, circularZone.CenterY
                        );

                        if (distance <= circularZone.Radius)
                        {
                            // Lấy zone tương ứng
                            Zone zone = GetZone(circularZone.ZoneId);

                            // Kiểm tra điều kiện truy cập
                            if (CanAccessZone(player, zone))
                            {
                                // Chuyển người chơi vào zone đặc biệt
                                MovePlayerToZone(player, circularZone.ZoneId);

                                // Thông báo cho người chơi
                                if (zone.IsCrossServer)
                                {
                                    player.HeThongNhacNho($"Bạn đã vào khu vực liên server {zone.ID}");
                                    player.UpdateBroadcastCharacterData();
                                    // Hiển thị thông tin về chủ sở hữu zone
                                    if (zone.Owner != null)
                                    {
                                        string ownerInfo = "";
                                        if (zone.Owner.type == 0) // Player
                                        {
                                            var ownerPlayer = FindPlayerById(zone.Owner.PID);
                                            ownerInfo = ownerPlayer != null ? ownerPlayer.UserName : $"Người chơi ID {zone.Owner.PID}";
                                        }
                                        else if (zone.Owner.type == 1) // Guild
                                        {
                                            ownerInfo = $"Bang hội {GetGuildName(zone.Owner.PID)}";
                                        }

                                        player.HeThongNhacNho($"Khu vực này thuộc về {ownerInfo}");
                                    }
                                }

                                break;
                            }
                            else
                            {
                                // Thông báo cho người chơi về điều kiện truy cập
                                string accessConditionText = "";
                                switch (zone.AccessCondition)
                                {
                                    case 0:
                                        accessConditionText = "không có điều kiện";
                                        break;
                                    case 1:
                                        accessConditionText = "cùng bang hội";
                                        break;
                                    case 2:
                                        accessConditionText = "cùng phái";
                                        break;
                                    case 3:
                                        accessConditionText = "cùng tổ đội";
                                        break;
                                    case 4:
                                        accessConditionText = "chỉ chủ sở hữu";
                                        break;
                                }

                                player.HeThongNhacNho($"Bạn không đủ điều kiện để vào khu vực này. Điều kiện: {accessConditionText}");
                            }
                        }
                    }
                }
            }
            else // Nếu người chơi đang ở zone đặc biệt
            {
                // Kiểm tra xem người chơi có còn trong khu vực của zone không
                var circularZone = _circularZones.FirstOrDefault(z => z.ZoneId == player.CurrentZone.ID);
                if (circularZone != null)
                {
                    if (circularZone.MapId != player.NhanVatToaDo_BanDo)
                    {
                        // Nếu người chơi đã rời khỏi map, chuyển về zone mặc định
                        MovePlayerToZone(player, 0);
                        player.HeThongNhacNho("Bạn đã rời khỏi khu vực đặc biệt");
                        // TODO:
                        // Gửi broadcast đến LoginServer để thông báo người chơi đã rời khỏi zone
                        player.RemovePlayerCrossServer();
                    }
                    else
                    {
                        float distance = CalculateDistance(
                            player.NhanVatToaDo_X, player.NhanVatToaDo_Y,
                            circularZone.CenterX, circularZone.CenterY
                        );

                        if (distance > circularZone.Radius)
                        {
                            // Nếu người chơi đã rời khỏi phạm vi, chuyển về zone mặc định
                            MovePlayerToZone(player, 0);
                            player.HeThongNhacNho("Bạn đã rời khỏi khu vực đặc biệt");
                            player.RemovePlayerCrossServer();
                        }
                        else
                        {
                            // Kiểm tra lại điều kiện truy cập
                            Zone zone = GetZone(circularZone.ZoneId);
                            if (!CanAccessZone(player, zone))
                            {
                                // Nếu không đủ điều kiện, chuyển về zone mặc định
                                MovePlayerToZone(player, 0);

                                // Thông báo cho người chơi về điều kiện truy cập
                                string accessConditionText = "";
                                switch (zone.AccessCondition)
                                {
                                    case 0:
                                        accessConditionText = "không có điều kiện";
                                        break;
                                    case 1:
                                        accessConditionText = "cùng bang hội";
                                        break;
                                    case 2:
                                        accessConditionText = "cùng phái";
                                        break;
                                    case 3:
                                        accessConditionText = "cùng tổ đội";
                                        break;
                                    case 4:
                                        accessConditionText = "chỉ chủ sở hữu";
                                        break;
                                }

                                player.HeThongNhacNho($"Bạn không đủ điều kiện để ở trong khu vực này. Điều kiện: {accessConditionText}");
                            }
                        }
                    }
                }
            }
        }

        // Kiểm tra xem người chơi có thể truy cập zone không
        public bool CanAccessZone(Players player, Zone zone)
        {
            if (player == null || zone == null)
                return false;

            // Zone mặc định luôn có thể truy cập
            if (zone.ID == 0)
                return true;

            // Nếu là chủ sở hữu zone, luôn có thể truy cập
            if (zone.Owner != null)
            {
                if (zone.Owner.type == 0 && zone.Owner.PID == player.SessionID) // Player owner
                    return true;

                if (zone.Owner.type == 1 && zone.Owner.PID == player.GuildId && player.GuildId > 0) // Guild owner
                    return true;
            }

            // Kiểm tra điều kiện truy cập
            switch (zone.AccessCondition)
            {
                case 0: // Không có điều kiện
                    return true;

                case 1: // Cùng guild
                    if (zone.Owner != null && zone.Owner.type == 1)
                        return player.GuildId > 0 && player.GuildId == zone.Owner.PID;
                    break;

                case 2: // Cùng phái
                    if (zone.Owner != null)
                    {
                        // Tìm người chơi là chủ sở hữu
                        Players owner = null;
                        if (zone.Owner.type == 0)
                        {
                            owner = FindPlayerById(zone.Owner.PID);
                        }
                        else if (zone.Owner.type == 1)
                        {
                            // Tìm một người chơi trong guild
                            owner = World.allConnectedChars.Values.FirstOrDefault(p => p.GuildId == zone.Owner.PID);
                        }

                        if (owner != null)
                            return player.Player_Zx == owner.Player_Zx;
                    }
                    break;

                case 3: // Cùng party
                    //if (player.Party_ID > 0)
                    //{
                    //    // Kiểm tra xem có người chơi nào trong party đang ở trong zone không
                    //    foreach (var p in zone.Players)
                    //    {
                    //        if (p.Party_ID > 0 && p.Party_ID == player.Party_ID)
                    //            return true;
                    //    }
                    //}
                    break;

                case 4: // Chỉ owner
                    return false; // Đã kiểm tra ở trên

                default:
                    return false;
            }

            return false;
        }

        // Tìm người chơi theo ID
        private Players FindPlayerById(int playerId)
        {
            return World.allConnectedChars.Values.FirstOrDefault(p => p.SessionID == playerId);
        }

        // Hàm lấy GuildID của người chơi
        private int GetPlayerGuildId(Players player)
        {
            // Đây là phương thức trích xuất Guild ID từ đối tượng player
            // Cần điều chỉnh theo cấu trúc thực tế của Players class
            try
            {
                // Giả sử property có thể là Guild_ID, GuildID, GuildId, etc.
                var guildIdProperty = player.GetType().GetProperty("Guild_ID") ??
                                      player.GetType().GetProperty("GuildID") ??
                                      player.GetType().GetProperty("GuildId");

                if (guildIdProperty != null)
                {
                    return (int)guildIdProperty.GetValue(player);
                }

                // Trường hợp không tìm thấy property
                return -1;
            }
            catch
            {
                return -1; // Trả về -1 nếu không tìm được Guild ID
            }
        }

        // Tìm người chơi trong tất cả các zone theo UserName
        public Players FindPlayerByName(string userName)
        {
            foreach (var zone in _zones.Values)
            {
                var player = zone.Players.FirstOrDefault(p => p.UserName == userName);
                if (player != null)
                    return player;
            }

            return null;
        }

        // Kiểm tra tương tác giữa người chơi
        public bool CanInteract(Players source, Players target)
        {
            if (source == null || target == null)
                return false;

            // Nếu một trong hai ở zone mặc định, có thể nhìn thấy nhưng không tương tác
            if (source.CurrentZone == DefaultZone || target.CurrentZone == DefaultZone)
                return false;

            // Nếu cùng zone, có thể tương tác
            if (source.CurrentZone == target.CurrentZone)
                return true;

            // Mặc định không thể tương tác
            return false;
        }

        // Kiểm tra tương tác với NPC
        public bool CanInteractWithNpc(Players source, NpcClass npc)
        {
            if (source == null || npc == null)
                return false;

            // Nếu là zone mặc định, có thể tương tác với mọi NPC
            if (source.CurrentZone == DefaultZone)
                return true;

            Zone npcZone = GetNpcZone(npc);

            // Nếu NPC ở zone mặc định, bất kỳ ai cũng có thể tương tác
            if (npcZone == DefaultZone)
                return true;

            // Nếu cùng zone, có thể tương tác
            if (source.CurrentZone == npcZone)
                return true;

            // Mặc định không thể tương tác
            return false;
        }

        // Lấy zone chứa NPC
        private Zone GetNpcZone(NpcClass npc)
        {
            foreach (var zone in _zones.Values)
            {
                if (zone.NPCs.Contains(npc))
                    return zone;
            }

            return DefaultZone;
        }

        // Triển khai spawn Guild Boss vào zone riêng
        public void SpawnGuildBoss(int guildId, string guildName, int mapId, float x, float y, int bossId = 15423, int durationMinutes = 30)
        {
            try
            {
                // Tạo zone cho guild
                float radius = 500f; // Bán kính 500 đơn vị
                int zoneId = CreateGuildBossZone(guildId, guildName, mapId, x, y, radius);

                // Lấy zone
                Zone guildZone = GetZone(zoneId);

                // Spawn boss
                NpcClass boss = World.AddNpcNeo(
                    bossId,
                    x, y,
                    mapId,
                    $"Boss Bang Hội {guildName}",
                    0, 0, 0, true, 0,
                    durationMinutes * 60 * 1000);

                // Thêm boss vào zone
                MoveNpcToZone(boss, zoneId);

                // Khởi tạo boss
                boss.InitializeAsGuildBoss(durationMinutes);

                // Đưa tất cả thành viên guild vào zone
                foreach (var player in World.allConnectedChars.Values)
                {
                    // Lấy Guild ID của người chơi
                    int playerGuildId = GetPlayerGuildId(player);

                    if (playerGuildId == guildId)
                    {
                        // Thông báo cho người chơi
                        player.HeThongNhacNho($"Boss Bang Hội {guildName} đã được triệu hồi. Hãy đến tọa độ ({x}, {y}) ở bản đồ {mapId} để tham gia!");

                        // Nếu người chơi ở gần boss, đưa vào zone
                        if (player.NhanVatToaDo_BanDo == mapId)
                        {
                            float distance = CalculateDistance(player.NhanVatToaDo_X, player.NhanVatToaDo_Y, x, y);
                            if (distance <= radius)
                            {
                                MovePlayerToZone(player, zoneId);
                            }
                        }
                    }
                }

                // Thông báo toàn server
                foreach (var player in World.allConnectedChars.Values)
                {
                    player.HeThongNhacNho($"Bang hội {guildName} đã triệu hồi Boss Bang Hội. Bạn có thể đến xem nhưng không thể tấn công!");
                }

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã spawn Guild Boss cho bang hội {guildName} tại map {mapId} vị trí ({x}, {y})");

                // Tự động xóa zone khi boss chết hoặc hết thời gian
                System.Threading.Tasks.Task.Delay(durationMinutes * 60 * 1000 + 300000).ContinueWith(_ => {
                    // Xóa zone sau khi boss chết 5 phút
                    RemoveZone(zoneId);
                });
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi spawn Guild Boss: {ex.Message}");
            }
        }

        /// <summary>
        /// Triển khai spawn Cross Server Boss
        /// </summary>
        /// <param name="ownerId">ID của chủ sở hữu</param>
        /// <param name="ownerType">Loại chủ sở hữu (0 = Player, 1 = Guild)</param>
        /// <param name="mapId">ID của bản đồ</param>
        /// <param name="x">Tọa độ X</param>
        /// <param name="y">Tọa độ Y</param>
        /// <param name="bossId">ID của boss</param>
        /// <param name="durationMinutes">Thời gian tồn tại (phút)</param>
        public void SpawnCrossServerBoss(int ownerId, int ownerType, int mapId, float x, float y, int bossId = 15423, int durationMinutes = 30, int accessCondition = 0)
        {
            try
            {
                // Tạo owner cho zone
                ZoneOwner owner = new()
                {
                    PID = ownerId,
                    type = ownerType, // 0 = Player, 1 = Guild
                    name = ownerId
                };

                // Tạo zone liên server
                float radius = 500f; // Bán kính 500 đơn vị
                int zoneId = CreateCrossServerZone(owner, mapId, x, y, radius, accessCondition);

                // Lấy zone
                Zone crossServerZone = GetZone(zoneId);

                // Spawn boss
                NpcClass boss = World.AddNpcNeo(
                    bossId,
                    x, y,
                    mapId,
                    $"Boss Liên Server",
                    0, 0, 0, true, 0,
                    durationMinutes * 60 * 1000);

                // Thêm boss vào zone
                MoveNpcToZone(boss, zoneId);

                // Khởi tạo boss
                boss.InitializeAsWorldBoss(durationMinutes);

                // Thông báo cho người chơi phù hợp
                string ownerName = "";
                string accessConditionText = "";

                switch (accessCondition)
                {
                    case 0:
                        accessConditionText = "tất cả người chơi";
                        break;
                    case 1:
                        ownerName = ownerType == 1 ? GetGuildName(ownerId) : "";
                        accessConditionText = $"thành viên bang hội {ownerName}";
                        break;
                    case 2:
                        accessConditionText = "người chơi cùng phái";
                        break;
                    case 3:
                        accessConditionText = "thành viên cùng tổ đội";
                        break;
                    case 4:
                        accessConditionText = "chỉ chủ sở hữu";
                        break;
                }

                // Thông báo toàn server
                foreach (var player in World.allConnectedChars.Values)
                {
                    if (CanAccessZone(player, crossServerZone))
                    {
                        player.HeThongNhacNho($"Boss Liên Server đã xuất hiện tại tọa độ ({x}, {y}) ở bản đồ {mapId}. Hãy đến tham gia!");
                    }
                    else
                    {
                        player.HeThongNhacNho($"Boss Liên Server đã xuất hiện tại tọa độ ({x}, {y}) ở bản đồ {mapId}. Chỉ {accessConditionText} mới có thể tham gia!");
                    }
                }

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã spawn Cross Server Boss tại map {mapId} vị trí ({x}, {y})");

                // Tự động xóa zone khi boss chết hoặc hết thời gian
                System.Threading.Tasks.Task.Delay(durationMinutes * 60 * 1000 + 300000).ContinueWith(_ => {
                    // Xóa zone sau khi boss chết 5 phút
                    RemoveZone(zoneId);

                    // Thông báo xóa zone đến LoginServer
                    World.conn.Transmit("CROSS_SERVER_ZONE_REMOVE|" + World.ServerID + "|" + zoneId);
                });
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi spawn Cross Server Boss: {ex.Message}");
            }
        }

        // Hàm tính khoảng cách
        private float CalculateDistance(float x1, float y1, float x2, float y2)
        {
            return (float)Math.Sqrt(Math.Pow(x2 - x1, 2) + Math.Pow(y2 - y1, 2));
        }

        // Khởi tạo ZoneManager
        public void Initialize()
        {
            // Xóa tất cả các zone (trừ zone mặc định nếu đã tồn tại)
            List<int> zoneIdsToRemove = _zones.Keys.Where(id => id != 0).ToList();
            foreach (var zoneId in zoneIdsToRemove)
            {
                RemoveZone(zoneId);
            }

            // Tạo lại zone mặc định nếu chưa có
            if (!_zones.ContainsKey(0))
            {
                DefaultZone = new Zone(0);
                _zones.Add(0, DefaultZone);
            }

            // Xóa tất cả các zone đặc biệt
            _circularZones.Clear();

          
        }

        // Khởi tạo NPC với zone mặc định
        public void InitializeNpcDefaultZone(NpcClass npc)
        {
            try
            {
                if (npc == null)
                    return;

                // Thiết lập zone mặc định cho NPC
                DefaultZone.AddNpc(npc);
                npc.CurrentZone = DefaultZone;

               // LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã đặt Zone mặc định cho NPC {npc.Name} (ID: {npc.NPC_SessionID})");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"InitializeNpcDefaultZone Error: {ex.Message}");
            }
        }
    }

    // Class mô tả khu vực hình tròn
    public class CircularZone
    {
        public int ZoneId { get; set; }
        public int MapId { get; set; }
        public float CenterX { get; set; }
        public float CenterY { get; set; }
        public float Radius { get; set; }
        public int GuildId { get; set; }
        public string GuildName { get; set; }

        internal static object FirstOrDefault(Func<object, bool> value)
        {
            throw new NotImplementedException();
        }
    }
}