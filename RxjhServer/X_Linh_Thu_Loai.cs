using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using HeroYulgang.Core.Network;
using Microsoft.Data.SqlClient;
using RxjhServer.Database;
using RxjhServer.Network;

namespace RxjhServer;

public class X_Linh_Thu_Loai : IDisposable
{
	private object thisLock = new();

	public X_Vo_Cong_Loai[,] VoCongMoi = new X_Vo_Cong_Loai[2, 17];

	private float _NhanVatToaDo_X;

	private float _NhanVatToaDo_Y;

	private float _NhanVatToaDo_Z;

	private int _NhanVatToaDo_MAP;

	private long _Id;

	private int _Bs;

	private string _ZrName;

	private string _Name;

	private int _FLD_ZCD;

	private long _FLD_EXP;

	private long _LonNhatKinhNghiem;

	private long _FLD_EXP_MAX;

	private int _FLD_LEVEL;

	private int _FLD_JOB;

	private int _FLD_JOB_LEVEL;

	private int _FLD_HP;

	private int _FLD_HP_MAX;

	private int _FLD_MP;

	private int _FLD_MP_MAX;

	private int _FLD_VatPham_ThemVao_HP;

	private int _FLD_VatPham_ThemVao_MP;

	private int _FLD_TrongLuong;

	private int _FLD_TrongLuong_MAX;

	private int _FLD_CongKich;

	private int _FLD_PhongNgu;

	private int _FLD_TrungDich;

	private int _FLD_NeTranh;

	private int _CuoiThu;

	private int _ThuBay;

	private int _FLD_TrangBi_ThemVao_CongKich;

	private int _FLD_TrangBi_ThemVao_PhongNgu;

	private int _FLD_TrangBi_ThemVao_TrungDich;

	private int _FLD_TrangBi_ThemVao_NeTranh;

	private int _FLD_TrangBi_ThemVao_HP;

	private int _FLD_TrangBi_ThemVao_MP;

	private int _TTTP_Tang_HP;

	private int _TTTP_Tang_MP;

	private int _TTTP_Tang_HP_NEW;

	private int _TTTP_Tang_MP_NEW;

	private double _FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram;

	private double _FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram;

	private double _FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram;

	private double _FLD_ThemVaoTiLePhanTram_CongKich;

	private double _FLD_DoThanThemVao_PhanTramTanCong;

	private double _FLD_CamSu_HieuUng_SieuAmThanh_DameDef;

	private double _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1022;

	private double _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1023;

	private double _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1027;

	private double _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1028;

	private double _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1480;

	private double _FLD_ThemVaoTiLePhanTram_PhongNgu;

	private double _FLD_ThemVao_PhanTram_Def_HoanVuVanHoa;

	private double _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022;

	private double _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023;

	private double _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027;

	private double _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028;

	private double _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480;

	private double _FLD_ThemVaoTiLePhanTram_TrungDich;

	private double _FLD_ThemVaoTiLePhanTram_NeTranh;

	private double _FLD_ThemVaoTiLePhanTram_HPCaoNhat;

	private double _FLD_ThemVaoTiLePhanTram_MPCaoNhat;

	private double _FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram;

	private double _FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram;

	private double _FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem;

	private int _FLD_MAGIC1;

	private int _FLD_MAGIC2;

	private int _FLD_MAGIC3;

	private int _FLD_MAGIC4;

	private int _FLD_MAGIC5;

	public int FullServiceID;

	public X_Vat_Pham_Loai[] ThuCung_Thanh_TrangBi;

	public X_Vat_Pham_Loai[] ThuCungVaTrangBi;

	public Players Playe;

	private ActorNetState client;

	public List<X_Cong_Kich_Loai> AttackList;

	public bool _TuVong;

	public float NhanVatToaDo_X
	{
		get
		{
			return _NhanVatToaDo_X;
		}
		set
		{
			_NhanVatToaDo_X = value;
		}
	}

	public float NhanVatToaDo_Y
	{
		get
		{
			return _NhanVatToaDo_Y;
		}
		set
		{
			_NhanVatToaDo_Y = value;
		}
	}

	public float NhanVatToaDo_Z
	{
		get
		{
			return _NhanVatToaDo_Z;
		}
		set
		{
			_NhanVatToaDo_Z = value;
		}
	}

	public int NhanVatToaDo_MAP
	{
		get
		{
			return _NhanVatToaDo_MAP;
		}
		set
		{
			_NhanVatToaDo_MAP = value;
		}
	}

	public long Id
	{
		get
		{
			return _Id;
		}
		set
		{
			_Id = value;
		}
	}

	public int Bs
	{
		get
		{
			return _Bs;
		}
		set
		{
			_Bs = value;
		}
	}

	public string ZrName
	{
		get
		{
			return _ZrName;
		}
		set
		{
			_ZrName = value;
		}
	}

	public string Name
	{
		get
		{
			return _Name;
		}
		set
		{
			_Name = value;
		}
	}

	public int FLD_ZCD
	{
		get
		{
			return _FLD_ZCD;
		}
		set
		{
			_FLD_ZCD = value;
		}
	}

	public long FLD_EXP
	{
		get
		{
			return _FLD_EXP;
		}
		set
		{
			_FLD_EXP = value;
		}
	}

	public long LonNhatKinhNghiem
	{
		get
		{
			return _LonNhatKinhNghiem;
		}
		set
		{
			_LonNhatKinhNghiem = value;
		}
	}

	public long FLD_EXP_MAX
	{
		get
		{
			return _FLD_EXP_MAX;
		}
		set
		{
			_FLD_EXP_MAX = value;
		}
	}

	public int FLD_LEVEL
	{
		get
		{
			return _FLD_LEVEL;
		}
		set
		{
			_FLD_LEVEL = value;
		}
	}

	public int FLD_JOB
	{
		get
		{
			return _FLD_JOB;
		}
		set
		{
			_FLD_JOB = value;
		}
	}

	public int FLD_JOB_LEVEL
	{
		get
		{
			return _FLD_JOB_LEVEL;
		}
		set
		{
			_FLD_JOB_LEVEL = value;
		}
	}

	public int FLD_HP
	{
		get
		{
			return _FLD_HP;
		}
		set
		{
			_FLD_HP = value;
		}
	}

	public int FLD_HP_MAX
	{
		get
		{
			return _FLD_HP_MAX;
		}
		set
		{
			_FLD_HP_MAX = value;
		}
	}

	public int FLD_MP
	{
		get
		{
			return _FLD_MP;
		}
		set
		{
			_FLD_MP = value;
		}
	}

	public int FLD_MP_MAX
	{
		get
		{
			return _FLD_MP_MAX;
		}
		set
		{
			_FLD_MP_MAX = value;
		}
	}

	public int FLD_VatPham_ThemVao_HP
	{
		get
		{
			return _FLD_VatPham_ThemVao_HP;
		}
		set
		{
			_FLD_VatPham_ThemVao_HP = value;
		}
	}

	public int FLD_VatPham_ThemVao_MP
	{
		get
		{
			return _FLD_VatPham_ThemVao_MP;
		}
		set
		{
			_FLD_VatPham_ThemVao_MP = value;
		}
	}

	public int Beast_BasicallyTheLargest_HP => (int)((FLD_HP_MAX + FLD_VatPham_ThemVao_HP + TTTP_Tang_HP + TTTP_Tang_HP_NEW + FLD_TrangBi_ThemVao_HP) * (1.0 + FLD_ThemVaoTiLePhanTram_HPCaoNhat));

	public int Beast_BasicallyTheLargest_MP => (int)((FLD_MP_MAX + FLD_VatPham_ThemVao_MP + TTTP_Tang_MP + TTTP_Tang_MP_NEW + FLD_TrangBi_ThemVao_MP) * (1.0 + FLD_ThemVaoTiLePhanTram_MPCaoNhat));

	public int SpiritBeastCoBanCongKich => (int)((FLD_CongKich + FLD_TrangBi_ThemVao_CongKich) * (1.0 + FLD_ThemVaoTiLePhanTram_CongKich));

	public int SpiritBeastCoBanPhongNgu => (int)((FLD_PhongNgu + FLD_TrangBi_ThemVao_PhongNgu) * (1.0 + FLD_ThemVaoTiLePhanTram_PhongNgu));

	public int SpiritBeastCoBanTrungDich => (int)((FLD_TrungDich + FLD_TrangBi_ThemVao_TrungDich) * (1.0 + FLD_ThemVaoTiLePhanTram_TrungDich));

	public int SpiritBeastCoBanNeTranh => (int)((FLD_NeTranh + FLD_TrangBi_ThemVao_NeTranh) * (1.0 + FLD_ThemVaoTiLePhanTram_NeTranh));

	public int FLD_TrongLuong
	{
		get
		{
			return _FLD_TrongLuong;
		}
		set
		{
			_FLD_TrongLuong = value;
		}
	}

	public int FLD_TrongLuong_MAX
	{
		get
		{
			return _FLD_TrongLuong_MAX;
		}
		set
		{
			_FLD_TrongLuong_MAX = value;
		}
	}

	public int FLD_CongKich
	{
		get
		{
			return _FLD_CongKich;
		}
		set
		{
			_FLD_CongKich = value;
		}
	}

	public int FLD_PhongNgu
	{
		get
		{
			return _FLD_PhongNgu;
		}
		set
		{
			_FLD_PhongNgu = value;
		}
	}

	public int FLD_TrungDich
	{
		get
		{
			return _FLD_TrungDich;
		}
		set
		{
			_FLD_TrungDich = value;
		}
	}

	public int FLD_NeTranh
	{
		get
		{
			return _FLD_NeTranh;
		}
		set
		{
			_FLD_NeTranh = value;
		}
	}

	public int CuoiThu
	{
		get
		{
			return _CuoiThu;
		}
		set
		{
			_CuoiThu = value;
		}
	}

	public int ThuBay
	{
		get
		{
			return _ThuBay;
		}
		set
		{
			_ThuBay = value;
		}
	}

	public int FLD_TrangBi_ThemVao_CongKich
	{
		get
		{
			return _FLD_TrangBi_ThemVao_CongKich;
		}
		set
		{
			_FLD_TrangBi_ThemVao_CongKich = value;
		}
	}

	public int FLD_TrangBi_ThemVao_PhongNgu
	{
		get
		{
			return _FLD_TrangBi_ThemVao_PhongNgu;
		}
		set
		{
			_FLD_TrangBi_ThemVao_PhongNgu = value;
		}
	}

	public int FLD_TrangBi_ThemVao_TrungDich
	{
		get
		{
			return _FLD_TrangBi_ThemVao_TrungDich;
		}
		set
		{
			_FLD_TrangBi_ThemVao_TrungDich = value;
		}
	}

	public int FLD_TrangBi_ThemVao_NeTranh
	{
		get
		{
			return _FLD_TrangBi_ThemVao_NeTranh;
		}
		set
		{
			_FLD_TrangBi_ThemVao_NeTranh = value;
		}
	}

	public int FLD_TrangBi_ThemVao_HP
	{
		get
		{
			return _FLD_TrangBi_ThemVao_HP;
		}
		set
		{
			_FLD_TrangBi_ThemVao_HP = value;
		}
	}

	public int FLD_TrangBi_ThemVao_MP
	{
		get
		{
			return _FLD_TrangBi_ThemVao_MP;
		}
		set
		{
			_FLD_TrangBi_ThemVao_MP = value;
		}
	}

	public int TTTP_Tang_HP
	{
		get
		{
			return _TTTP_Tang_HP;
		}
		set
		{
			_TTTP_Tang_HP = value;
		}
	}

	public int TTTP_Tang_MP
	{
		get
		{
			return _TTTP_Tang_MP;
		}
		set
		{
			_TTTP_Tang_MP = value;
		}
	}

	public int TTTP_Tang_HP_NEW
	{
		get
		{
			return _TTTP_Tang_HP_NEW;
		}
		set
		{
			_TTTP_Tang_HP_NEW = value;
		}
	}

	public int TTTP_Tang_MP_NEW
	{
		get
		{
			return _TTTP_Tang_MP_NEW;
		}
		set
		{
			_TTTP_Tang_MP_NEW = value;
		}
	}

	public double FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram
	{
		get
		{
			return _FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram;
		}
		set
		{
			_FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram = value;
		}
	}

	public double FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram
	{
		get
		{
			return _FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram;
		}
		set
		{
			_FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram = value;
		}
	}

	public double FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram
	{
		get
		{
			return _FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram;
		}
		set
		{
			_FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_CongKich;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_CongKich = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1022
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1022;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1022 = value;
		}
	}

	public double FLD_DoThanThemVao_PhanTramTanCong
	{
		get
		{
			return _FLD_DoThanThemVao_PhanTramTanCong;
		}
		set
		{
			_FLD_DoThanThemVao_PhanTramTanCong = value;
		}
	}

	public double FLD_CamSu_HieuUng_SieuAmThanh_DameDef
	{
		get
		{
			return _FLD_CamSu_HieuUng_SieuAmThanh_DameDef;
		}
		set
		{
			_FLD_CamSu_HieuUng_SieuAmThanh_DameDef = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1023
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1023;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1023 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1027
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1027;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1027 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1028
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1028;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1028 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1480
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1480;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1480 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_PhongNgu;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_PhongNgu = value;
		}
	}

	public double FLD_ThemVao_PhanTram_Def_HoanVuVanHoa
	{
		get
		{
			return _FLD_ThemVao_PhanTram_Def_HoanVuVanHoa;
		}
		set
		{
			_FLD_ThemVao_PhanTram_Def_HoanVuVanHoa = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480 = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_TrungDich
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_TrungDich;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_TrungDich = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_NeTranh
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_NeTranh;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_NeTranh = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_HPCaoNhat
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_HPCaoNhat;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_HPCaoNhat = value;
		}
	}

	public double FLD_ThemVaoTiLePhanTram_MPCaoNhat
	{
		get
		{
			return _FLD_ThemVaoTiLePhanTram_MPCaoNhat;
		}
		set
		{
			_FLD_ThemVaoTiLePhanTram_MPCaoNhat = value;
		}
	}

	public double FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram
	{
		get
		{
			return _FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram;
		}
		set
		{
			_FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram = value;
		}
	}

	public double FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram
	{
		get
		{
			return _FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram;
		}
		set
		{
			_FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram = value;
		}
	}

	public double FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem
	{
		get
		{
			return _FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem;
		}
		set
		{
			_FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return _FLD_MAGIC1;
		}
		set
		{
			_FLD_MAGIC1 = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return _FLD_MAGIC2;
		}
		set
		{
			_FLD_MAGIC2 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return _FLD_MAGIC3;
		}
		set
		{
			_FLD_MAGIC3 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return _FLD_MAGIC4;
		}
		set
		{
			_FLD_MAGIC4 = value;
		}
	}

	public int FLD_MAGIC5
	{
		get
		{
			return _FLD_MAGIC5;
		}
		set
		{
			_FLD_MAGIC5 = value;
		}
	}

	public bool TuVong
	{
		get
		{
			if (FLD_HP <= 0)
			{
				_TuVong = false;
			}
			return _TuVong;
		}
		set
		{
			_TuVong = value;
		}
	}

	public void addFLD_ThemVaoTiLePhanTram_Attack(double i)
	{
		using (new Lock(thisLock, "addFLD_ThemVaoTiLePhanTram_CongKich"))
		{
			FLD_ThemVaoTiLePhanTram_CongKich += i;
		}
	}

	public void dllFLD_ThemVaoTiLePhanTram_Attack(double i)
	{
		using (new Lock(thisLock, "dllFLD_ThemVaoTiLePhanTram_CongKich"))
		{
			FLD_ThemVaoTiLePhanTram_CongKich -= i;
		}
	}

	public void addFLD_ThemVaoTiLePhanTram_PhongNgu(double i)
	{
		using (new Lock(thisLock, "addFLD_ThemVaoTiLePhanTram_PhongNgu"))
		{
			FLD_ThemVaoTiLePhanTram_PhongNgu += i;
		}
	}

	public void dllFLD_ThemVaoTiLePhanTram_PhongNgu(double i)
	{
		using (new Lock(thisLock, "dllFLD_ThemVaoTiLePhanTram_PhongNgu"))
		{
			FLD_ThemVaoTiLePhanTram_PhongNgu -= i;
		}
	}

	~X_Linh_Thu_Loai()
	{
	}

	public void Dispose()
	{
		Playe = null;
		client = null;
	}

	public bool LookInNpc(int far_, NpcClass Npc)
	{
		if (Npc.Rxjh_Map != NhanVatToaDo_MAP)
		{
			return false;
		}
		var num = Npc.Rxjh_X - NhanVatToaDo_X;
		var num2 = Npc.Rxjh_Y - NhanVatToaDo_Y;
		return (int)Math.Sqrt((double)num * (double)num + (double)num2 * (double)num2) <= (double)far_;
	}

	public X_Linh_Thu_Loai(long id, ActorNetState clien, DataTable table2, Players Playe)
	{
		this.Playe = Playe;
		client = clien;
		_Id = id;
		ZrName = table2.Rows[0]["ZrName"].ToString();
		Name = table2.Rows[0]["Name"].ToString();
		FLD_ZCD = (int)table2.Rows[0]["FLD_ZCD"];
		FLD_EXP = long.Parse(table2.Rows[0]["FLD_EXP"].ToString());
		FLD_LEVEL = (int)table2.Rows[0]["FLD_LEVEL"];
		FLD_JOB = (int)table2.Rows[0]["FLD_JOB"];
		FLD_JOB_LEVEL = (int)table2.Rows[0]["FLD_JOB_LEVEL"];
		FLD_HP = (int)table2.Rows[0]["FLD_HP"];
		FLD_MP = (int)table2.Rows[0]["FLD_MP"];
		Bs = (int)table2.Rows[0]["FLD_BS"];
		FLD_MAGIC1 = (int)table2.Rows[0]["FLD_MAGIC1"];
		FLD_MAGIC2 = (int)table2.Rows[0]["FLD_MAGIC2"];
		FLD_MAGIC3 = (int)table2.Rows[0]["FLD_MAGIC3"];
		FLD_MAGIC4 = (int)table2.Rows[0]["FLD_MAGIC4"];
		FLD_MAGIC5 = (int)table2.Rows[0]["FLD_MAGIC5"];
		FLD_EXP_MAX = 100000L;
		FLD_CongKich = 1000;
		FLD_PhongNgu = 1000;
		FLD_TrungDich = 1000;
		FLD_NeTranh = 1000;
		FLD_TrongLuong = 0;
		FLD_TrongLuong_MAX = 100;
		FLD_VatPham_ThemVao_HP = 0;
		FLD_VatPham_ThemVao_MP = 0;
		FLD_TrangBi_ThemVao_CongKich = 0;
		FLD_TrangBi_ThemVao_PhongNgu = 0;
		FLD_TrangBi_ThemVao_TrungDich = 0;
		FLD_TrangBi_ThemVao_NeTranh = 0;
		FLD_TrangBi_ThemVao_HP = 0;
		FLD_TrangBi_ThemVao_MP = 0;
		TTTP_Tang_HP = 0;
		TTTP_Tang_MP = 0;
		TTTP_Tang_HP_NEW = 0;
		TTTP_Tang_MP_NEW = 0;
		FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
		FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
		FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
		FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
		FLD_ThemVaoTiLePhanTram_NeTranh = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich = 0.0;
		FLD_CamSu_HieuUng_SieuAmThanh_DameDef = 0.0;
		FLD_DoThanThemVao_PhanTramTanCong = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1022 = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1023 = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1027 = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1028 = 0.0;
		FLD_ThemVaoTiLePhanTram_CongKich_TTTP_1480 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu = 0.0;
		FLD_ThemVao_PhanTram_Def_HoanVuVanHoa = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1022 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1023 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1027 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1028 = 0.0;
		FLD_ThemVaoTiLePhanTram_PhongNgu_TTTP_1480 = 0.0;
		FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
		FLD_ThemVaoTiLePhanTram_MPCaoNhat = 0.0;
		FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
		FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
		FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem = 0.0;
		ThuCung_Thanh_TrangBi = new X_Vat_Pham_Loai[16];
		ThuCungVaTrangBi = new X_Vat_Pham_Loai[5];
		VoCongMoi = new X_Vo_Cong_Loai[2, 16];
		AttackList = new();
		var src = (byte[])table2.Rows[0]["FLD_ITEM"];
		for (var i = 0; i < 16; i++)
		{
			var array = new byte[World.Item_Db_Byte_Length];
			try
			{
				System.Buffer.BlockCopy(src, i * World.Item_Db_Byte_Length, array, 0, World.Item_Db_Byte_Length);
			}
			catch (Exception value)
			{
				Console.WriteLine(value);
			}
			ThuCung_Thanh_TrangBi[i] = new(array, i);
			_FLD_TrongLuong += ThuCung_Thanh_TrangBi[i].VatPham_TongTrongLuong;
		}
		var src2 = (byte[])table2.Rows[0]["FLD_WEARITEM"];
		for (var j = 0; j < 5; j++)
		{
			var array2 = new byte[World.Item_Db_Byte_Length];
			try
			{
				System.Buffer.BlockCopy(src2, j * World.Item_Db_Byte_Length, array2, 0, World.Item_Db_Byte_Length);
			}
			catch (Exception value2)
			{
				Console.WriteLine(value2);
			}
			ThuCungVaTrangBi[j] = new(array2, j);
			_FLD_TrongLuong += ThuCungVaTrangBi[j].VatPham_TongTrongLuong;
		}
		var array3 = (byte[])table2.Rows[0]["FLD_KONGFU"];
		for (var k = 0; k < 32; k++)
		{
			var array4 = new byte[4];
			try
			{
				if (array3.Length < k * 4 + 4)
				{
					break;
				}
				System.Buffer.BlockCopy(array3, k * 4, array4, 0, 4);
				var num = BitConverter.ToInt32(array4, 0);
				if (num == 0)
				{
					continue;
				}
				X_Vo_Cong_Loai x_Vo_Cong_Loai = new(num);
				if (x_Vo_Cong_Loai.FLD_JOB != 0)
				{
					switch (x_Vo_Cong_Loai.FLD_JOB)
					{
					case 7:
						if (FLD_JOB != 1)
						{
							continue;
						}
						break;
					case 8:
						if (FLD_JOB != 2)
						{
							continue;
						}
						break;
					case 9:
						if (FLD_JOB != 3)
						{
							continue;
						}
						break;
					case 10:
						if (FLD_JOB != 4)
						{
							continue;
						}
						break;
					}
				}
				if (FLD_JOB_LEVEL >= x_Vo_Cong_Loai.FLD_JOBLEVEL && FLD_LEVEL >= x_Vo_Cong_Loai.FLD_LEVEL)
				{
					VoCongMoi[x_Vo_Cong_Loai.FLD_VoCongLoaiHinh, x_Vo_Cong_Loai.FLD_INDEX] = x_Vo_Cong_Loai;
				}
				continue;
			}
			catch (Exception value3)
			{
				Console.WriteLine(value3);
				continue;
			}
		}
		CalculateBasicData();
		TinhToan_ThuCung_TrangBi_SoLieu();
	}

	public void CalculateBasicData()
	{
		if (FLD_LEVEL > 99)
		{
			FLD_LEVEL = 99;
		}
		LonNhatKinhNghiem = (long)World.lever[FLD_LEVEL];
		var fLD_LEVEL = FLD_LEVEL;
		while (FLD_LEVEL < 99)
		{
			if (client == null || !client.Running)
			{
				return;
			}
			if (FLD_EXP < LonNhatKinhNghiem)
			{
				if (FLD_LEVEL != 1 && FLD_EXP - Convert.ToInt64(World.lever[FLD_LEVEL - 1]) < 1.0)
				{
					FLD_LEVEL--;
					LonNhatKinhNghiem = (long)World.lever[FLD_LEVEL];
				}
				break;
			}
			FLD_LEVEL++;
			LonNhatKinhNghiem = (long)World.lever[FLD_LEVEL];
		}
		if (FLD_LEVEL - fLD_LEVEL != 0)
		{
			if (FLD_LEVEL - fLD_LEVEL > 0 && client.Player.CharacterBeast != null)
			{
				client.Player.TipsAfterTheUpgradeOfTheSpiritBeast();
			}
			if (client.Player.CharacterBeast != null)
			{
				client.Player.UpdateSpiritBeastHP_MP_SP();
				client.Player.UpdateSpiritBeastMartialArtsAndStatus();
			}
		}
		FLD_TrongLuong_MAX = 500 + 20 * FLD_LEVEL;
		switch (_FLD_JOB)
		{
		case 1:
		{
			_FLD_HP_MAX = 133 + FLD_LEVEL * 12;
			_FLD_MP_MAX = 114 + FLD_LEVEL * 2;
			FLD_TrungDich = 8 + FLD_LEVEL;
			FLD_NeTranh = 8 + FLD_LEVEL;
			FLD_CongKich = 9;
			FLD_PhongNgu = 16;
			for (var j = 2; j <= FLD_LEVEL; j++)
			{
				if (j % 2 == 0)
				{
					FLD_CongKich += 2;
					FLD_PhongNgu += 2;
				}
				else
				{
					FLD_CongKich++;
					FLD_PhongNgu++;
				}
			}
			break;
		}
		case 2:
		{
			_FLD_HP_MAX = 133 + FLD_LEVEL * 12;
			_FLD_MP_MAX = 114 + FLD_LEVEL * 2;
			FLD_TrungDich = 8 + FLD_LEVEL;
			FLD_NeTranh = 8 + FLD_LEVEL;
			FLD_CongKich = 9;
			FLD_PhongNgu = 16;
			for (var l = 2; l <= FLD_LEVEL; l++)
			{
				if (l % 2 == 0)
				{
					FLD_CongKich += 2;
					FLD_PhongNgu += 2;
				}
				else
				{
					FLD_CongKich++;
					FLD_PhongNgu++;
				}
			}
			break;
		}
		case 3:
		{
			_FLD_HP_MAX = 133 + FLD_LEVEL * 12;
			_FLD_MP_MAX = 114 + FLD_LEVEL * 2;
			FLD_TrungDich = 8 + FLD_LEVEL;
			FLD_NeTranh = 8 + FLD_LEVEL;
			FLD_CongKich = 9;
			FLD_PhongNgu = 16;
			for (var m = 2; m <= FLD_LEVEL; m++)
			{
				if (m % 2 == 0)
				{
					FLD_CongKich += 2;
					FLD_PhongNgu += 2;
				}
				else
				{
					FLD_CongKich++;
					FLD_PhongNgu++;
				}
			}
			break;
		}
		case 4:
		{
			_FLD_HP_MAX = 133 + FLD_LEVEL * 12;
			_FLD_MP_MAX = 114 + FLD_LEVEL * 2;
			FLD_TrungDich = 8 + FLD_LEVEL;
			FLD_NeTranh = 8 + FLD_LEVEL;
			FLD_CongKich = 9;
			FLD_PhongNgu = 16;
			for (var k = 2; k <= FLD_LEVEL; k++)
			{
				if (k % 2 == 0)
				{
					FLD_CongKich += 2;
					FLD_PhongNgu += 2;
				}
				else
				{
					FLD_CongKich++;
					FLD_PhongNgu++;
				}
			}
			break;
		}
		case 5:
		{
			_FLD_HP_MAX = 133 + FLD_LEVEL * 12;
			_FLD_MP_MAX = 114 + FLD_LEVEL * 2;
			FLD_TrungDich = 8 + FLD_LEVEL;
			FLD_NeTranh = 8 + FLD_LEVEL;
			FLD_CongKich = 9;
			FLD_PhongNgu = 16;
			for (var i = 2; i <= FLD_LEVEL; i++)
			{
				if (i % 2 == 0)
				{
					FLD_CongKich += 2;
					FLD_PhongNgu += 2;
				}
				else
				{
					FLD_CongKich++;
					FLD_PhongNgu++;
				}
			}
			break;
		}
		}
		switch (_FLD_JOB_LEVEL)
		{
		case 1:
			FLD_CongKich += 5;
			FLD_PhongNgu += 5;
			_FLD_HP_MAX += 85;
			_FLD_MP_MAX += 50;
			break;
		case 2:
			FLD_CongKich += 15;
			FLD_PhongNgu += 15;
			_FLD_HP_MAX += 200;
			_FLD_MP_MAX += 150;
			break;
		case 3:
			FLD_CongKich += 35;
			FLD_PhongNgu += 35;
			_FLD_HP_MAX += 400;
			_FLD_MP_MAX += 350;
			break;
		}
		if (FLD_JOB == 1)
		{
			Playe.FLD_Pet_ThemVao_LonNhatHP = (FLD_LEVEL + 1) * 3;
		}
		else if (FLD_JOB == 5)
		{
			Playe.FLD_Pet_ThemVao_LonNhatHP = (FLD_LEVEL + 1) * 2;
			Playe.FLD_Pet_ThemVao_PhongNgu = (int)((FLD_LEVEL + 1) * 0.5);
			Playe.FLD_Pet_ThemVao_CongKich = (int)((FLD_LEVEL + 1) * 0.5);
		}
		else
		{
			Playe.FLD_Pet_ThemVao_LonNhatHP = (FLD_LEVEL + 1) * 2;
			Playe.FLD_Pet_ThemVao_PhongNgu = (int)((FLD_LEVEL + 1) * 0.5);
		}
		Playe.CapNhat_HP_MP_SP();
		Playe.UpdateMartialArtsAndStatus();
	}

	public void TinhToan_ThuCung_TrangBi_SoLieu()
	{
		using (new Lock(thisLock, "TinhToan_ThuCung_TrangBi_SoLieu"))
		{
			FLD_TrangBi_ThemVao_CongKich = 0;
			FLD_TrangBi_ThemVao_PhongNgu = 0;
			FLD_TrangBi_ThemVao_TrungDich = 0;
			FLD_TrangBi_ThemVao_NeTranh = 0;
			FLD_TrangBi_ThemVao_HP = 0;
			FLD_TrangBi_ThemVao_MP = 0;
			TTTP_Tang_HP = 0;
			TTTP_Tang_MP = 0;
			TTTP_Tang_HP_NEW = 0;
			TTTP_Tang_MP_NEW = 0;
			FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
			FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
			FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
			FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem = 0.0;
			for (var i = 0; i < 4; i++)
			{
				if (ThuCungVaTrangBi[i].GetVatPham_ID != 0)
				{
					ThuCungVaTrangBi[i].DatDuocVatPham_ThuocTinhPhuongThuc(0, 0, "pet", "pet");
					FLD_TrangBi_ThemVao_CongKich += (ThuCungVaTrangBi[i].Vat_Pham_Luc_Cong_Kich + ThuCungVaTrangBi[i].Vat_Pham_Luc_Cong_KichMAX) / 2;
					FLD_TrangBi_ThemVao_PhongNgu += ThuCungVaTrangBi[i].Vat_Pham_Luc_Phong_Ngu;
					FLD_TrangBi_ThemVao_TrungDich += ThuCungVaTrangBi[i].VatPham_ThuocTinh_TiLeChinhXac_GiaTang;
					FLD_TrangBi_ThemVao_NeTranh += ThuCungVaTrangBi[i].VatPham_ThuocTinh_NeTranh_Suat_GiaTang;
					FLD_TrangBi_ThemVao_HP += ThuCungVaTrangBi[i].VatPham_ThuocTinh_SinhMenhLuc_GiaTang;
					FLD_TrangBi_ThemVao_MP += ThuCungVaTrangBi[i].VatPham_ThuocTinh_NoiCong_Luc_GiaTang;
					double num = ThuCungVaTrangBi[i].VatPham_ThuocTinh_VoCong_LucCongKich;
					var num2 = ThuCungVaTrangBi[i].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang * (1.0 - World.TyLePhanTram_PhongNguVoCong);
					FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += num * 0.01;
					FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += num2 * 0.01;
					FLD_TrangBi_HatNgoc_VoCong_LucPhongNgu_GiaTangTiLePhanTram += num2 * 0.01;
					var fLD_RESIDE = ThuCungVaTrangBi[i].FLD_RESIDE2;
				}
			}
			DatDuocThuLinhDanThuocTinh(FLD_MAGIC1.ToString());
			DatDuocThuLinhDanThuocTinh(FLD_MAGIC2.ToString());
			DatDuocThuLinhDanThuocTinh(FLD_MAGIC3.ToString());
			DatDuocThuLinhDanThuocTinh(FLD_MAGIC4.ToString());
			DatDuocThuLinhDanThuocTinh(FLD_MAGIC5.ToString());
		}
	}

	public void DatDuocThuLinhDanThuocTinh(string ysqh)
	{
		try
		{
			if (ysqh == "0")
			{
				string s;
				switch (ysqh.Length)
				{
				default:
					return;
				case 9:
					s = ysqh.Substring(0, 2);
					break;
				case 8:
					s = ysqh.Substring(0, 1);
					break;
				}
				var num = int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
				switch (int.Parse(s))
				{
				case 8:
				case 9:
				case 10:
				case 12:
				case 13:
				case 14:
					break;
				case 1:
					FLD_TrangBi_ThemVao_CongKich += num;
					break;
				case 2:
					FLD_TrangBi_ThemVao_PhongNgu += num;
					break;
				case 3:
					FLD_TrangBi_ThemVao_HP += num;
					break;
				case 4:
					FLD_TrangBi_ThemVao_MP += num;
					break;
				case 5:
					FLD_TrangBi_ThemVao_TrungDich += num;
					break;
				case 6:
					FLD_TrangBi_ThemVao_NeTranh += num;
					break;
				case 7:
					FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += num * 0.01;
					break;
				case 11:
					FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += num * 0.01;
					break;
				case 15:
					FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem += num * 0.01;
					break;
				}
			}
		}
		catch
		{
		}
	}

	public void SaveSoLieu()
	{
		var prams = new SqlParameter[18]
		{
			SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 20, _Id.ToString()),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 20, Name),
			SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, FLD_LEVEL),
			SqlDBA.MakeInParam("@zcd", SqlDbType.Int, 10, FLD_ZCD),
			SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, FLD_JOB),
			SqlDBA.MakeInParam("@job_level", SqlDbType.Int, 0, FLD_JOB_LEVEL),
			SqlDBA.MakeInParam("@exp", SqlDbType.VarChar, 50, FLD_EXP.ToString()),
			SqlDBA.MakeInParam("@hp", SqlDbType.Int, 0, FLD_HP),
			SqlDBA.MakeInParam("@mp", SqlDbType.Int, 0, FLD_MP),
			SqlDBA.MakeInParam("@strWearitem", SqlDbType.VarBinary, 385, GetWEARITEMCodes()),
			SqlDBA.MakeInParam("@strItem", SqlDbType.VarBinary, 1232, GetFLD_ITEMCodes()),
			SqlDBA.MakeInParam("@strKongfu", SqlDbType.VarBinary, 128, GetFLD_KONGFUCodes()),
			SqlDBA.MakeInParam("@bs", SqlDbType.Int, 0, Bs),
			SqlDBA.MakeInParam("@MAGIC1", SqlDbType.Int, 0, FLD_MAGIC1),
			SqlDBA.MakeInParam("@MAGIC2", SqlDbType.Int, 0, FLD_MAGIC2),
			SqlDBA.MakeInParam("@MAGIC3", SqlDbType.Int, 0, FLD_MAGIC3),
			SqlDBA.MakeInParam("@MAGIC4", SqlDbType.Int, 0, FLD_MAGIC4),
			SqlDBA.MakeInParam("@MAGIC5", SqlDbType.Int, 0, FLD_MAGIC5)
		};
		DBA.ExeSqlCommand("XWWL_UPDATE_Cw_DATA", prams);
		// World.SqlPool.Enqueue(new DbPoolClass
		// {
		// 	Conn = DBA.getstrConnection(null),
		// 	Prams = prams,
		// 	Sql = "XWWL_UPDATE_Cw_DATA"
		// });
	}

	public byte[] GetWEARITEMCodes()
	{
		var array = new byte[World.Item_Db_Byte_Length * 5];
		for (var i = 0; i < 5; i++)
		{
			byte[] src;
			try
			{
				src = ThuCungVaTrangBi[i].VatPham_byte;
			}
			catch
			{
				src = new byte[World.Item_Db_Byte_Length];
			}
			System.Buffer.BlockCopy(src, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
		}
		return array;
	}

	public byte[] GetFLD_ITEMCodes()
	{
		var array = new byte[World.Item_Db_Byte_Length * 16];
		for (var i = 0; i < 16; i++)
		{
			byte[] src;
			try
			{
				src = ThuCung_Thanh_TrangBi[i].VatPham_byte;
			}
			catch
			{
				src = new byte[World.Item_Db_Byte_Length];
			}
			System.Buffer.BlockCopy(src, 0, array, i * World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
		}
		return array;
	}

	public byte[] GetFLD_KONGFUCodes()
	{
		SendingClass sendingClass = new();
		try
		{
			for (var i = 0; i < 2; i++)
			{
				for (var j = 0; j < 17; j++)
				{
					if (VoCongMoi[i, j] != null)
					{
						sendingClass.Write4(VoCongMoi[i, j].FLD_PID);
					}
				}
			}
		}
		catch
		{
		}
		return sendingClass.ToArray3();
	}
}
