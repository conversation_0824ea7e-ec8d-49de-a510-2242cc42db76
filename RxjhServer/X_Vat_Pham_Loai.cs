using System;
using System.Collections.Generic;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public class X_Vat_Pham_Loai : IDisposable
{
	public int _Vat_Pham_Co_Ty_Le_Bo_Qua_Phong_Thu;

	public int _Vat_Pham_Co_Ty_Le_Bo_Qua_Tan_Cong;

	public bool Lock_Move;

	private bool _Vat_Pham_Khoa_Lai;

	private string _VatPhamstring;

	private int _VatPhamViTri;

	private byte[] _VatPham_byte;

	private Itimesx _ThuocTinh1;

	private Itimesx _ThuocTinh2;

	private Itimesx _ThuocTinh3;

	private Itimesx _ThuocTinh4;

	private int _Vat_Pham_Luc_Phong_Ngu;

	private int _Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu;

	private int _Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich;

	private int _Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh;

	private int _Vat_Pham_Luc_Cong_Kich;

	private int _Vat_Pham_Luc_Cong_KichMAX;

	private int _VatPham_ThuocTinh_LaChan_GiaTang;

	private int _VatPham_ThuocTinh_Manh_Loai_Hinh;

	private int _VatPham_ThuocTinh_Manh;

	private int _VatPham_ThuocTinh_Giai_Doan_Loai_Hinh;

	private int _VatPham_ThuocTinh_So_Giai_Doan;

	private int _VatPham_ThuocTinh_LucCongKich_GiaTang;

	private int _Vat_Pham_Luc_Cong_KichNew;

	private int _Vat_Pham_Luc_Cong_KichMaxNew;

	private int _VatPham_ThuocTinh_LucPhongNgu_GiaTang;

	private int _Vat_Pham_Luc_Phong_NguNew;

	private int _VatPham_ThuocTinh_SinhMenhLuc_GiaTang;

	private int _VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan;

	private int _VatPham_ThuocTinh_NoiCong_Luc_GiaTang;

	private int _VatPham_ThuocTinh_TiLeChinhXac_GiaTang;

	private int _VatPham_ThuocTinh_NeTranh_Suat_GiaTang;

	private int _VatPham_ThuocTinh_VoCong_LucCongKich;

	private int _VatPham_ThuocTinh_VoCong_LucCongKichNew;

	private double _VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich;

	private double _VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu;

	private double _VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich;

	private double _VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh;

	private double _VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram;

	private int _VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang;

	private int _VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;

	private int _VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon;

	private int _VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich;

	private int _VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu;

	private int _VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha;

	private int _VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan;

	private int _VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap;

	private int _VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich;

	private int _VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat;

	private int _VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon;

	private int _VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu;

	private int _VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_NhanKiem_NhatThe;

	private int _VatPham_ThuocTinh_ThemVao_KIEM_HonNguyen_KiemPhap;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_HoanhLuyenThaiBao;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_CanKhonNaDi;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_LinhGiapHoThan;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_CuongThanHangThe;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_ChuyenThuViCong;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_MatNhatCuongVu;

	private int _VatPham_ThuocTinh_ThemVao_THUONG_NoYChiHong;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_BachBoXuyenDuong;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_LiepUngChiNhan;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_NgungThanTuKhi;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_HoiLuuChanKhi;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_ChinhBanBoiNguyen;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_LuuTinhTamThi;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_NhueLoiChiTien;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_VoMinhAmThi;

	private int _VatPham_ThuocTinh_ThemVao_CUNG_TriMenhTuyetSat;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_VanKhiHanhTam;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_ThaiCucTamPhap;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_TheHuyetBoiTang;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_TayTuyDichKinh;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_DieuThuHoiXuan;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_TruongCongCongKich;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_HapTinhDaiPhap;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_CuuThienChanKhi;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_CuongYHoThe;

	private int _VatPham_ThuocTinh_ThemVao_DAIPHU_VoTrungSinhHuu;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__KinhKhaChiNo;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__TamHoaTuDinh;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__NhatChieuTanSat;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__TriThuTuyetMenh;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__TienPhatCheNhan;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__ThienChuVanThu;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__LienTieuDaiDa;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__KiemNhanLoanVu;

	private int _VatPham_ThuocTinh_ThemVao_NINJA__DiNoHoanNo;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_ChienMaBonDang;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_ThuGiangDaBac;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_ThanhTamPhoThien;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_DuongQuanTamDiep;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_HanCungThuNguyet;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_CaoSonLuuThuy;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_NhacDuongTamTuy;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_MaiHoaTamLong;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_LoanPhuongHoaMinh;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_DuongMinhXuanHieu;

	private int _VatPham_ThuocTinh_ThemVao_CAMSU_TieuTuongVuDa;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_ThienMaCuongHuyet;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_TruyCotHapNguyen;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_HoaLongVanDinh;

	private int _VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_New_LienHoanPhiVu;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_ChieuThucTanPhap;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_HoThan_CuongKhi;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_TungHoanhVoSong;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan;

	private int _VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo;

	private int _VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_NoHaoNhatThanh;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_VanKhiLieuThuong;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_NgungThanTuKhi;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_CuongPhongVanPha;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_LinhGiapHoThan;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_ThanCoMinhChau;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_MaPhuViCham;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_VatNgaNhatThe;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_KimCuongBatHoai;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_PhongQuyDiemBich;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_NoTamXuatKich;

	private int _VatPham_ThuocTinh_ThemVao_QuyenSu_ThienHaCuongPhong;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_KichHoatCuongLuc;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapThuCuongLuc;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_BachBienThanHanh;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_CuongPhongVanPhaMLC;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuChiDiem;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_ThanCoMinhChau;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuCuongKich;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuNguyHoa;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HoThanKhoiNguyen;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_TatDoHoaThan;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_NoKhiXungThien;

	private int _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapHuyetTienCong;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_KimChungCanhKhi;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_VanKhiLieuThuong;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_LienHoanPhiVu;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_ChanhBanBoiNguyen;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_CuongPhongVanPha;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_ThanCoMinhChau;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_ChanVuTuyetKich;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_DiTinhVanThien;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_PhatNhietKhiDan;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_DiCongViThu;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_NhatDiemNguHanh;

	private int _VatPham_ThuocTinh_ThemVao_TuHao_ToiCuongHongDiem;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_VanKhiHanhTam;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_ThaiCucTamPhap;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_ThanLucKichPhat;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_SatTinhNghiaKhi;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_TayTuyDichCan;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_ThanCoMinhChau;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaManKhai;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_DieuThuHoiXuan;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_TruongCongKichLuc;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaTapTrung;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_ChanVuTuyetKich;

	private int _VatPham_ThuocTinh_ThemVao_ThanNu_VanDocBatXam;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_DonXuatNghichCanh;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_CungDoMatLo;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaLong_ChiHoa;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_LongHong_PhuThe;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThan_CuongKhi;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhThien_DongDia;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_PhaGiapThuHon;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_DiThoiViTien;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_NoYChiHoa;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_DietThe_CuongVong;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_TuyetAnhXaHon;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienQuanApDa;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_ThienNgoaiTamThi;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienLy_NhatKich;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_CuongPhongThienY;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThanKhiGiap;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_VanVatHoiXuan;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_MinhKinhChiThuy;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_HinhDi_YeuTuong;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_DaMaTrienThan;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_ThuanThuyThoiChu;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_VoTinhDaKich;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_NhatChieuSatThan;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_PhiHoaDiemThuy;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_TamDamAnhNguyet;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_TuDaThuCa;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_LongTraoChiThu;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_HanhPhongLongVu;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienMaHoThe;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_NoiTucHanhTam;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienMaChiLuc;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_DiNhuKhacCuong;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhDao_HaiLang;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienDiaDongTho;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaPhuongLamTrieu;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_DoatMenhLienHoan;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_DienQuangThachHoa;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_TinhIchCauTinh;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_BatTu_ChiKhu;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_HuyenVuTroChu;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_SatNhanQuy;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_MaHonChiLuc;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_LangKinhThoiLe;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_SatTinhQuangPhu;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_KyQuanQuanHung;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_PhaKhongTruyTinh;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_PhanNoDieuTiet;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_2_CoDocGiaiTru;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_3_ThanLucBaoHo;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_ThiDocBaoPhat;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_5_TriTan;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_4_HongNguyetCuongPhong;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_4_DocXaXuatDong;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_4_ManNguyetCuongPhong;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_4_LietNhatViemViem;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_4_VongMaiThiemHoa;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_4_HuyenTiChanMach;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_4_TruongHongQuanThien;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_4_AiHongBienDa;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_LucPhachHoaSon;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_TruongHong_QuanNhat;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_KimChungCuongKhi;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhiHanhTam;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_ChinhBanBoiNguyen;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhi_LieuThuong;

	private int _VatPham_ThuocTinh_ThemVao_ThangThien_1_BachBien_ThanHanh;

	private int _VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong;

	private int _VatPham_ThuocTinh_ThemVao_MucThuongTon;

	private int _VatPham_ThuocTinh_GiamXuong_MucThuongTon;

	private double _VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram;

	private double _VatPham_ThuocTinh_ThemVao_CuongHoa;

	private int _VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang;

	private int _VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew;

	private int _VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang;

	private int _VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot;

	private int _VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang;

	private int _FLD_RESIDE2;

	public int _FLD_LEVEL;

	public bool VatPham_KhoaLai
	{
		get
		{
			try
			{
				var array = new byte[2];
				System.Buffer.BlockCopy(VatPham_byte, 72, array, 0, 1);
				return BitConverter.ToInt16(array, 0) != 0;
			}
			catch (Exception)
			{
				return false;
			}
		}
		set
		{
			_Vat_Pham_Khoa_Lai = value;
		}
	}

	public long GetItemGlobal_ID => BitConverter.ToInt64(VatPham_byte, 0);

	public long GetVatPham_ID => BitConverter.ToInt32(VatPham_byte, 8);

	public int FLD_Intrgration
	{
		get
		{
			try
			{
				if (GetVatPham_ID != 0)
				{
					return World.ItemList[(int)GetVatPham_ID].FLD_INTEGRATION;
				}
			}
			catch
			{
			}
			return 0;
		}
	}

	public int FLD_SERIES
	{
		get
		{
			try
			{
				if (GetVatPham_ID != 0)
				{
					return World.ItemList[(int)GetVatPham_ID].FLD_SERIES;
				}
			}
			catch
			{
			}
			return 0;
		}
	}

	public int GetVatPhamSoLuong => BitConverter.ToInt32(VatPham_byte, 12);

	public string VatPhamstring
	{
		get
		{
			return DatDuocVatPhamstring();
		}
		set
		{
			_VatPhamstring = value;
		}
	}

	public int VatPhamViTri
	{
		get
		{
			return _VatPhamViTri;
		}
		set
		{
			_VatPhamViTri = value;
		}
	}

	public int VatPhamLoaiHinh => DatDuocVatPhamLoaiHinh();

	public int VatPham_TrongLuong1Cai => DatDuocVatPham_TrongLuong1Cai();

	public int VatPham_TongTrongLuong => DatDuocVatPhamTrongLuong();

	public byte[] VatPham_byte
	{
		get
		{
			return _VatPham_byte;
		}
		set
		{
			Lock_Move = false;
			_VatPham_byte = value;
		}
	}

	public byte[] VatPhamSoLuong
	{
		get
		{
			return DatDuocVatPhamSoLuong();
		}
		set
		{
			ThietLap_VatPhamSoLuong(value);
		}
	}

	public byte[] VatPham_ID
    {
        get => DatDuocVatPham_ID();
        set => ThietLapVatPham_ID(value);
    }
	 public void ThietLapVatPham_ID(byte[] PID)
    {
        System.Buffer.BlockCopy(PID, 0, VatPham_byte, 8, 4);
    }
	public byte[] ItemGlobal_ID => DatDuocGlobal_ID();

	public byte[] VatPham_ThuocTinh => DatDuocVatPham_ThuocTinh();

	public Itimesx ThuocTinh1
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 20, array, 0, 4);
			_ThuocTinh1 = new(array);
			return _ThuocTinh1;
		}
		set
		{
			_ThuocTinh1 = value;
		}
	}

	public Itimesx ThuocTinh2
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 24, array, 0, 4);
			_ThuocTinh2 = new(array);
			return _ThuocTinh2;
		}
		set
		{
			_ThuocTinh2 = value;
		}
	}

	public Itimesx ThuocTinh3
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 28, array, 0, 4);
			_ThuocTinh3 = new(array);
			return _ThuocTinh3;
		}
		set
		{
			_ThuocTinh3 = value;
		}
	}

	public Itimesx ThuocTinh4
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 32, array, 0, 4);
			_ThuocTinh4 = new(array);
			return _ThuocTinh4;
		}
		set
		{
			_ThuocTinh4 = value;
		}
	}

	public int Vat_Pham_Luc_Phong_Ngu
	{
		get
		{
			return _Vat_Pham_Luc_Phong_Ngu;
		}
		set
		{
			_Vat_Pham_Luc_Phong_Ngu = value;
		}
	}

	public int Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu
	{
		get
		{
			return _Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu;
		}
		set
		{
			_Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu = value;
		}
	}

	public int Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich
	{
		get
		{
			return _Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich;
		}
		set
		{
			_Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich = value;
		}
	}

	public int Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh
	{
		get
		{
			return _Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh;
		}
		set
		{
			_Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh = value;
		}
	}

	public int Vat_Pham_Luc_Cong_Kich
	{
		get
		{
			return _Vat_Pham_Luc_Cong_Kich;
		}
		set
		{
			_Vat_Pham_Luc_Cong_Kich = value;
		}
	}

	public int Vat_Pham_Luc_Cong_KichMAX
	{
		get
		{
			return _Vat_Pham_Luc_Cong_KichMAX;
		}
		set
		{
			_Vat_Pham_Luc_Cong_KichMAX = value;
		}
	}

	public int VatPham_ThuocTinh_LaChan_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_LaChan_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_LaChan_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_Manh_Loai_Hinh
	{
		get
		{
			return _VatPham_ThuocTinh_Manh_Loai_Hinh;
		}
		set
		{
			_VatPham_ThuocTinh_Manh_Loai_Hinh = value;
		}
	}

	public int VatPham_ThuocTinh_Manh
	{
		get
		{
			return _VatPham_ThuocTinh_Manh;
		}
		set
		{
			_VatPham_ThuocTinh_Manh = value;
		}
	}

	public int VatPham_ThuocTinh_Giai_Doan_Loai_Hinh
	{
		get
		{
			return _VatPham_ThuocTinh_Giai_Doan_Loai_Hinh;
		}
		set
		{
			_VatPham_ThuocTinh_Giai_Doan_Loai_Hinh = value;
		}
	}

	public int VatPham_ThuocTinh_So_Giai_Doan
	{
		get
		{
			return _VatPham_ThuocTinh_So_Giai_Doan;
		}
		set
		{
			_VatPham_ThuocTinh_So_Giai_Doan = value;
		}
	}

	public int VatPham_ThuocTinh_LucCongKich_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_LucCongKich_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_LucCongKich_GiaTang = value;
		}
	}

	public int Vat_Pham_Luc_Cong_KichNew
	{
		get
		{
			return _Vat_Pham_Luc_Cong_KichNew;
		}
		set
		{
			_Vat_Pham_Luc_Cong_KichNew = value;
		}
	}

	public int Vat_Pham_Luc_Cong_KichMaxNew
	{
		get
		{
			return _Vat_Pham_Luc_Cong_KichMaxNew;
		}
		set
		{
			_Vat_Pham_Luc_Cong_KichMaxNew = value;
		}
	}

	public int VatPham_ThuocTinh_LucPhongNgu_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_LucPhongNgu_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_LucPhongNgu_GiaTang = value;
		}
	}

	public int Vat_Pham_Luc_Phong_NguNew
	{
		get
		{
			return _Vat_Pham_Luc_Phong_NguNew;
		}
		set
		{
			_Vat_Pham_Luc_Phong_NguNew = value;
		}
	}

	public int VatPham_ThuocTinh_SinhMenhLuc_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_SinhMenhLuc_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_SinhMenhLuc_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan
	{
		get
		{
			return _VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan;
		}
		set
		{
			_VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan = value;
		}
	}

	public int VatPham_ThuocTinh_NoiCong_Luc_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_NoiCong_Luc_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_NoiCong_Luc_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_TiLeChinhXac_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_TiLeChinhXac_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_TiLeChinhXac_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_NeTranh_Suat_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_NeTranh_Suat_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_NeTranh_Suat_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_VoCong_LucCongKich
	{
		get
		{
			return _VatPham_ThuocTinh_VoCong_LucCongKich;
		}
		set
		{
			_VatPham_ThuocTinh_VoCong_LucCongKich = value;
		}
	}

	public int VatPham_ThuocTinh_VoCong_LucCongKichNew
	{
		get
		{
			return _VatPham_ThuocTinh_VoCong_LucCongKichNew;
		}
		set
		{
			_VatPham_ThuocTinh_VoCong_LucCongKichNew = value;
		}
	}

	public double VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich
	{
		get
		{
			return _VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich;
		}
		set
		{
			_VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich = value;
		}
	}

	public double VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu
	{
		get
		{
			return _VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu;
		}
		set
		{
			_VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu = value;
		}
	}

	public double VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich
	{
		get
		{
			return _VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich;
		}
		set
		{
			_VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich = value;
		}
	}

	public double VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh
	{
		get
		{
			return _VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh;
		}
		set
		{
			_VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh = value;
		}
	}

	public double VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram
	{
		get
		{
			return _VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram;
		}
		set
		{
			_VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram = value;
		}
	}

	public int VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaLong_ChiHoa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaLong_ChiHoa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaLong_ChiHoa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThan_CuongKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThan_CuongKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThan_CuongKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_NhanKiem_NhatThe
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_NhanKiem_NhatThe;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_NhanKiem_NhatThe = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_KIEM_HonNguyen_KiemPhap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_KIEM_HonNguyen_KiemPhap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_KIEM_HonNguyen_KiemPhap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_NoYChiHoa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_NoYChiHoa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_NoYChiHoa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_HoanhLuyenThaiBao
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_HoanhLuyenThaiBao;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_HoanhLuyenThaiBao = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_CanKhonNaDi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_CanKhonNaDi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_CanKhonNaDi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_LinhGiapHoThan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_LinhGiapHoThan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_LinhGiapHoThan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_CuongThanHangThe
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_CuongThanHangThe;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_CuongThanHangThe = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_ChuyenThuViCong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_ChuyenThuViCong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_ChuyenThuViCong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_MatNhatCuongVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_MatNhatCuongVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_MatNhatCuongVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_THUONG_NoYChiHong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_THUONG_NoYChiHong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_THUONG_NoYChiHong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_BachBoXuyenDuong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_BachBoXuyenDuong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_BachBoXuyenDuong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_LiepUngChiNhan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_LiepUngChiNhan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_LiepUngChiNhan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_NgungThanTuKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_NgungThanTuKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_NgungThanTuKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_HoiLuuChanKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_HoiLuuChanKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_HoiLuuChanKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_ChinhBanBoiNguyen
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_ChinhBanBoiNguyen;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_ChinhBanBoiNguyen = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_LuuTinhTamThi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_LuuTinhTamThi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_LuuTinhTamThi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_NhueLoiChiTien
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_NhueLoiChiTien;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_NhueLoiChiTien = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_VoMinhAmThi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_VoMinhAmThi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_VoMinhAmThi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CUNG_TriMenhTuyetSat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CUNG_TriMenhTuyetSat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CUNG_TriMenhTuyetSat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_VanKhiHanhTam
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_VanKhiHanhTam;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_VanKhiHanhTam = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_ThaiCucTamPhap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_ThaiCucTamPhap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_ThaiCucTamPhap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_TheHuyetBoiTang
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_TheHuyetBoiTang;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_TheHuyetBoiTang = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_TayTuyDichKinh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_TayTuyDichKinh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_TayTuyDichKinh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_DieuThuHoiXuan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_DieuThuHoiXuan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_DieuThuHoiXuan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_TruongCongCongKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_TruongCongCongKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_TruongCongCongKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_HapTinhDaiPhap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_HapTinhDaiPhap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_HapTinhDaiPhap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThanKhiGiap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThanKhiGiap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThanKhiGiap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_CuuThienChanKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_CuuThienChanKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_CuuThienChanKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_CuongYHoThe
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_CuongYHoThe;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_CuongYHoThe = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DAIPHU_VoTrungSinhHuu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DAIPHU_VoTrungSinhHuu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DAIPHU_VoTrungSinhHuu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__KinhKhaChiNo
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__KinhKhaChiNo;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__KinhKhaChiNo = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__TamHoaTuDinh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__TamHoaTuDinh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__TamHoaTuDinh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__NhatChieuTanSat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__NhatChieuTanSat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__NhatChieuTanSat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__TriThuTuyetMenh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__TriThuTuyetMenh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__TriThuTuyetMenh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__TienPhatCheNhan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__TienPhatCheNhan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__TienPhatCheNhan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__ThienChuVanThu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__ThienChuVanThu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__ThienChuVanThu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__LienTieuDaiDa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__LienTieuDaiDa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__LienTieuDaiDa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__KiemNhanLoanVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__KiemNhanLoanVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__KiemNhanLoanVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_NINJA__DiNoHoanNo
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_NINJA__DiNoHoanNo;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_NINJA__DiNoHoanNo = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_ChienMaBonDang
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_ChienMaBonDang;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_ChienMaBonDang = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_ThuGiangDaBac
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_ThuGiangDaBac;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_ThuGiangDaBac = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_ThanhTamPhoThien
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_ThanhTamPhoThien;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_ThanhTamPhoThien = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_DuongQuanTamDiep
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_DuongQuanTamDiep;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_DuongQuanTamDiep = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_HanCungThuNguyet
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_HanCungThuNguyet;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_HanCungThuNguyet = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_CaoSonLuuThuy
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_CaoSonLuuThuy;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_CaoSonLuuThuy = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_NhacDuongTamTuy
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_NhacDuongTamTuy;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_NhacDuongTamTuy = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_MaiHoaTamLong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_MaiHoaTamLong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_MaiHoaTamLong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_LoanPhuongHoaMinh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_LoanPhuongHoaMinh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_LoanPhuongHoaMinh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_DuongMinhXuanHieu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_DuongMinhXuanHieu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_DuongMinhXuanHieu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_CAMSU_TieuTuongVuDa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CAMSU_TieuTuongVuDa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CAMSU_TieuTuongVuDa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_ThienMaCuongHuyet
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_ThienMaCuongHuyet;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_ThienMaCuongHuyet = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_TruyCotHapNguyen
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_TruyCotHapNguyen;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_TruyCotHapNguyen = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_HoaLongVanDinh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_HoaLongVanDinh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_HoaLongVanDinh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_New_LienHoanPhiVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_New_LienHoanPhiVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_New_LienHoanPhiVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_ChieuThucTanPhap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_ChieuThucTanPhap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_ChieuThucTanPhap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_HoThan_CuongKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_HoThan_CuongKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_HoThan_CuongKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_TungHoanhVoSong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_TungHoanhVoSong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_TungHoanhVoSong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanCoMinhChau
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanCoMinhChau;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanCoMinhChau = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_NoHaoNhatThanh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_NoHaoNhatThanh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_NoHaoNhatThanh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_VanKhiLieuThuong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_VanKhiLieuThuong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_VanKhiLieuThuong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_NgungThanTuKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_NgungThanTuKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_NgungThanTuKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_CuongPhongVanPha
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_CuongPhongVanPha;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_CuongPhongVanPha = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_LinhGiapHoThan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_LinhGiapHoThan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_LinhGiapHoThan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_ThanCoMinhChau
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_ThanCoMinhChau;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_ThanCoMinhChau = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_MaPhuViCham
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_MaPhuViCham;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_MaPhuViCham = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_VatNgaNhatThe
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_VatNgaNhatThe;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_VatNgaNhatThe = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_KimCuongBatHoai
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_KimCuongBatHoai;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_KimCuongBatHoai = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_PhongQuyDiemBich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_PhongQuyDiemBich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_PhongQuyDiemBich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_NoTamXuatKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_NoTamXuatKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_NoTamXuatKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_QuyenSu_ThienHaCuongPhong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_QuyenSu_ThienHaCuongPhong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_QuyenSu_ThienHaCuongPhong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_KichHoatCuongLuc
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_KichHoatCuongLuc;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_KichHoatCuongLuc = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapThuCuongLuc
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapThuCuongLuc;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapThuCuongLuc = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_BachBienThanHanh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_BachBienThanHanh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_BachBienThanHanh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_CuongPhongVanPhaMLC
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_CuongPhongVanPhaMLC;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_CuongPhongVanPhaMLC = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuChiDiem
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuChiDiem;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuChiDiem = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_ThanCoMinhChau
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_ThanCoMinhChau;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_ThanCoMinhChau = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuCuongKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuCuongKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuCuongKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuNguyHoa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuNguyHoa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuNguyHoa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_HoThanKhoiNguyen
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HoThanKhoiNguyen;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_HoThanKhoiNguyen = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_TatDoHoaThan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_TatDoHoaThan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_TatDoHoaThan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_NoKhiXungThien
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_NoKhiXungThien;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_NoKhiXungThien = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapHuyetTienCong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapHuyetTienCong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapHuyetTienCong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_KimChungCanhKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_KimChungCanhKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_KimChungCanhKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_VanKhiLieuThuong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_VanKhiLieuThuong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_VanKhiLieuThuong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_LienHoanPhiVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_LienHoanPhiVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_LienHoanPhiVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_ChanhBanBoiNguyen
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_ChanhBanBoiNguyen;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_ChanhBanBoiNguyen = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_CuongPhongVanPha
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_CuongPhongVanPha;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_CuongPhongVanPha = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_ThanCoMinhChau
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_ThanCoMinhChau;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_ThanCoMinhChau = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_ChanVuTuyetKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_ChanVuTuyetKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_ChanVuTuyetKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_DiTinhVanThien
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_DiTinhVanThien;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_DiTinhVanThien = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_PhatNhietKhiDan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_PhatNhietKhiDan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_PhatNhietKhiDan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_DiCongViThu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_DiCongViThu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_DiCongViThu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_NhatDiemNguHanh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_NhatDiemNguHanh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_NhatDiemNguHanh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_TuHao_ToiCuongHongDiem
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TuHao_ToiCuongHongDiem;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TuHao_ToiCuongHongDiem = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_VanKhiHanhTam
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_VanKhiHanhTam;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_VanKhiHanhTam = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_ThaiCucTamPhap
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_ThaiCucTamPhap;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_ThaiCucTamPhap = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_ThanLucKichPhat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_ThanLucKichPhat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_ThanLucKichPhat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_SatTinhNghiaKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_SatTinhNghiaKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_SatTinhNghiaKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_TayTuyDichCan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_TayTuyDichCan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_TayTuyDichCan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_ThanCoMinhChau
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_ThanCoMinhChau;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_ThanCoMinhChau = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaManKhai
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaManKhai;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaManKhai = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_DieuThuHoiXuan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_DieuThuHoiXuan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_DieuThuHoiXuan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_TruongCongKichLuc
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_TruongCongKichLuc;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_TruongCongKichLuc = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaTapTrung
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaTapTrung;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaTapTrung = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_ChanVuTuyetKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_ChanVuTuyetKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_ChanVuTuyetKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThanNu_VanDocBatXam
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThanNu_VanDocBatXam;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThanNu_VanDocBatXam = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_DonXuatNghichCanh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_DonXuatNghichCanh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_DonXuatNghichCanh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_CungDoMatLo
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_CungDoMatLo;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_CungDoMatLo = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_4_HongNguyetCuongPhong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_4_HongNguyetCuongPhong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_4_HongNguyetCuongPhong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_4_DocXaXuatDong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_4_DocXaXuatDong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_4_DocXaXuatDong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_4_ManNguyetCuongPhong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_4_ManNguyetCuongPhong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_4_ManNguyetCuongPhong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_4_LietNhatViemViem
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_4_LietNhatViemViem;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_4_LietNhatViemViem = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_4_TruongHongQuanThien
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_4_TruongHongQuanThien;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_4_TruongHongQuanThien = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_4_AiHongBienDa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_4_AiHongBienDa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_4_AiHongBienDa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_DoatMenhLienHoan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_DoatMenhLienHoan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_DoatMenhLienHoan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_DienQuangThachHoa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_DienQuangThachHoa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_DienQuangThachHoa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_TinhIchCauTinh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_TinhIchCauTinh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_TinhIchCauTinh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienDiaDongTho
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienDiaDongTho;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienDiaDongTho = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaPhuongLamTrieu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaPhuongLamTrieu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaPhuongLamTrieu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_PhaGiapThuHon
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_PhaGiapThuHon;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_PhaGiapThuHon = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_DiThoiViTien
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_DiThoiViTien;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_DiThoiViTien = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_TuyetAnhXaHon
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_TuyetAnhXaHon;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_TuyetAnhXaHon = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienQuanApDa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienQuanApDa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienQuanApDa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_VanVatHoiXuan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_VanVatHoiXuan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_VanVatHoiXuan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_ThienNgoaiTamThi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_ThienNgoaiTamThi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_ThienNgoaiTamThi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_MinhKinhChiThuy
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_MinhKinhChiThuy;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_MinhKinhChiThuy = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_4_VongMaiThiemHoa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_4_VongMaiThiemHoa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_4_VongMaiThiemHoa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_DaMaTrienThan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_DaMaTrienThan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_DaMaTrienThan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_ThuanThuyThoiChu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_ThuanThuyThoiChu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_ThuanThuyThoiChu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_VoTinhDaKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_VoTinhDaKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_VoTinhDaKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_DiNhuKhacCuong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_DiNhuKhacCuong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_DiNhuKhacCuong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_NoiTucHanhTam
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_NoiTucHanhTam;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_NoiTucHanhTam = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienMaHoThe
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienMaHoThe;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienMaHoThe = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_HanhPhongLongVu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_HanhPhongLongVu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_HanhPhongLongVu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_4_HuyenTiChanMach
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_4_HuyenTiChanMach;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_4_HuyenTiChanMach = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_TuDaThuCa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_TuDaThuCa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_TuDaThuCa = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_TamDamAnhNguyet
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_TamDamAnhNguyet;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_TamDamAnhNguyet = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_LucPhachHoaSon
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_LucPhachHoaSon;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_LucPhachHoaSon = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_TruongHong_QuanNhat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_TruongHong_QuanNhat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_TruongHong_QuanNhat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_KimChungCuongKhi
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_KimChungCuongKhi;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_KimChungCuongKhi = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhiHanhTam
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhiHanhTam;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhiHanhTam = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_ChinhBanBoiNguyen
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_ChinhBanBoiNguyen;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_ChinhBanBoiNguyen = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhi_LieuThuong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhi_LieuThuong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhi_LieuThuong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_BachBien_ThanHanh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_BachBien_ThanHanh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_BachBien_ThanHanh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_CuongPhongThienY
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_CuongPhongThienY;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_CuongPhongThienY = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_PhiHoaDiemThuy
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_PhiHoaDiemThuy;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_PhiHoaDiemThuy = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_LongHong_PhuThe
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_LongHong_PhuThe;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_LongHong_PhuThe = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhThien_DongDia
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhThien_DongDia;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhThien_DongDia = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_DietThe_CuongVong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_DietThe_CuongVong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_DietThe_CuongVong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienLy_NhatKich
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienLy_NhatKich;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienLy_NhatKich = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_HinhDi_YeuTuong
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_HinhDi_YeuTuong;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_HinhDi_YeuTuong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_NhatChieuSatThan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_NhatChieuSatThan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_NhatChieuSatThan = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_LongTraoChiThu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_LongTraoChiThu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_LongTraoChiThu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienMaChiLuc
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienMaChiLuc;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienMaChiLuc = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhDao_HaiLang
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhDao_HaiLang;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhDao_HaiLang = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_BatTu_ChiKhu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_BatTu_ChiKhu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_BatTu_ChiKhu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_HuyenVuTroChu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_HuyenVuTroChu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_HuyenVuTroChu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_SatNhanQuy
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_SatNhanQuy;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_SatNhanQuy = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_MaHonChiLuc
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_MaHonChiLuc;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_MaHonChiLuc = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_LangKinhThoiLe
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_LangKinhThoiLe;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_LangKinhThoiLe = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_SatTinhQuangPhu
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_SatTinhQuangPhu;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_SatTinhQuangPhu = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_KyQuanQuanHung
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_KyQuanQuanHung;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_KyQuanQuanHung = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_PhaKhongTruyTinh
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_PhaKhongTruyTinh;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_PhaKhongTruyTinh = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_1_PhanNoDieuTiet
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_1_PhanNoDieuTiet;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_1_PhanNoDieuTiet = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_2_CoDocGiaiTru
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_2_CoDocGiaiTru;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_2_CoDocGiaiTru = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_3_ThanLucBaoHo
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_3_ThanLucBaoHo;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_3_ThanLucBaoHo = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_ThiDocBaoPhat
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_ThiDocBaoPhat;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_ThiDocBaoPhat = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_ThangThien_5_TriTan
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_ThangThien_5_TriTan;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_ThangThien_5_TriTan = value;
		}
	}

	public int VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong
	{
		get
		{
			return _VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong;
		}
		set
		{
			_VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong = value;
		}
	}

	public int VatPham_ThuocTinh_ThemVao_MucThuongTon
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_MucThuongTon;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_MucThuongTon = value;
		}
	}

	public int VatPham_ThuocTinh_GiamXuong_MucThuongTon
	{
		get
		{
			return _VatPham_ThuocTinh_GiamXuong_MucThuongTon;
		}
		set
		{
			_VatPham_ThuocTinh_GiamXuong_MucThuongTon = value;
		}
	}

	public double VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram = value;
		}
	}

	public double VatPham_ThuocTinh_ThemVao_CuongHoa
	{
		get
		{
			return _VatPham_ThuocTinh_ThemVao_CuongHoa;
		}
		set
		{
			_VatPham_ThuocTinh_ThemVao_CuongHoa = value;
		}
	}

	public int VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew
	{
		get
		{
			return _VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew;
		}
		set
		{
			_VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew = value;
		}
	}

	public int VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang = value;
		}
	}

	public int VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot
	{
		get
		{
			return _VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot;
		}
		set
		{
			_VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot = value;
		}
	}

	public int VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang
	{
		get
		{
			return _VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang;
		}
		set
		{
			_VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang = value;
		}
	}

	public int FLD_LEVEL
	{
		get
		{
			return _FLD_LEVEL;
		}
		set
		{
			_FLD_LEVEL = value;
		}
	}

	public int FLD_RESIDE2
	{
		get
		{
			return _FLD_RESIDE2;
		}
		set
		{
			_FLD_RESIDE2 = value;
		}
	}
	public int FLD_RESIDE1 { get; set; }
	public int FLD_MAGIC0
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 16, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 16, 2);
		}
	}

	public int FLD_CuongHoaLoaiHinh
	{
		get
		{
			var fLD_MAGIC = FLD_MAGIC0;
			if (fLD_MAGIC <= 0)
			{
				return 0;
			}
			var text = fLD_MAGIC.ToString();
			return int.Parse(text.Substring(text.Length - 8, 1));
		}
	}

	public int FLD_CuongHoaSoLuong
	{
		get
		{
			var fLD_MAGIC = FLD_MAGIC0;
			if (fLD_MAGIC <= 0)
			{
				return 0;
			}
			var text = fLD_MAGIC.ToString();
			return int.Parse(text.Substring(text.Length - 2, 2));
		}
	}

	public int FLDThuocTinhLoaiHinh
	{
		get
		{
			var fLD_MAGIC = FLD_MAGIC0;
			if (fLD_MAGIC > 0 && fLD_MAGIC > **********)
			{
				var text = fLD_MAGIC.ToString();
				return int.Parse(text.Substring(text.Length - 4, 1));
			}
			return 0;
		}
	}

	public int FLDThuocTinhSoLuong
	{
		get
		{
			var fLD_MAGIC = FLD_MAGIC0;
			if (fLD_MAGIC > 0 && fLD_MAGIC > **********)
			{
				var text = fLD_MAGIC.ToString();
				return int.Parse(text.Substring(text.Length - 3, 1));
			}
			return 0;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 20, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 20, 4);
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 24, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 24, 4);
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 28, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 28, 4);
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 32, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 32, 4);
		}
	}

	public int FLD_FJ_MAGIC0
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham_byte, 36, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 36, 2);
		}
	}

	public int FLD_FJ_MAGIC1
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham_byte, 38, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 38, 2);
		}
	}

	public int FLD_FJ_TrungCapPhuHon
	{
		get
		{
			try
			{
				var array = new byte[2];
				System.Buffer.BlockCopy(VatPham_byte, 40, array, 0, 2);
				return BitConverter.ToInt16(array, 0);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "FLD_FJ_TrungCapPhuHon Geterror： [" + DatDuocVatPhamstring() + "]" + ex);
				return 0;
			}
		}
		set
		{
			try
			{
				if (value > 0)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, VatPham_byte, 38, 2);
				}
				else if (value == 0)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, VatPham_byte, 38, 2);
				}
				System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 40, 2);
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "FLD_FJ_TrungCapPhuHon Seterror [" + DatDuocVatPhamstring() + "]" + ex);
			}
		}
	}

	public int FLD_FJ_MAGIC2
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham_byte, 42, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 42, 2);
		}
	}

	public int FLD_FJ_MAGIC3
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham_byte, 44, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 44, 2);
		}
	}

	public int FLD_FJ_MAGIC4
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham_byte, 46, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 46, 2);
		}
	}

	public int FLD_FJ_MAGIC5
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham_byte, 48, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 48, 2);
		}
	}

	public int FLD_DAY2
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 56, array, 0, 4);
			var dateTime = new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(BitConverter.ToInt32(array, 0));
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 56, 4);
		}
	}

	public int FLD_DAY1
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 52, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 52, 4);
		}
	}

	public int FLD_FJ_LowSoul
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 62, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 62, 4);
		}
	}

	public int FLD_FJ_NJ
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(VatPham_byte, 60, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 60, 2);
		}
	}

	public int FLD_FJ_TienHoa
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 68, array, 0, 2);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 68, 2);
		}
	}

	public int FLD_TuLinh
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 71, array, 0, 1);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 71, 1);
		}
	}

	public byte[] GetByte()
	{
		SendingClass sendingClass = new();
		sendingClass.Write(GetItemGlobal_ID);
		if (VatPham_KhoaLai)
		{
			sendingClass.Write(GetVatPham_ID + 20000);
		}
		else
		{
			sendingClass.Write(GetVatPham_ID);
		}
		sendingClass.Write4(GetVatPhamSoLuong);
		sendingClass.Write4(FLD_MAGIC0);
		sendingClass.Write4(FLD_MAGIC1);
		sendingClass.Write4(FLD_MAGIC2);
		sendingClass.Write4(FLD_MAGIC3);
		sendingClass.Write4(FLD_MAGIC4);
		//40
		sendingClass.Write2(FLD_FJ_MAGIC0);
		sendingClass.Write2(FLD_FJ_MAGIC1);
		sendingClass.Write2(FLD_FJ_TrungCapPhuHon);
		sendingClass.Write2(FLD_FJ_MAGIC2);
		sendingClass.Write2(FLD_FJ_MAGIC3);
		sendingClass.Write2(FLD_FJ_MAGIC4);
		sendingClass.Write2(FLD_FJ_MAGIC5);
		sendingClass.Write2(0);
		//56
		sendingClass.Write4(FLD_DAY1);
		sendingClass.Write4(FLD_DAY2);
		sendingClass.Write2(FLD_FJ_NJ);
		//66
		sendingClass.Write4(FLD_FJ_LowSoul + Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh);
		sendingClass.Write2(0);
		sendingClass.Write2(FLD_FJ_TienHoa);
		sendingClass.Write2(0);
		//76
		sendingClass.Write4(FLD_TuLinh);
		sendingClass.Write4(0);
		sendingClass.Write4(0);
		sendingClass.Write4(0);
		// 92
		return sendingClass.ToArray3();
	}

	public X_Vat_Pham_Loai()
	{
	}

	public X_Vat_Pham_Loai(byte[] VatPham_byte_)
	{
		VatPham_byte = VatPham_byte_;
	}

	public X_Vat_Pham_Loai(byte[] VatPham_byte_, int Position)
	{
		VatPham_byte = VatPham_byte_;
		VatPhamViTri = Position;
	}

	public void Dispose()
	{
	}

	public string DatDuocVatPhamstring()
	{
		try
		{
			return Converter.ToString(VatPham_byte);
		}
		catch
		{
			return string.Empty;
		}
	}

	public int DatDuocVatPham_TrongLuong1Cai()
	{
		try
		{
			ItmeClass value;
			return World.ItemList.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out value) ? value.FLD_WEIGHT : 0;
		}
		catch
		{
			return 0;
		}
	}

	public int DatDuocVatPhamTrongLuong()
	{
		try
		{
			ItmeClass value;
			return World.ItemList.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out value) ? (value.FLD_WEIGHT * BitConverter.ToInt32(VatPhamSoLuong, 0)) : 0;
		}
		catch
		{
			return 0;
		}
	}

	public byte[] DatDuocVatPham_ThuocTinh()
	{
		var array = new byte[56];
		System.Buffer.BlockCopy(VatPham_byte, 16, array, 0, 56);
		return array;
	}

	public byte[] DatDuocGlobal_ID()
	{
		var array = new byte[8];
		System.Buffer.BlockCopy(VatPham_byte, 0, array, 0, 8);
		return array;
	}

	public byte[] DatDuocVatPham_ID()
	{
		var array = new byte[4];
		System.Buffer.BlockCopy(VatPham_byte, 8, array, 0, 4);
		return array;
	}

	public byte[] DatDuocVatPhamSoLuong()
	{
		var array = new byte[4];
		System.Buffer.BlockCopy(VatPham_byte, 12, array, 0, 4);
		return array;
	}

	public void ThietLap_VatPhamSoLuong(byte[] SoLuong)
	{
		System.Buffer.BlockCopy(SoLuong, 0, VatPham_byte, 12, 4);
	}

	public int DatDuocVatPhamLoaiHinh()
	{
		var key = BitConverter.ToInt32(DatDuocVatPham_ID(), 0);
		return World.ItemList[key].FLD_SIDE;
	}

	public int DatDuocVatPhamViTriLoaiHinh()
	{
		var byte_ = DatDuocVatPham_ID();
		if (!World.ItemList.TryGetValue(BitConverter.ToInt32(byte_, 0), out var value))
		{
			return 0;
		}
		return value.FLD_RESIDE2;
	}

	public int GetGender()
	{
		if (World.ItemList.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out var value))
		{
			if (value.FLD_SEX != 0)
			{
				return value.FLD_SEX;
			}
			return 0;
		}
		return 0;
	}

	public string GetItemName()
	{
		if (!World.ItemList.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out var value))
		{
			return string.Empty;
		}
		return value.ItmeNAME;
	}

	private void Clear_VatPham_ThuocTinh_方法()
	{
		Vat_Pham_Luc_Cong_Kich = 0;
		Vat_Pham_Luc_Cong_KichNew = 0;
		Vat_Pham_Luc_Cong_KichMAX = 0;
		Vat_Pham_Luc_Cong_KichMaxNew = 0;
		VatPham_ThuocTinh_LucCongKich_GiaTang = 0;
		VatPham_ThuocTinh_LaChan_GiaTang = 0;
		Vat_Pham_Luc_Phong_Ngu = 0;
		VatPham_ThuocTinh_LucPhongNgu_GiaTang = 0;
		Vat_Pham_Luc_Phong_NguNew = 0;
		VatPham_ThuocTinh_SinhMenhLuc_GiaTang = 0;
		VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan = 0;
		VatPham_ThuocTinh_NoiCong_Luc_GiaTang = 0;
		VatPham_ThuocTinh_TiLeChinhXac_GiaTang = 0;
		VatPham_ThuocTinh_NeTranh_Suat_GiaTang = 0;
		VatPham_ThuocTinh_VoCong_LucCongKich = 0;
		VatPham_ThuocTinh_VoCong_LucCongKichNew = 0;
		VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu = 0.0;
		VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich = 0.0;
		VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich = 0.0;
		VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh = 0.0;
		VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram = 0.0;
		VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang = 0;
		VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram = 0.0;
		VatPham_ThuocTinh_GiamXuong_MucThuongTon = 0;
		VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang = 0;
		VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong = 0;
		VatPham_ThuocTinh_ThemVao_MucThuongTon = 0;
		VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang = 0;
		VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew = 0;
		VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang = 0;
		VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot = 0;
		VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang = 0;
		Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh = 0;
		FLD_RESIDE2 = 0;
		FLD_LEVEL = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_TruongCongCongKich = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_VanKhiHanhTam = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_TayTuyDichKinh = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_HapTinhDaiPhap = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThanKhiGiap = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_TheHuyetBoiTang = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_ThaiCucTamPhap = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_DieuThuHoiXuan = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_CuuThienChanKhi = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_CuongYHoThe = 0;
		VatPham_ThuocTinh_ThemVao_DAIPHU_VoTrungSinhHuu = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_ChienMaBonDang = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_NhacDuongTamTuy = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_DuongMinhXuanHieu = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_DuongQuanTamDiep = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_ThuGiangDaBac = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_ThanhTamPhoThien = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_MaiHoaTamLong = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_LoanPhuongHoaMinh = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_HanCungThuNguyet = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_CaoSonLuuThuy = 0;
		VatPham_ThuocTinh_ThemVao_CAMSU_TieuTuongVuDa = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_ChuyenThuViCong = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_CanKhonNaDi = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_LinhGiapHoThan = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_CuongThanHangThe = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_HoanhLuyenThaiBao = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_NoYChiHoa = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_MatNhatCuongVu = 0;
		VatPham_ThuocTinh_ThemVao_THUONG_NoYChiHong = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThan_CuongKhi = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_NhanKiem_NhatThe = 0;
		VatPham_ThuocTinh_ThemVao_KIEM_HonNguyen_KiemPhap = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_ChinhBanBoiNguyen = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_VoMinhAmThi = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_NhueLoiChiTien = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_NgungThanTuKhi = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_LuuTinhTamThi = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_LiepUngChiNhan = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_HoiLuuChanKhi = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_BachBoXuyenDuong = 0;
		VatPham_ThuocTinh_ThemVao_CUNG_TriMenhTuyetSat = 0;
		VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich = 0;
		VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon = 0;
		VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan = 0;
		VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich = 0;
		VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu = 0;
		VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon = 0;
		VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaLong_ChiHoa = 0;
		VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap = 0;
		VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat = 0;
		VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu = 0;
		VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__DiNoHoanNo = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__TriThuTuyetMenh = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__TienPhatCheNhan = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__TamHoaTuDinh = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__ThienChuVanThu = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__LienTieuDaiDa = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__KinhKhaChiNo = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__KiemNhanLoanVu = 0;
		VatPham_ThuocTinh_ThemVao_NINJA__NhatChieuTanSat = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_HoaLongVanDinh = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_ThienMaCuongHuyet = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich = 0;
		VatPham_ThuocTinh_ThemVao_HanBaoQuan_TruyCotHapNguyen = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_HoThan_CuongKhi = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_New_LienHoanPhiVu = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_ChieuThucTanPhap = 0;
		VatPham_ThuocTinh_ThemVao_DamHoaLien_TungHoanhVoSong = 0;
		VatPham_ThuocTinh_ThemVao_ThanCoMinhChau = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_NoHaoNhatThanh = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_VanKhiLieuThuong = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_NgungThanTuKhi = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_CuongPhongVanPha = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_LinhGiapHoThan = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_ThanCoMinhChau = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_MaPhuViCham = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_VatNgaNhatThe = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_KimCuongBatHoai = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_PhongQuyDiemBich = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_NoTamXuatKich = 0;
		VatPham_ThuocTinh_ThemVao_QuyenSu_ThienHaCuongPhong = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_KichHoatCuongLuc = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapThuCuongLuc = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_BachBienThanHanh = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_CuongPhongVanPhaMLC = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuChiDiem = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_ThanCoMinhChau = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuCuongKich = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuNguyHoa = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_HoThanKhoiNguyen = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_TatDoHoaThan = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_NoKhiXungThien = 0;
		VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapHuyetTienCong = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_KimChungCanhKhi = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_VanKhiLieuThuong = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_LienHoanPhiVu = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_ChanhBanBoiNguyen = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_CuongPhongVanPha = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_ThanCoMinhChau = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_ChanVuTuyetKich = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_DiTinhVanThien = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_PhatNhietKhiDan = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_DiCongViThu = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_NhatDiemNguHanh = 0;
		VatPham_ThuocTinh_ThemVao_TuHao_ToiCuongHongDiem = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_VanKhiHanhTam = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_ThaiCucTamPhap = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_ThanLucKichPhat = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_SatTinhNghiaKhi = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_TayTuyDichCan = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_ThanCoMinhChau = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaManKhai = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_DieuThuHoiXuan = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_TruongCongKichLuc = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaTapTrung = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_ChanVuTuyetKich = 0;
		VatPham_ThuocTinh_ThemVao_ThanNu_VanDocBatXam = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_DoatMenhLienHoan = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_DienQuangThachHoa = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_TinhIchCauTinh = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_4_TruongHongQuanThien = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_4_HuyenTiChanMach = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_4_VongMaiThiemHoa = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_4_AiHongBienDa = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_4_DocXaXuatDong = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_4_HongNguyetCuongPhong = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_4_LietNhatViemViem = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_4_ManNguyetCuongPhong = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_ThienNgoaiTamThi = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_DiNhuKhacCuong = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_TuDaThuCa = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaPhuongLamTrieu = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_MinhKinhChiThuy = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_NoiTucHanhTam = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_VoTinhDaKich = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienMaHoThe = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienDiaDongTho = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_ThuanThuyThoiChu = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_DiThoiViTien = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienQuanApDa = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_CungDoMatLo = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_TamDamAnhNguyet = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_VanVatHoiXuan = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_DaMaTrienThan = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_ChinhBanBoiNguyen = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_TruongHong_QuanNhat = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhi_LieuThuong = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhiHanhTam = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_LucPhachHoaSon = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_CuongPhongThienY = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_KimChungCuongKhi = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_PhiHoaDiemThuy = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_BachBien_ThanHanh = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_DonXuatNghichCanh = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_HanhPhongLongVu = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_TuyetAnhXaHon = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_PhaGiapThuHon = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_LongHong_PhuThe = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhThien_DongDia = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_DietThe_CuongVong = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienLy_NhatKich = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_HinhDi_YeuTuong = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_NhatChieuSatThan = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_LongTraoChiThu = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienMaChiLuc = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhDao_HaiLang = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_BatTu_ChiKhu = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_HuyenVuTroChu = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_SatNhanQuy = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_MaHonChiLuc = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_LangKinhThoiLe = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_SatTinhQuangPhu = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_KyQuanQuanHung = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_PhaKhongTruyTinh = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_1_PhanNoDieuTiet = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_2_CoDocGiaiTru = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_3_ThanLucBaoHo = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_ThiDocBaoPhat = 0;
		VatPham_ThuocTinh_ThemVao_ThangThien_5_TriTan = 0;
		VatPham_ThuocTinh_ThemVao_CuongHoa = 0.0;
		Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu = 0;
		Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich = 0;
	}

	public void DatDuocVatPham_ThuocTinhPhuongThuc(string userid, string username)
	{
		try
		{
			Clear_VatPham_ThuocTinh_方法();
			if (BitConverter.ToInt32(VatPham_ID, 0) != 0)
			{
				DatDuocVatPhamCoBanCongKichLuc(userid, username);
				var array = new byte[4];
				System.Buffer.BlockCopy(VatPham_byte, 16, array, 0, 4);
				DatDuocCuongHoa(BitConverter.ToInt32(array, 0).ToString());
				for (var i = 0; i < 4; i++)
				{
					var array2 = new byte[4];
					System.Buffer.BlockCopy(VatPham_byte, 20 + i * 4, array2, 0, 4);
					DatDuocCoBanThuocTinh(BitConverter.ToInt32(array2, 0).ToString());
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DatDuocVatPham_ThuocTinhPhuongThuc error：" + ex);
		}
	}

	private void DatDuocCuongHoa(string CuongHoaThanThu)
	{
		try
		{
			if (CuongHoaThanThu.Length != 9)
			{
				return;
			}
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 20, array, 0, 4);
			var text = BitConverter.ToInt32(array, 0).ToString();
			var num = 0;
			if (text != "0")
			{
				num = ((text.Length <= 2) ? int.Parse(text) : int.Parse(text.Substring(text.Length - 2, 2)));
			}
			VatPham_ThuocTinh_Manh_Loai_Hinh = int.Parse(CuongHoaThanThu.Substring(CuongHoaThanThu.Length - 9, 2));
			VatPham_ThuocTinh_Manh = int.Parse(CuongHoaThanThu.Substring(CuongHoaThanThu.Length - 2, 2));
			if (VatPham_ThuocTinh_Manh_Loai_Hinh != 19 || FLD_RESIDE2 != 16 || !World.ItemList.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out var value))
			{
				return;
			}
			var num2 = 0;
			var num3 = 0;
			var num4 = 0;
			var num5 = 0;
			var num6 = 0;
			if (VatPham_ThuocTinh_Manh >= 0 && VatPham_ThuocTinh_Manh <= 99)
			{
				switch (num)
				{
				default:
					if (VatPham_ThuocTinh_Manh % 2 == 0)
					{
						num2 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 4;
						num3 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 4;
						num4 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 4;
						num5 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 4;
						num6 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 4;
					}
					else
					{
						num2 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 8;
						num3 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 8;
						num4 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 8;
						num5 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 8;
						num6 = (int)(VatPham_ThuocTinh_Manh * 0.5) + 8;
					}
					break;
				case 1:
					if (VatPham_ThuocTinh_Manh % 2 == 0)
					{
						num2 = (int)(VatPham_ThuocTinh_Manh * 0.6) + 5;
						num3 = VatPham_ThuocTinh_Manh * 2 + 16;
						num4 = VatPham_ThuocTinh_Manh * 2 + 16;
						num5 = VatPham_ThuocTinh_Manh * 2 + 16;
						num6 = VatPham_ThuocTinh_Manh * 2 + 16;
					}
					else
					{
						num2 = (int)(VatPham_ThuocTinh_Manh * 0.6) + 6;
						num3 = VatPham_ThuocTinh_Manh * 2 + 18;
						num4 = VatPham_ThuocTinh_Manh * 2 + 18;
						num5 = VatPham_ThuocTinh_Manh * 2 + 18;
						num6 = VatPham_ThuocTinh_Manh * 2 + 18;
					}
					break;
				case 2:
					if (VatPham_ThuocTinh_Manh % 2 == 0)
					{
						num2 = (int)(VatPham_ThuocTinh_Manh * 0.7) + 8;
						num3 = VatPham_ThuocTinh_Manh * 4 + 32;
						num4 = VatPham_ThuocTinh_Manh * 4 + 32;
						num5 = VatPham_ThuocTinh_Manh * 4 + 32;
						num6 = VatPham_ThuocTinh_Manh * 4 + 32;
					}
					else
					{
						num2 = (int)(VatPham_ThuocTinh_Manh * 0.7) + 9;
						num3 = VatPham_ThuocTinh_Manh * 4 + 32 + 4;
						num4 = VatPham_ThuocTinh_Manh * 4 + 32 + 4;
						num5 = VatPham_ThuocTinh_Manh * 4 + 32 + 4;
						num6 = VatPham_ThuocTinh_Manh * 4 + 32 + 4;
					}
					break;
				case 3:
					if (value.FLD_PID == 1000001174)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 2;
							num3 = VatPham_ThuocTinh_Manh * 2 + 40;
							num4 = VatPham_ThuocTinh_Manh * 2 + 40;
							num5 = VatPham_ThuocTinh_Manh * 2 + 50;
							num6 = VatPham_ThuocTinh_Manh * 2 + 50;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 2;
							num3 = VatPham_ThuocTinh_Manh * 3 + 40 + 2;
							num4 = VatPham_ThuocTinh_Manh * 3 + 40 + 2;
							num5 = VatPham_ThuocTinh_Manh * 4 + 50 + 3;
							num6 = VatPham_ThuocTinh_Manh * 4 + 50 + 3;
						}
					}
					else if (value.FLD_PID == 1000001172)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 13;
							num3 = VatPham_ThuocTinh_Manh * 3 + 44;
							num4 = VatPham_ThuocTinh_Manh * 3 + 44;
							num5 = VatPham_ThuocTinh_Manh * 3 + 50;
							num6 = VatPham_ThuocTinh_Manh * 3 + 50;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 13;
							num3 = VatPham_ThuocTinh_Manh * 4 + 44 + 3;
							num4 = VatPham_ThuocTinh_Manh * 4 + 44 + 3;
							num5 = VatPham_ThuocTinh_Manh * 5 + 50 + 4;
							num6 = VatPham_ThuocTinh_Manh * 5 + 50 + 4;
						}
					}
					else if (value.FLD_PID == 1000001173)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 34;
							num3 = VatPham_ThuocTinh_Manh * 4 + 48;
							num4 = VatPham_ThuocTinh_Manh * 4 + 48;
							num5 = VatPham_ThuocTinh_Manh * 5 + 55;
							num6 = VatPham_ThuocTinh_Manh * 5 + 55;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 34;
							num3 = VatPham_ThuocTinh_Manh * 5 + 48 + 4;
							num4 = VatPham_ThuocTinh_Manh * 5 + 48 + 4;
							num5 = VatPham_ThuocTinh_Manh * 6 + 55 + 5;
							num6 = VatPham_ThuocTinh_Manh * 6 + 55 + 5;
						}
					}
					else if (value.FLD_PID == 1000001175)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 55;
							num3 = VatPham_ThuocTinh_Manh * 5 + 64;
							num4 = VatPham_ThuocTinh_Manh * 5 + 64;
							num5 = VatPham_ThuocTinh_Manh * 6 + 70;
							num6 = VatPham_ThuocTinh_Manh * 7 + 90;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 55;
							num3 = VatPham_ThuocTinh_Manh * 6 + 64 + 5;
							num4 = VatPham_ThuocTinh_Manh * 6 + 64 + 5;
							num5 = VatPham_ThuocTinh_Manh * 7 + 75 + 6;
							num6 = VatPham_ThuocTinh_Manh * 8 + 95 + 7;
						}
					}
					else if (value.FLD_PID == 1000001171)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 96;
							num3 = VatPham_ThuocTinh_Manh * 6 + 72;
							num4 = VatPham_ThuocTinh_Manh * 6 + 72;
							num5 = VatPham_ThuocTinh_Manh * 7 + 80;
							num6 = VatPham_ThuocTinh_Manh * 8 + 115;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 96;
							num3 = VatPham_ThuocTinh_Manh * 8 + 72 + 6;
							num4 = VatPham_ThuocTinh_Manh * 8 + 72 + 6;
							num5 = VatPham_ThuocTinh_Manh * 9 + 85 + 7;
							num6 = VatPham_ThuocTinh_Manh * 10 + 120 + 8;
						}
					}
					else if (value.FLD_PID == 1000001170)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 157;
							num3 = VatPham_ThuocTinh_Manh * 7 + 80;
							num4 = VatPham_ThuocTinh_Manh * 7 + 80;
							num5 = VatPham_ThuocTinh_Manh * 8 + 95;
							num6 = VatPham_ThuocTinh_Manh * 9 + 125;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 157;
							num3 = VatPham_ThuocTinh_Manh * 11 + 80 + 7;
							num4 = VatPham_ThuocTinh_Manh * 11 + 80 + 7;
							num5 = VatPham_ThuocTinh_Manh * 12 + 100 + 8;
							num6 = VatPham_ThuocTinh_Manh * 13 + 130 + 9;
						}
					}
					else if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
					{
						num2 = VatPham_ThuocTinh_Manh + 108;
						num3 = VatPham_ThuocTinh_Manh * 8 + 64;
						num4 = VatPham_ThuocTinh_Manh * 8 + 64;
						num5 = VatPham_ThuocTinh_Manh * 9 + 70;
						num6 = VatPham_ThuocTinh_Manh * 9 + 70;
					}
					else
					{
						num2 = VatPham_ThuocTinh_Manh + 108;
						num3 = VatPham_ThuocTinh_Manh * 8 + 64 + 8;
						num4 = VatPham_ThuocTinh_Manh * 8 + 64 + 8;
						num5 = VatPham_ThuocTinh_Manh * 9 + 70 + 9;
						num6 = VatPham_ThuocTinh_Manh * 9 + 70 + 9;
					}
					break;
				case 4:
					if (value.FLD_PID == 1000001174)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 4;
							num3 = VatPham_ThuocTinh_Manh * 4 + 60;
							num4 = VatPham_ThuocTinh_Manh * 4 + 60;
							num5 = VatPham_ThuocTinh_Manh * 4 + 70;
							num6 = VatPham_ThuocTinh_Manh * 4 + 70;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 4;
							num3 = VatPham_ThuocTinh_Manh * 5 + 60 + 4;
							num4 = VatPham_ThuocTinh_Manh * 5 + 60 + 4;
							num5 = VatPham_ThuocTinh_Manh * 6 + 70 + 5;
							num6 = VatPham_ThuocTinh_Manh * 6 + 70 + 5;
						}
					}
					else if (value.FLD_PID == 1000001172)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 33;
							num3 = VatPham_ThuocTinh_Manh * 5 + 64;
							num4 = VatPham_ThuocTinh_Manh * 5 + 64;
							num5 = VatPham_ThuocTinh_Manh * 5 + 70;
							num6 = VatPham_ThuocTinh_Manh * 5 + 70;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 33;
							num3 = VatPham_ThuocTinh_Manh * 6 + 64 + 5;
							num4 = VatPham_ThuocTinh_Manh * 6 + 64 + 5;
							num5 = VatPham_ThuocTinh_Manh * 7 + 70 + 6;
							num6 = VatPham_ThuocTinh_Manh * 7 + 70 + 6;
						}
					}
					else if (value.FLD_PID == 1000001173)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 54;
							num3 = VatPham_ThuocTinh_Manh * 6 + 68;
							num4 = VatPham_ThuocTinh_Manh * 6 + 68;
							num5 = VatPham_ThuocTinh_Manh * 7 + 75;
							num6 = VatPham_ThuocTinh_Manh * 7 + 75;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 54;
							num3 = VatPham_ThuocTinh_Manh * 7 + 68 + 6;
							num4 = VatPham_ThuocTinh_Manh * 7 + 68 + 6;
							num5 = VatPham_ThuocTinh_Manh * 8 + 75 + 7;
							num6 = VatPham_ThuocTinh_Manh * 8 + 75 + 7;
						}
					}
					else if (value.FLD_PID == 1000001175)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 75;
							num3 = VatPham_ThuocTinh_Manh * 7 + 84;
							num4 = VatPham_ThuocTinh_Manh * 7 + 84;
							num5 = VatPham_ThuocTinh_Manh * 8 + 90;
							num6 = VatPham_ThuocTinh_Manh * 9 + 110;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 75;
							num3 = VatPham_ThuocTinh_Manh * 8 + 84 + 7;
							num4 = VatPham_ThuocTinh_Manh * 8 + 84 + 7;
							num5 = VatPham_ThuocTinh_Manh * 9 + 95 + 8;
							num6 = VatPham_ThuocTinh_Manh * 10 + 115 + 9;
						}
					}
					else if (value.FLD_PID == 1000001171)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 116;
							num3 = VatPham_ThuocTinh_Manh * 8 + 92;
							num4 = VatPham_ThuocTinh_Manh * 8 + 92;
							num5 = VatPham_ThuocTinh_Manh * 9 + 100;
							num6 = VatPham_ThuocTinh_Manh * 10 + 135;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 116;
							num3 = VatPham_ThuocTinh_Manh * 10 + 92 + 8;
							num4 = VatPham_ThuocTinh_Manh * 10 + 92 + 8;
							num5 = VatPham_ThuocTinh_Manh * 11 + 105 + 9;
							num6 = VatPham_ThuocTinh_Manh * 11 + 140 + 10;
						}
					}
					else if (value.FLD_PID == 1000001170)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 177;
							num3 = VatPham_ThuocTinh_Manh * 9 + 100;
							num4 = VatPham_ThuocTinh_Manh * 9 + 100;
							num5 = VatPham_ThuocTinh_Manh * 10 + 115;
							num6 = VatPham_ThuocTinh_Manh * 11 + 145;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 177;
							num3 = VatPham_ThuocTinh_Manh * 13 + 100 + 9;
							num4 = VatPham_ThuocTinh_Manh * 13 + 100 + 9;
							num5 = VatPham_ThuocTinh_Manh * 14 + 120 + 10;
							num6 = VatPham_ThuocTinh_Manh * 15 + 150 + 11;
						}
					}
					else if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
					{
						num2 = VatPham_ThuocTinh_Manh + 128;
						num3 = VatPham_ThuocTinh_Manh * 10 + 84;
						num4 = VatPham_ThuocTinh_Manh * 10 + 84;
						num5 = VatPham_ThuocTinh_Manh * 11 + 90;
						num6 = VatPham_ThuocTinh_Manh * 11 + 90;
					}
					else
					{
						num2 = VatPham_ThuocTinh_Manh + 128;
						num3 = VatPham_ThuocTinh_Manh * 10 + 84 + 10;
						num4 = VatPham_ThuocTinh_Manh * 10 + 84 + 10;
						num5 = VatPham_ThuocTinh_Manh * 11 + 90 + 11;
						num6 = VatPham_ThuocTinh_Manh * 11 + 90 + 11;
					}
					break;
				case 5:
					if (value.FLD_PID == 1000001174)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 6;
							num3 = VatPham_ThuocTinh_Manh * 6 + 80;
							num4 = VatPham_ThuocTinh_Manh * 6 + 80;
							num5 = VatPham_ThuocTinh_Manh * 6 + 90;
							num6 = VatPham_ThuocTinh_Manh * 6 + 90;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 6;
							num3 = VatPham_ThuocTinh_Manh * 7 + 80 + 6;
							num4 = VatPham_ThuocTinh_Manh * 7 + 80 + 6;
							num5 = VatPham_ThuocTinh_Manh * 8 + 90 + 7;
							num6 = VatPham_ThuocTinh_Manh * 8 + 90 + 7;
						}
					}
					else if (value.FLD_PID == 1000001172)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 53;
							num3 = VatPham_ThuocTinh_Manh * 7 + 84;
							num4 = VatPham_ThuocTinh_Manh * 7 + 84;
							num5 = VatPham_ThuocTinh_Manh * 7 + 90;
							num6 = VatPham_ThuocTinh_Manh * 7 + 90;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 53;
							num3 = VatPham_ThuocTinh_Manh * 8 + 84 + 7;
							num4 = VatPham_ThuocTinh_Manh * 8 + 84 + 7;
							num5 = VatPham_ThuocTinh_Manh * 9 + 90 + 8;
							num6 = VatPham_ThuocTinh_Manh * 9 + 90 + 8;
						}
					}
					else if (value.FLD_PID == 1000001173)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 74;
							num3 = VatPham_ThuocTinh_Manh * 8 + 88;
							num4 = VatPham_ThuocTinh_Manh * 8 + 88;
							num5 = VatPham_ThuocTinh_Manh * 9 + 95;
							num6 = VatPham_ThuocTinh_Manh * 9 + 95;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 74;
							num3 = VatPham_ThuocTinh_Manh * 9 + 88 + 8;
							num4 = VatPham_ThuocTinh_Manh * 9 + 88 + 8;
							num5 = VatPham_ThuocTinh_Manh * 10 + 95 + 9;
							num6 = VatPham_ThuocTinh_Manh * 10 + 95 + 9;
						}
					}
					else if (value.FLD_PID == 1000001175)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 95;
							num3 = VatPham_ThuocTinh_Manh * 9 + 104;
							num4 = VatPham_ThuocTinh_Manh * 9 + 104;
							num5 = VatPham_ThuocTinh_Manh * 10 + 110;
							num6 = VatPham_ThuocTinh_Manh * 11 + 130;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 95;
							num3 = VatPham_ThuocTinh_Manh * 10 + 104 + 9;
							num4 = VatPham_ThuocTinh_Manh * 10 + 104 + 9;
							num5 = VatPham_ThuocTinh_Manh * 11 + 115 + 10;
							num6 = VatPham_ThuocTinh_Manh * 12 + 135 + 11;
						}
					}
					else if (value.FLD_PID == 1000001171)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 136;
							num3 = VatPham_ThuocTinh_Manh * 10 + 112;
							num4 = VatPham_ThuocTinh_Manh * 10 + 112;
							num5 = VatPham_ThuocTinh_Manh * 11 + 120;
							num6 = VatPham_ThuocTinh_Manh * 12 + 155;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 136;
							num3 = VatPham_ThuocTinh_Manh * 12 + 112 + 10;
							num4 = VatPham_ThuocTinh_Manh * 12 + 112 + 10;
							num5 = VatPham_ThuocTinh_Manh * 13 + 125 + 11;
							num6 = VatPham_ThuocTinh_Manh * 13 + 160 + 12;
						}
					}
					else if (value.FLD_PID == 1000001170)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 197;
							num3 = VatPham_ThuocTinh_Manh * 11 + 120;
							num4 = VatPham_ThuocTinh_Manh * 11 + 120;
							num5 = VatPham_ThuocTinh_Manh * 12 + 135;
							num6 = VatPham_ThuocTinh_Manh * 13 + 165;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 197;
							num3 = VatPham_ThuocTinh_Manh * 15 + 120 + 11;
							num4 = VatPham_ThuocTinh_Manh * 15 + 120 + 11;
							num5 = VatPham_ThuocTinh_Manh * 16 + 140 + 12;
							num6 = VatPham_ThuocTinh_Manh * 17 + 170 + 13;
						}
					}
					else if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
					{
						num2 = VatPham_ThuocTinh_Manh + 148;
						num3 = VatPham_ThuocTinh_Manh * 12 + 104;
						num4 = VatPham_ThuocTinh_Manh * 12 + 104;
						num5 = VatPham_ThuocTinh_Manh * 13 + 110;
						num6 = VatPham_ThuocTinh_Manh * 13 + 110;
					}
					else
					{
						num2 = VatPham_ThuocTinh_Manh + 148;
						num3 = VatPham_ThuocTinh_Manh * 12 + 104 + 12;
						num4 = VatPham_ThuocTinh_Manh * 12 + 104 + 12;
						num5 = VatPham_ThuocTinh_Manh * 13 + 110 + 13;
						num6 = VatPham_ThuocTinh_Manh * 13 + 110 + 13;
					}
					break;
				case 6:
					if (value.FLD_PID == 1000001174)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 8;
							num3 = VatPham_ThuocTinh_Manh * 8 + 100;
							num4 = VatPham_ThuocTinh_Manh * 8 + 100;
							num5 = VatPham_ThuocTinh_Manh * 8 + 110;
							num6 = VatPham_ThuocTinh_Manh * 8 + 110;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 8;
							num3 = VatPham_ThuocTinh_Manh * 9 + 100 + 8;
							num4 = VatPham_ThuocTinh_Manh * 9 + 100 + 8;
							num5 = VatPham_ThuocTinh_Manh * 10 + 110 + 9;
							num6 = VatPham_ThuocTinh_Manh * 10 + 110 + 9;
						}
					}
					else if (value.FLD_PID == 1000001172)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 73;
							num3 = VatPham_ThuocTinh_Manh * 9 + 104;
							num4 = VatPham_ThuocTinh_Manh * 9 + 104;
							num5 = VatPham_ThuocTinh_Manh * 9 + 110;
							num6 = VatPham_ThuocTinh_Manh * 9 + 110;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 73;
							num3 = VatPham_ThuocTinh_Manh * 10 + 104 + 9;
							num4 = VatPham_ThuocTinh_Manh * 10 + 104 + 9;
							num5 = VatPham_ThuocTinh_Manh * 11 + 110 + 10;
							num6 = VatPham_ThuocTinh_Manh * 11 + 110 + 10;
						}
					}
					else if (value.FLD_PID == 1000001173)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 94;
							num3 = VatPham_ThuocTinh_Manh * 10 + 108;
							num4 = VatPham_ThuocTinh_Manh * 10 + 108;
							num5 = VatPham_ThuocTinh_Manh * 11 + 115;
							num6 = VatPham_ThuocTinh_Manh * 11 + 115;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 94;
							num3 = VatPham_ThuocTinh_Manh * 11 + 108 + 10;
							num4 = VatPham_ThuocTinh_Manh * 11 + 108 + 10;
							num5 = VatPham_ThuocTinh_Manh * 12 + 115 + 11;
							num6 = VatPham_ThuocTinh_Manh * 12 + 115 + 11;
						}
					}
					else if (value.FLD_PID == 1000001175)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 115;
							num3 = VatPham_ThuocTinh_Manh * 11 + 124;
							num4 = VatPham_ThuocTinh_Manh * 11 + 124;
							num5 = VatPham_ThuocTinh_Manh * 12 + 130;
							num6 = VatPham_ThuocTinh_Manh * 13 + 150;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 115;
							num3 = VatPham_ThuocTinh_Manh * 12 + 124 + 11;
							num4 = VatPham_ThuocTinh_Manh * 12 + 124 + 11;
							num5 = VatPham_ThuocTinh_Manh * 13 + 135 + 12;
							num6 = VatPham_ThuocTinh_Manh * 14 + 155 + 13;
						}
					}
					else if (value.FLD_PID == 1000001171)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 156;
							num3 = VatPham_ThuocTinh_Manh * 12 + 132;
							num4 = VatPham_ThuocTinh_Manh * 12 + 132;
							num5 = VatPham_ThuocTinh_Manh * 13 + 140;
							num6 = VatPham_ThuocTinh_Manh * 14 + 175;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 156;
							num3 = VatPham_ThuocTinh_Manh * 14 + 132 + 12;
							num4 = VatPham_ThuocTinh_Manh * 14 + 132 + 12;
							num5 = VatPham_ThuocTinh_Manh * 15 + 145 + 13;
							num6 = VatPham_ThuocTinh_Manh * 15 + 180 + 14;
						}
					}
					else if (value.FLD_PID == 1000001170)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 217;
							num3 = VatPham_ThuocTinh_Manh * 13 + 140;
							num4 = VatPham_ThuocTinh_Manh * 13 + 140;
							num5 = VatPham_ThuocTinh_Manh * 14 + 155;
							num6 = VatPham_ThuocTinh_Manh * 15 + 185;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 217;
							num3 = VatPham_ThuocTinh_Manh * 17 + 140 + 13;
							num4 = VatPham_ThuocTinh_Manh * 17 + 140 + 13;
							num5 = VatPham_ThuocTinh_Manh * 18 + 160 + 14;
							num6 = VatPham_ThuocTinh_Manh * 19 + 190 + 15;
						}
					}
					else if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
					{
						num2 = VatPham_ThuocTinh_Manh + 168;
						num3 = VatPham_ThuocTinh_Manh * 14 + 124;
						num4 = VatPham_ThuocTinh_Manh * 14 + 124;
						num5 = VatPham_ThuocTinh_Manh * 15 + 130;
						num6 = VatPham_ThuocTinh_Manh * 15 + 130;
					}
					else
					{
						num2 = VatPham_ThuocTinh_Manh + 168;
						num3 = VatPham_ThuocTinh_Manh * 14 + 124 + 13;
						num4 = VatPham_ThuocTinh_Manh * 14 + 124 + 13;
						num5 = VatPham_ThuocTinh_Manh * 15 + 130 + 14;
						num6 = VatPham_ThuocTinh_Manh * 15 + 130 + 14;
					}
					break;
				case 7:
					if (value.FLD_PID == 1000001174)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 10;
							num3 = VatPham_ThuocTinh_Manh * 10 + 120;
							num4 = VatPham_ThuocTinh_Manh * 10 + 120;
							num5 = VatPham_ThuocTinh_Manh * 10 + 130;
							num6 = VatPham_ThuocTinh_Manh * 10 + 130;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 10;
							num3 = VatPham_ThuocTinh_Manh * 11 + 120 + 10;
							num4 = VatPham_ThuocTinh_Manh * 11 + 120 + 10;
							num5 = VatPham_ThuocTinh_Manh * 12 + 130 + 11;
							num6 = VatPham_ThuocTinh_Manh * 12 + 130 + 11;
						}
					}
					else if (value.FLD_PID == 1000001172)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 93;
							num3 = VatPham_ThuocTinh_Manh * 11 + 124;
							num4 = VatPham_ThuocTinh_Manh * 11 + 124;
							num5 = VatPham_ThuocTinh_Manh * 11 + 130;
							num6 = VatPham_ThuocTinh_Manh * 11 + 130;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 93;
							num3 = VatPham_ThuocTinh_Manh * 12 + 124 + 11;
							num4 = VatPham_ThuocTinh_Manh * 12 + 124 + 11;
							num5 = VatPham_ThuocTinh_Manh * 13 + 130 + 12;
							num6 = VatPham_ThuocTinh_Manh * 13 + 130 + 12;
						}
					}
					else if (value.FLD_PID == 1000001173)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 114;
							num3 = VatPham_ThuocTinh_Manh * 12 + 128;
							num4 = VatPham_ThuocTinh_Manh * 12 + 128;
							num5 = VatPham_ThuocTinh_Manh * 13 + 135;
							num6 = VatPham_ThuocTinh_Manh * 13 + 135;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 114;
							num3 = VatPham_ThuocTinh_Manh * 13 + 128 + 12;
							num4 = VatPham_ThuocTinh_Manh * 13 + 128 + 12;
							num5 = VatPham_ThuocTinh_Manh * 14 + 135 + 13;
							num6 = VatPham_ThuocTinh_Manh * 14 + 135 + 13;
						}
					}
					else if (value.FLD_PID == 1000001175)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 135;
							num3 = VatPham_ThuocTinh_Manh * 13 + 144;
							num4 = VatPham_ThuocTinh_Manh * 13 + 144;
							num5 = VatPham_ThuocTinh_Manh * 14 + 150;
							num6 = VatPham_ThuocTinh_Manh * 15 + 170;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 135;
							num3 = VatPham_ThuocTinh_Manh * 14 + 144 + 13;
							num4 = VatPham_ThuocTinh_Manh * 14 + 144 + 13;
							num5 = VatPham_ThuocTinh_Manh * 15 + 155 + 14;
							num6 = VatPham_ThuocTinh_Manh * 16 + 175 + 15;
						}
					}
					else if (value.FLD_PID == 1000001171)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 176;
							num3 = VatPham_ThuocTinh_Manh * 14 + 152;
							num4 = VatPham_ThuocTinh_Manh * 14 + 152;
							num5 = VatPham_ThuocTinh_Manh * 15 + 160;
							num6 = VatPham_ThuocTinh_Manh * 16 + 195;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 176;
							num3 = VatPham_ThuocTinh_Manh * 16 + 152 + 14;
							num4 = VatPham_ThuocTinh_Manh * 16 + 152 + 14;
							num5 = VatPham_ThuocTinh_Manh * 17 + 165 + 15;
							num6 = VatPham_ThuocTinh_Manh * 17 + 200 + 16;
						}
					}
					else if (value.FLD_PID == 1000001170)
					{
						if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
						{
							num2 = VatPham_ThuocTinh_Manh + 237;
							num3 = VatPham_ThuocTinh_Manh * 15 + 160;
							num4 = VatPham_ThuocTinh_Manh * 15 + 160;
							num5 = VatPham_ThuocTinh_Manh * 16 + 175;
							num6 = VatPham_ThuocTinh_Manh * 17 + 205;
						}
						else
						{
							num2 = VatPham_ThuocTinh_Manh + 237;
							num3 = VatPham_ThuocTinh_Manh * 19 + 160 + 15;
							num4 = VatPham_ThuocTinh_Manh * 19 + 160 + 15;
							num5 = VatPham_ThuocTinh_Manh * 20 + 180 + 16;
							num6 = VatPham_ThuocTinh_Manh * 21 + 210 + 17;
						}
					}
					else if (VatPham_ThuocTinh_Manh % 2 == 0 && VatPham_ThuocTinh_Manh != 10 && VatPham_ThuocTinh_Manh != 20 && VatPham_ThuocTinh_Manh != 30 && VatPham_ThuocTinh_Manh != 40 && VatPham_ThuocTinh_Manh != 50 && VatPham_ThuocTinh_Manh != 60 && VatPham_ThuocTinh_Manh != 70 && VatPham_ThuocTinh_Manh != 80 && VatPham_ThuocTinh_Manh != 90)
					{
						num2 = VatPham_ThuocTinh_Manh + 188;
						num3 = VatPham_ThuocTinh_Manh * 16 + 144;
						num4 = VatPham_ThuocTinh_Manh * 16 + 144;
						num5 = VatPham_ThuocTinh_Manh * 17 + 150;
						num6 = VatPham_ThuocTinh_Manh * 17 + 150;
					}
					else
					{
						num2 = VatPham_ThuocTinh_Manh + 188;
						num3 = VatPham_ThuocTinh_Manh * 16 + 14 + 15;
						num4 = VatPham_ThuocTinh_Manh * 16 + 144 + 15;
						num5 = VatPham_ThuocTinh_Manh * 17 + 150 + 16;
						num6 = VatPham_ThuocTinh_Manh * 17 + 150 + 16;
					}
					break;
				}
			}
			Vat_Pham_Luc_Cong_Kich += num2;
			Vat_Pham_Luc_Cong_KichMAX += num2;
			Vat_Pham_Luc_Phong_Ngu += num2;
			Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += num5;
			Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += num6;
			VatPham_ThuocTinh_VoCong_LucCongKich += num3 / 20;
			VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang += num4 / 2;
			if (VatPham_ThuocTinh_Manh >= 2)
			{
				if (VatPham_ThuocTinh_Manh % 2 == 0)
				{
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 10 - ((VatPham_ThuocTinh_Manh - 1) * 5 - 5);
				}
				else
				{
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 10 - (VatPham_ThuocTinh_Manh - 1) * 5;
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "[Cường Hóa Thần Thú] LỖI !! ：" + ex.ToString());
		}
	}

	public void DatDuocVatPham_ThuocTinhPhuongThuc(int 追加CuongHoa, int TriggerAttributePromotion, string userid, string username)
	{
		try
		{
			Clear_VatPham_ThuocTinh_方法();
			if (BitConverter.ToInt32(VatPham_ID, 0) != 0)
			{
				DatDuocVatPhamCoBanCongKichLuc(userid, username);
				VatPham_ThuocTinh_ThemVao_CuongHoa = 追加CuongHoa;
				var array = new byte[4];
				System.Buffer.BlockCopy(VatPham_byte, 16, array, 0, 4);
				DatDuocCuongHoa(BitConverter.ToInt32(array, 0).ToString(), TriggerAttributePromotion);
				for (var i = 0; i < 4; i++)
				{
					var array2 = new byte[4];
					System.Buffer.BlockCopy(VatPham_byte, 20 + i * 4, array2, 0, 4);
					DatDuocCoBanThuocTinh(BitConverter.ToInt32(array2, 0).ToString());
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DatDuocVatPham_ThuocTinhPhuongThuc error：" + ex);
		}
	}

	public void DatDuocVatPhamCoBanCongKichLuc(string userid, string username)
	{
		var num = 0;
		try
		{
			if (BitConverter.ToInt32(VatPham_ID, 0) == 0)
			{
				return;
			}
			num = 1;
			var itmeID = ItmeClass.GetItmeID((int)GetVatPham_ID);
			num = 2;
			if (itmeID == null)
			{
				return;
			}
			num = 3;
			FLD_RESIDE2 = itmeID.FLD_RESIDE2;
			num = 4;
			FLD_LEVEL = itmeID.FLD_LEVEL;
			num = 5;
			if (FLD_RESIDE2 == 4)
			{
				if (FLD_FJ_TienHoa == 0)
				{
					Vat_Pham_Luc_Cong_Kich = itmeID.FLD_AT;
					Vat_Pham_Luc_Cong_KichMAX = itmeID.FLD_AT_Max;
					Vat_Pham_Luc_Cong_KichNew = itmeID.FLD_AT;
					Vat_Pham_Luc_Cong_KichMaxNew = itmeID.FLD_AT_Max;
				}
				else if (FLD_FJ_TienHoa == 1)
				{
					Vat_Pham_Luc_Cong_Kich = (int)(itmeID.FLD_AT + itmeID.FLD_AT * 0.05);
					Vat_Pham_Luc_Cong_KichMAX = (int)(itmeID.FLD_AT_Max + itmeID.FLD_AT_Max * 0.05);
					Vat_Pham_Luc_Cong_KichNew = (int)(itmeID.FLD_AT + itmeID.FLD_AT * 0.05);
					Vat_Pham_Luc_Cong_KichMaxNew = (int)(itmeID.FLD_AT_Max + itmeID.FLD_AT_Max * 0.05);
				}
				else if (FLD_FJ_TienHoa == 2)
				{
					Vat_Pham_Luc_Cong_Kich = (int)(itmeID.FLD_AT + itmeID.FLD_AT * 0.08);
					Vat_Pham_Luc_Cong_KichMAX = (int)(itmeID.FLD_AT_Max + itmeID.FLD_AT_Max * 0.08);
					Vat_Pham_Luc_Cong_KichNew = (int)(itmeID.FLD_AT + itmeID.FLD_AT * 0.08);
					Vat_Pham_Luc_Cong_KichMaxNew = (int)(itmeID.FLD_AT_Max + itmeID.FLD_AT_Max * 0.08);
				}
			}
			else if (FLD_RESIDE2 == 13)
			{
				Vat_Pham_Luc_Cong_Kich = itmeID.FLD_AT;
				Vat_Pham_Luc_Cong_KichMAX = itmeID.FLD_AT_Max;
				Vat_Pham_Luc_Cong_KichNew = itmeID.FLD_AT;
				Vat_Pham_Luc_Cong_KichMaxNew = itmeID.FLD_AT_Max;
			}
			else
			{
				VatPham_ThuocTinh_LaChan_GiaTang = itmeID.FLD_LEVEL;
				if (FLD_FJ_TienHoa == 0)
				{
					Vat_Pham_Luc_Phong_Ngu = itmeID.FLD_DF;
					Vat_Pham_Luc_Phong_NguNew = itmeID.FLD_DF;
				}
				else if (FLD_FJ_TienHoa == 1)
				{
					Vat_Pham_Luc_Phong_Ngu = (int)(itmeID.FLD_DF + itmeID.FLD_DF * 0.1);
					Vat_Pham_Luc_Phong_NguNew = (int)(itmeID.FLD_DF + itmeID.FLD_DF * 0.1);
				}
				else if (FLD_FJ_TienHoa == 2)
				{
					Vat_Pham_Luc_Phong_Ngu = (int)(itmeID.FLD_DF + itmeID.FLD_DF * 0.153);
					Vat_Pham_Luc_Phong_NguNew = (int)(itmeID.FLD_DF + itmeID.FLD_DF * 0.153);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "VatPham CongKichLuc Lỗi num loi:[" + ItmeClass.GetItmeID((int)GetVatPham_ID)?.ToString() + "] num:[" + num + "] - [" + userid + "][ " + username + "] - " + ex.Message);
		}
	}

	private void DatDuocCoBanThuocTinh(string ysqh)
	{
		try
		{
			string text;
			switch (ysqh.Length)
			{
			default:
				return;
			case 9:
				text = ysqh.Substring(0, 2);
				break;
			case 8:
				text = ysqh.Substring(0, 1);
				break;
			}
			var num = ((World.PhaiChang_HoTroMoRong_CacVatPham_ChuSo != 0) ? (text != "8") ? (int.Parse(ysqh) - int.Parse(text) * 10000000) : int.Parse(ysqh.Substring(ysqh.Length - 2, 2)) : text!= "8" ? int.Parse(ysqh.Substring(ysqh.Length - 3, 3)) : int.Parse(ysqh.Substring(ysqh.Length - 2, 2)));
			switch (int.Parse(text))
			{
			case 14:
				break;
			case 1:
				VatPham_ThuocTinh_LucCongKich_GiaTang += num;
				Vat_Pham_Luc_Cong_Kich += num;
				Vat_Pham_Luc_Cong_KichMAX += num;
				switch (num)
				{
				case 20:
					num += World.g20;
					break;
				case 21:
					num += World.g21;
					break;
				case 22:
					num += World.g22;
					break;
				case 23:
					num += World.g23;
					break;
				case 24:
					num += World.g24;
					break;
				case 25:
					num += World.g25;
					break;
				}
				Vat_Pham_Luc_Cong_KichNew += num;
				Vat_Pham_Luc_Cong_KichMaxNew += num;
				break;
			case 2:
				VatPham_ThuocTinh_LucPhongNgu_GiaTang += num;
				Vat_Pham_Luc_Phong_Ngu += num;
				switch (num)
				{
				case 10:
					num += World.f10;
					break;
				case 11:
					num += World.f11;
					break;
				case 12:
					num += World.f12;
					break;
				case 13:
					num += World.f13;
					break;
				case 14:
					num += World.f14;
					break;
				case 15:
					num += World.f15;
					break;
				}
				Vat_Pham_Luc_Phong_NguNew += num / 5;
				break;
			case 3:
				VatPham_ThuocTinh_SinhMenhLuc_GiaTang += num;
				break;
			case 4:
				VatPham_ThuocTinh_NoiCong_Luc_GiaTang += num;
				break;
			case 5:
				VatPham_ThuocTinh_TiLeChinhXac_GiaTang += num;
				break;
			case 6:
				VatPham_ThuocTinh_NeTranh_Suat_GiaTang += num;
				break;
			case 7:
				VatPham_ThuocTinh_VoCong_LucCongKich += num;
				switch (num)
				{
				case 25:
					num += World.wg25;
					break;
				case 26:
					num += World.wg26;
					break;
				case 27:
					num += World.wg27;
					break;
				case 28:
					num += World.wg28;
					break;
				case 29:
					num += World.wg29;
					break;
				case 30:
					num += World.wg30;
					break;
				case 31:
					num += World.wg31;
					break;
				case 32:
					num += World.wg32;
					break;
				case 33:
					num += World.wg33;
					break;
				case 34:
					num += World.wg34;
					break;
				case 35:
					num += World.wg35;
					break;
				case 36:
					num += World.wg36;
					break;
				case 37:
					num += World.wg37;
					break;
				case 38:
					num += World.wg38;
					break;
				case 39:
					num += World.wg39;
					break;
				case 40:
					num += World.wg40;
					break;
				}
				VatPham_ThuocTinh_VoCong_LucCongKichNew += num;
				break;
			case 8:
			{
				var text2 = ysqh.Substring(3, 3);
				if (text2 == null || text2 == null)
				{
					break;
				}
				if (text2 == "000" && num > 0)
				{
					VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += num;
					break;
				}
				switch (text2)
				{
				default:
					if (!(text2 == "193"))
					{
						switch (text2)
						{
						case "010":
							VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon += num;
							break;
						case "011":
							VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich += num;
							break;
						case "012":
							VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu += num;
							break;
						case "014":
							VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha += num;
							break;
						case "019":
							VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon += num;
							break;
						case "016":
							VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap += num;
							break;
						case "017":
							VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich += num;
							VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich += num;
							break;
						case "015":
							VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan += num;
							break;
						case "018":
							VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat += num;
							break;
						case "312":
							VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran += num;
							break;
						case "110":
							VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu += num;
							break;
						case "020":
							VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat += num;
							break;
						case "021":
							VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh += num;
							break;
						case "022":
							VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu += num;
							break;
						case "023":
							VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem += num;
							break;
						case "024":
							VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha += num;
							break;
						case "026":
							VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc += num;
							break;
						case "028":
							VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan += num;
							break;
						case "027":
							VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap += num;
							break;
						case "120":
							VatPham_ThuocTinh_ThemVao_KIEM_NhanKiem_NhatThe += num;
							break;
						case "320":
							VatPham_ThuocTinh_ThemVao_KIEM_HonNguyen_KiemPhap += num;
							break;
						case "029":
							VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo += num;
							break;
						case "030":
							VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi += num;
							break;
						case "031":
							VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong += num;
							break;
						case "032":
							VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu += num;
							break;
						case "034":
							VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha += num;
							break;
						case "035":
							VatPham_ThuocTinh_ThemVao_THUONG_HoanhLuyenThaiBao += num;
							break;
						case "039":
							VatPham_ThuocTinh_ThemVao_THUONG_ChuyenThuViCong += num;
							break;
						case "038":
							VatPham_ThuocTinh_ThemVao_THUONG_CuongThanHangThe += num;
							break;
						case "036":
							VatPham_ThuocTinh_ThemVao_THUONG_CanKhonNaDi += num;
							break;
						case "130":
							VatPham_ThuocTinh_ThemVao_THUONG_MatNhatCuongVu += num;
							break;
						case "332":
							VatPham_ThuocTinh_ThemVao_THUONG_NoYChiHong += num;
							break;
						case "037":
							VatPham_ThuocTinh_ThemVao_THUONG_LinhGiapHoThan += num;
							break;
						case "040":
							VatPham_ThuocTinh_ThemVao_CUNG_BachBoXuyenDuong += num;
							break;
						case "041":
							VatPham_ThuocTinh_ThemVao_CUNG_LiepUngChiNhan += num;
							break;
						case "042":
							VatPham_ThuocTinh_ThemVao_CUNG_NgungThanTuKhi += num;
							break;
						case "044":
							VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha += num;
							break;
						case "045":
							VatPham_ThuocTinh_ThemVao_CUNG_ChinhBanBoiNguyen += num;
							break;
						case "048":
							VatPham_ThuocTinh_ThemVao_CUNG_NhueLoiChiTien += num;
							break;
						case "046":
							VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu += num;
							VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu += num;
							break;
						case "047":
							VatPham_ThuocTinh_ThemVao_CUNG_LuuTinhTamThi += num;
							break;
						case "043":
							VatPham_ThuocTinh_ThemVao_CUNG_HoiLuuChanKhi += num;
							break;
						case "049":
							VatPham_ThuocTinh_ThemVao_CUNG_VoMinhAmThi += num;
							break;
						case "140":
							VatPham_ThuocTinh_ThemVao_CUNG_TriMenhTuyetSat += num;
							break;
						case "050":
							VatPham_ThuocTinh_ThemVao_DAIPHU_VanKhiHanhTam += num;
							break;
						case "051":
							VatPham_ThuocTinh_ThemVao_DAIPHU_ThaiCucTamPhap += num;
							break;
						case "052":
							VatPham_ThuocTinh_ThemVao_DAIPHU_TheHuyetBoiTang += num;
							break;
						case "053":
							VatPham_ThuocTinh_ThemVao_DAIPHU_TayTuyDichKinh += num;
							break;
						case "054":
							VatPham_ThuocTinh_ThemVao_DAIPHU_DieuThuHoiXuan += num;
							break;
						case "055":
							VatPham_ThuocTinh_ThemVao_DAIPHU_TruongCongCongKich += num;
							break;
						case "057":
							VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich += num;
							VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich += num;
							break;
						case "056":
							VatPham_ThuocTinh_ThemVao_DAIPHU_HapTinhDaiPhap += num;
							break;
						case "350":
							VatPham_ThuocTinh_ThemVao_DAIPHU_CuongYHoThe += num;
							break;
						case "351":
							VatPham_ThuocTinh_ThemVao_DAIPHU_VoTrungSinhHuu += num;
							break;
						case "059":
							VatPham_ThuocTinh_ThemVao_DAIPHU_CuuThienChanKhi += num;
							break;
						case "070":
							VatPham_ThuocTinh_ThemVao_NINJA__KinhKhaChiNo += num;
							break;
						case "071":
							VatPham_ThuocTinh_ThemVao_NINJA__TamHoaTuDinh += num;
							break;
						case "072":
							VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu += num;
							VatPham_ThuocTinh_ThemVao_NINJA__LienHoanPhiVu += num;
							break;
						case "074":
							VatPham_ThuocTinh_ThemVao_NINJA__TamThanNgungTu += num;
							VatPham_ThuocTinh_ThemVao_CUNG_TamThanNgungTu += num;
							break;
						case "075":
							VatPham_ThuocTinh_ThemVao_NINJA__TriThuTuyetMenh += num;
							break;
						case "372":
							VatPham_ThuocTinh_ThemVao_NINJA__DiNoHoanNo += num;
							break;
						case "076":
							VatPham_ThuocTinh_ThemVao_NINJA__TienPhatCheNhan += num;
							break;
						case "077":
							VatPham_ThuocTinh_ThemVao_NINJA__ThienChuVanThu += num;
							break;
						case "078":
							VatPham_ThuocTinh_ThemVao_NINJA__LienTieuDaiDa += num;
							break;
						case "079":
							VatPham_ThuocTinh_ThemVao_NINJA__KiemNhanLoanVu += num;
							break;
						case "073":
							VatPham_ThuocTinh_ThemVao_NINJA__NhatChieuTanSat += num;
							break;
						case "080":
							VatPham_ThuocTinh_ThemVao_CAMSU_ChienMaBonDang += num;
							break;
						case "081":
							VatPham_ThuocTinh_ThemVao_CAMSU_ThuGiangDaBac += num;
							break;
						case "082":
							VatPham_ThuocTinh_ThemVao_CAMSU_ThanhTamPhoThien += num;
							break;
						case "083":
							VatPham_ThuocTinh_ThemVao_CAMSU_DuongQuanTamDiep += num;
							break;
						case "084":
							VatPham_ThuocTinh_ThemVao_CAMSU_HanCungThuNguyet += num;
							break;
						case "085":
							VatPham_ThuocTinh_ThemVao_CAMSU_CaoSonLuuThuy += num;
							break;
						case "086":
							VatPham_ThuocTinh_ThemVao_CAMSU_NhacDuongTamTuy += num;
							break;
						case "087":
							VatPham_ThuocTinh_ThemVao_CAMSU_MaiHoaTamLong += num;
							break;
						case "088":
							VatPham_ThuocTinh_ThemVao_CAMSU_LoanPhuongHoaMinh += num;
							break;
						case "089":
							VatPham_ThuocTinh_ThemVao_CAMSU_DuongMinhXuanHieu += num;
							break;
						case "180":
							VatPham_ThuocTinh_ThemVao_CAMSU_TieuTuongVuDa += num;
							break;
						case "250":
							VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_LucPhachHoaSon += num;
							break;
						case "251":
							VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_NhiepHonNhatKich += num;
							break;
						case "253":
							VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh += num;
							break;
						case "254":
							VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha += num;
							break;
						case "252":
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_ThienMaCuongHuyet += num;
							break;
						case "255":
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_TruyCotHapNguyen += num;
							break;
						case "256":
							VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_BaKhi_PhaGiap += num;
							break;
						case "257":
							VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_ChanVu_TuyetKich += num;
							VatPham_ThuocTinh_ThemVao_DAIPHU_ChanVu_TuyetKich += num;
							break;
						case "259":
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_HoaLongVanDinh += num;
							break;
						case "260":
							VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_LuuQuang_LoanVu += num;
							break;
						case "258":
							VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_AmAnh_TuyetSat += num;
							break;
						case "270":
							VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_TruongHong_QuanNhat += num;
							break;
						case "271":
							VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_BachBien_ThanHanh += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_BachBien_ThanHanh += num;
							break;
						case "272":
							VatPham_ThuocTinh_ThemVao_DamHoaLien_New_LienHoanPhiVu += num;
							break;
						case "273":
							VatPham_ThuocTinh_ThemVao_DamHoaLien_ChieuThucTanPhap += num;
							break;
						case "274":
							VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_THUONG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_CUNG_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_HanBaoQuan_CuongPhong_VanPha += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_CuongPhong_VanPha += num;
							break;
						case "275":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThan_CuongKhi += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_HoThan_CuongKhi += num;
							break;
						case "276":
							VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_DiHoa_TiepMoc += num;
							break;
						case "277":
							VatPham_ThuocTinh_ThemVao_DamHoaLien_TungHoanhVoSong += num;
							break;
						case "278":
							VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_HoiLieu_ThanPhap += num;
							break;
						case "279":
							VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_NoHai_CuongLan += num;
							break;
						case "280":
							VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo += num;
							VatPham_ThuocTinh_ThemVao_DamHoaLien_TrungQuan_NhatNo += num;
							break;
						case "550":
							VatPham_ThuocTinh_ThemVao_QuyenSu_NoHaoNhatThanh += num;
							break;
						case "551":
							VatPham_ThuocTinh_ThemVao_QuyenSu_VanKhiLieuThuong += num;
							break;
						case "552":
							VatPham_ThuocTinh_ThemVao_QuyenSu_NgungThanTuKhi += num;
							break;
						case "553":
							VatPham_ThuocTinh_ThemVao_QuyenSu_CuongPhongVanPha += num;
							break;
						case "558":
							VatPham_ThuocTinh_ThemVao_QuyenSu_LinhGiapHoThan += num;
							break;
						case "559":
							VatPham_ThuocTinh_ThemVao_QuyenSu_MaPhuViCham += num;
							break;
						case "556":
							VatPham_ThuocTinh_ThemVao_QuyenSu_VatNgaNhatThe += num;
							break;
						case "554":
							VatPham_ThuocTinh_ThemVao_QuyenSu_KimCuongBatHoai += num;
							break;
						case "555":
							VatPham_ThuocTinh_ThemVao_QuyenSu_PhongQuyDiemBich += num;
							break;
						case "557":
							VatPham_ThuocTinh_ThemVao_QuyenSu_NoTamXuatKich += num;
							break;
						case "560":
							VatPham_ThuocTinh_ThemVao_QuyenSu_ThienHaCuongPhong += num;
							break;
						case "650":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_KichHoatCuongLuc += num;
							break;
						case "651":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapThuCuongLuc += num;
							break;
						case "653":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_BachBienThanHanh += num;
							break;
						case "654":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_CuongPhongVanPhaMLC += num;
							break;
						case "656":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuChiDiem += num;
							break;
						case "657":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuCuongKich += num;
							break;
						case "658":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_HuyenVuNguyHoa += num;
							break;
						case "659":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_HoThanKhoiNguyen += num;
							break;
						case "660":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_TatDoHoaThan += num;
							break;
						case "661":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_NoKhiXungThien += num;
							break;
						case "318":
							VatPham_ThuocTinh_ThemVao_MaiLieuChan_HapHuyetTienCong += num;
							break;
						case "281":
							VatPham_ThuocTinh_ThemVao_TuHao_KimChungCanhKhi += num;
							break;
						case "282":
							VatPham_ThuocTinh_ThemVao_TuHao_VanKhiLieuThuong += num;
							break;
						case "283":
							VatPham_ThuocTinh_ThemVao_TuHao_LienHoanPhiVu += num;
							break;
						case "284":
							VatPham_ThuocTinh_ThemVao_TuHao_ChanhBanBoiNguyen += num;
							break;
						case "285":
							VatPham_ThuocTinh_ThemVao_TuHao_CuongPhongVanPha += num;
							break;
						case "287":
							VatPham_ThuocTinh_ThemVao_TuHao_ChanVuTuyetKich += num;
							break;
						case "286":
							VatPham_ThuocTinh_ThemVao_TuHao_DiTinhVanThien += num;
							break;
						case "288":
							VatPham_ThuocTinh_ThemVao_TuHao_PhatNhietKhiDan += num;
							break;
						case "289":
							VatPham_ThuocTinh_ThemVao_TuHao_DiCongViThu += num;
							break;
						case "290":
							VatPham_ThuocTinh_ThemVao_TuHao_NhatDiemNguHanh += num;
							break;
						case "291":
							VatPham_ThuocTinh_ThemVao_TuHao_ToiCuongHongDiem += num;
							break;
						case "450":
							VatPham_ThuocTinh_ThemVao_ThanNu_VanKhiHanhTam += num;
							break;
						case "451":
							VatPham_ThuocTinh_ThemVao_ThanNu_ThaiCucTamPhap += num;
							break;
						case "452":
							VatPham_ThuocTinh_ThemVao_ThanNu_ThanLucKichPhat += num;
							break;
						case "453":
							VatPham_ThuocTinh_ThemVao_ThanNu_SatTinhNghiaKhi += num;
							break;
						case "454":
							VatPham_ThuocTinh_ThemVao_ThanNu_TayTuyDichCan += num;
							break;
						case "455":
							VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaManKhai += num;
							break;
						case "456":
							VatPham_ThuocTinh_ThemVao_ThanNu_DieuThuHoiXuan += num;
							break;
						case "457":
							VatPham_ThuocTinh_ThemVao_ThanNu_TruongCongKichLuc += num;
							break;
						case "458":
							VatPham_ThuocTinh_ThemVao_ThanNu_HacHoaTapTrung += num;
							break;
						case "459":
							VatPham_ThuocTinh_ThemVao_ThanNu_ChanVuTuyetKich += num;
							break;
						case "460":
							VatPham_ThuocTinh_ThemVao_ThanNu_VanDocBatXam += num;
							break;
						case "310":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_DonXuatNghichCanh += num;
							break;
						case "311":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_CungDoMatLo += num;
							break;
						case "013":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaLong_ChiHoa += num;
							break;
						case "679":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_LongHong_PhuThe += num;
							break;
						case "025":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThan_CuongKhi += num;
							break;
						case "680":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhThien_DongDia += num;
							break;
						case "330":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_PhaGiapThuHon += num;
							break;
						case "331":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_DiThoiViTien += num;
							break;
						case "033":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_NoYChiHoa += num;
							break;
						case "681":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_DietThe_CuongVong += num;
							break;
						case "340":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_TuyetAnhXaHon += num;
							break;
						case "341":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienQuanApDa += num;
							break;
						case "342":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_ThienNgoaiTamThi += num;
							break;
						case "682":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienLy_NhatKich += num;
							break;
						case "058":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_CuongPhongThienY += num;
							break;
						case "387":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_HoThanKhiGiap += num;
							break;
						case "150":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_VanVatHoiXuan += num;
							break;
						case "352":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_MinhKinhChiThuy += num;
							break;
						case "683":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_HinhDi_YeuTuong += num;
							break;
						case "370":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_DaMaTrienThan += num;
							break;
						case "371":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_ThuanThuyThoiChu += num;
							break;
						case "170":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_VoTinhDaKich += num;
							break;
						case "684":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_NhatChieuSatThan += num;
							break;
						case "390":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_PhiHoaDiemThuy += num;
							break;
						case "391":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_TamDamAnhNguyet += num;
							break;
						case "392":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_TuDaThuCa += num;
							break;
						case "685":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_LongTraoChiThu += num;
							break;
						case "600":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_HanhPhongLongVu += num;
							break;
						case "601":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienMaHoThe += num;
							break;
						case "602":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_NoiTucHanhTam += num;
							break;
						case "686":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_ThienMaChiLuc += num;
							break;
						case "700":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_DiNhuKhacCuong += num;
							break;
						case "687":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_KinhDao_HaiLang += num;
							break;
						case "321":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_ThienDiaDongTho += num;
							break;
						case "322":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_HoaPhuongLamTrieu += num;
							break;
						case "561":
							VatPham_ThuocTinh_ThemVao_ThangThien_DoatMenhLienHoan += num;
							break;
						case "562":
							VatPham_ThuocTinh_ThemVao_ThangThien_DienQuangThachHoa += num;
							break;
						case "563":
							VatPham_ThuocTinh_ThemVao_ThangThien_TinhIchCauTinh += num;
							break;
						case "688":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_BatTu_ChiKhu += num;
							break;
						case "316":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_HuyenVuLoiDien += num;
							break;
						case "325":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_HuyenVuTroChu += num;
							break;
						case "315":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_SatNhanQuy += num;
							break;
						case "689":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_MaHonChiLuc += num;
							break;
						case "662":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_LangKinhThoiLe += num;
							break;
						case "663":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_SatTinhQuangPhu += num;
							break;
						case "664":
							_VatPham_ThuocTinh_ThemVao_ThangThien_3_KyQuanQuanHung += num;
							break;
						case "690":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_PhaKhongTruyTinh += num;
							break;
						case "610":
							VatPham_ThuocTinh_ThemVao_ThangThien_1_PhanNoDieuTiet += num;
							break;
						case "611":
							VatPham_ThuocTinh_ThemVao_ThangThien_2_CoDocGiaiTru += num;
							break;
						case "612":
							VatPham_ThuocTinh_ThemVao_ThangThien_3_ThanLucBaoHo += num;
							break;
						case "616":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_ThiDocBaoPhat += num;
							break;
						default:
							if (!(text2 == "678"))
							{
								switch (text2)
								{
								default:
									if (!(text2 == "666"))
									{
										switch (text2)
										{
										default:
											if (!(text2 == "665"))
											{
												switch (text2)
												{
												default:
													if (!(text2 == "613"))
													{
														if (text2 == "344" || text2 == "374" || text2 == "326")
														{
															VatPham_ThuocTinh_ThemVao_ThangThien_4_LietNhatViemViem += num;
															break;
														}
														if (text2 == "354" || text2 == "614")
														{
															VatPham_ThuocTinh_ThemVao_ThangThien_4_VongMaiThiemHoa += num;
															break;
														}
														if (text2 == "354" || text2 == "614")
														{
															VatPham_ThuocTinh_ThemVao_ThangThien_4_VongMaiThiemHoa += num;
															break;
														}
														if (text2 == "394")
														{
															VatPham_ThuocTinh_ThemVao_ThangThien_4_HuyenTiChanMach += num;
															break;
														}
														if (text2 == "603" || text2 == "701")
														{
															VatPham_ThuocTinh_ThemVao_ThangThien_4_TruongHongQuanThien += num;
															break;
														}
														if (text2 == "604" || text2 == "702")
														{
															VatPham_ThuocTinh_ThemVao_ThangThien_4_AiHongBienDa += num;
															break;
														}
														switch (text2)
														{
														case "380":
															VatPham_ThuocTinh_ThemVao_ThangThien_1_LucPhachHoaSon += num;
															break;
														case "381":
															VatPham_ThuocTinh_ThemVao_ThangThien_1_TruongHong_QuanNhat += num;
															break;
														case "382":
															VatPham_ThuocTinh_ThemVao_ThangThien_1_KimChungCuongKhi += num;
															break;
														case "383":
															VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhiHanhTam += num;
															break;
														case "384":
															VatPham_ThuocTinh_ThemVao_ThangThien_1_ChinhBanBoiNguyen += num;
															break;
														case "385":
															VatPham_ThuocTinh_ThemVao_ThangThien_1_VanKhi_LieuThuong += num;
															break;
														case "386":
															VatPham_ThuocTinh_ThemVao_ThangThien_1_BachBien_ThanHanh += num;
															break;
														}
														break;
													}
													goto case "343";
												case "343":
												case "353":
												case "373":
												case "393":
												case "327":
													VatPham_ThuocTinh_ThemVao_ThangThien_4_ManNguyetCuongPhong += num;
													break;
												}
												break;
											}
											goto case "314";
										case "314":
										case "324":
										case "334":
										case "565":
											VatPham_ThuocTinh_ThemVao_ThangThien_4_DocXaXuatDong += num;
											break;
										}
										break;
									}
									goto case "313";
								case "313":
								case "323":
								case "333":
								case "564":
									VatPham_ThuocTinh_ThemVao_ThangThien_4_HongNguyetCuongPhong += num;
									break;
								}
								break;
							}
							goto case "667";
						case "667":
						case "668":
						case "669":
						case "670":
						case "671":
						case "672":
						case "673":
						case "674":
						case "675":
						case "676":
						case "677":
							VatPham_ThuocTinh_ThemVao_ThangThien_5_TriTan += num;
							break;
						}
						break;
					}
					goto case "181";
				case "181":
				case "182":
				case "183":
				case "184":
				case "185":
				case "186":
				case "187":
				case "188":
				case "189":
				case "190":
				case "191":
				case "192":
					VatPham_ThuocTinh_ThemVao_ThanCoMinhChau += num;
					break;
				}
				break;
			}
			case 9:
				VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong += num;
				break;
			case 10:
				VatPham_ThuocTinh_ThemVao_MucThuongTon += num;
				break;
			case 11:
				VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang += num;
				try
				{
					switch (num)
					{
					case 85:
						num += World.wf85;
						break;
					case 68:
						num += World.wf68;
						break;
					case 70:
						num += World.wf70;
						break;
					case 72:
						num += World.wf72;
						break;
					case 74:
						num += World.wf74;
						break;
					case 76:
						num += World.wf76;
						break;
					case 78:
						num += World.wf78;
						break;
					case 80:
						num += World.wf80;
						break;
					case 100:
						num += World.wf100;
						break;
					case 95:
						num += World.wf95;
						break;
					case 90:
						num += World.wf90;
						break;
					}
					VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew += num;
					break;
				}
				catch
				{
					break;
				}
			case 12:
				VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += num;
				break;
			case 13:
				VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot += num;
				break;
			case 15:
				VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang += num;
				break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DatDuocCoBanThuocTinh error：" + ex);
		}
	}

	private int DatDuocTrangSucCuongHoaGiaiDoanGiaTang(int level, int CuongHoaGiaiDoan)
	{
		switch (level)
		{
		case 100:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 18;
			case 2:
				return 24;
			case 3:
				return 31;
			case 4:
				return 38;
			case 5:
				return 45;
			case 6:
				return 53;
			case 7:
				return 61;
			case 8:
				return 76;
			case 9:
				return 80;
			case 10:
				return 95;
			}
			break;
		case 80:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 16;
			case 2:
				return 21;
			case 3:
				return 27;
			case 4:
				return 33;
			case 5:
				return 40;
			case 6:
				return 47;
			case 7:
				return 55;
			case 8:
				return 67;
			case 9:
				return 71;
			case 10:
				return 84;
			}
			break;
		case 60:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 14;
			case 2:
				return 19;
			case 3:
				return 25;
			case 4:
				return 30;
			case 5:
				return 36;
			case 6:
				return 43;
			case 7:
				return 49;
			case 8:
				return 61;
			case 9:
				return 64;
			case 10:
				return 76;
			}
			break;
		case 120:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 20;
			case 2:
				return 27;
			case 3:
				return 35;
			case 4:
				return 43;
			case 5:
				return 52;
			case 6:
				return 61;
			case 7:
				return 70;
			case 8:
				return 87;
			case 9:
				return 91;
			case 10:
				return 108;
			}
			break;
		case 115:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 19;
			case 2:
				return 26;
			case 3:
				return 33;
			case 4:
				return 40;
			case 5:
				return 48;
			case 6:
				return 57;
			case 7:
				return 66;
			case 8:
				return 81;
			case 9:
				return 85;
			case 10:
				return 101;
			}
			break;
		case 140:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 29;
			case 2:
				return 39;
			case 3:
				return 50;
			case 4:
				return 61;
			case 5:
				return 73;
			case 6:
				return 86;
			case 7:
				return 99;
			case 8:
				return 122;
			case 9:
				return 128;
			case 10:
				return 152;
			}
			break;
		case 130:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 24;
			case 2:
				return 32;
			case 3:
				return 41;
			case 4:
				return 50;
			case 5:
				return 60;
			case 6:
				return 71;
			case 7:
				return 82;
			case 8:
				return 101;
			case 9:
				return 106;
			case 10:
				return 126;
			}
			break;
		case 150:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 29;
			case 2:
				return 39;
			case 3:
				return 50;
			case 4:
				return 61;
			case 5:
				return 73;
			case 6:
				return 86;
			case 7:
				return 99;
			case 8:
				return 122;
			case 9:
				return 128;
			case 10:
				return 152;
			}
			break;
		case 160:
			switch (CuongHoaGiaiDoan)
			{
			case 1:
				return 29;
			case 2:
				return 39;
			case 3:
				return 50;
			case 4:
				return 61;
			case 5:
				return 73;
			case 6:
				return 86;
			case 7:
				return 99;
			case 8:
				return 122;
			case 9:
				return 128;
			case 10:
				return 152;
			}
			break;
		}
		return 0;
	}

	private int DatDuocVatPham障力(ItmeClass Itme, int CuongHoaGiaiDoan)
	{
		switch (FLD_RESIDE2)
		{
		case 1:
		{
			var num4 = 0;
			var num5 = VatPham_ThuocTinh_Manh + (int)VatPham_ThuocTinh_ThemVao_CuongHoa;
			if (Itme.FLD_LEVEL <= 80)
			{
				num4 = 5;
			}
			else if (Itme.FLD_LEVEL == 90)
			{
				num4 = 10;
			}
			else if (Itme.FLD_LEVEL == 100)
			{
				num4 = 20;
			}
			else if (Itme.FLD_LEVEL == 110)
			{
				num4 = 30;
			}
			else if (Itme.FLD_LEVEL == 120)
			{
				num4 = 40;
			}
			else if (Itme.FLD_LEVEL == 130)
			{
				num4 = 50;
			}
			else if (Itme.FLD_LEVEL == 140)
			{
				num4 = 60;
			}
			VatPham_ThuocTinh_LaChan_GiaTang += num5 * num4;
			switch (num5)
			{
			case 6:
				VatPham_ThuocTinh_LaChan_GiaTang += 155;
				break;
			case 7:
				VatPham_ThuocTinh_LaChan_GiaTang += 195;
				break;
			case 8:
				VatPham_ThuocTinh_LaChan_GiaTang += 260;
				break;
			case 9:
				VatPham_ThuocTinh_LaChan_GiaTang += 375;
				break;
			case 10:
				VatPham_ThuocTinh_LaChan_GiaTang += 615;
				break;
			case 11:
				VatPham_ThuocTinh_LaChan_GiaTang += 905;
				break;
			case 12:
				VatPham_ThuocTinh_LaChan_GiaTang += 1295;
				break;
			case 13:
				VatPham_ThuocTinh_LaChan_GiaTang += 1685;
				break;
			case 14:
				VatPham_ThuocTinh_LaChan_GiaTang += 2125;
				break;
			case 15:
				VatPham_ThuocTinh_LaChan_GiaTang += 2565;
				break;
			case 16:
				VatPham_ThuocTinh_LaChan_GiaTang += 3115;
				break;
			}
			break;
		}
		case 2:
		case 5:
		{
			var num2 = 0;
			var num3 = VatPham_ThuocTinh_Manh + (int)VatPham_ThuocTinh_ThemVao_CuongHoa;
			if (Itme.FLD_LEVEL <= 80)
			{
				num2 = 5;
			}
			else if (Itme.FLD_LEVEL == 90)
			{
				num2 = 7;
			}
			else if (Itme.FLD_LEVEL == 100)
			{
				num2 = 9;
			}
			else if (Itme.FLD_LEVEL == 110)
			{
				num2 = 11;
			}
			else if (Itme.FLD_LEVEL == 120)
			{
				num2 = 13;
			}
			else if (Itme.FLD_LEVEL == 130)
			{
				num2 = 15;
			}
			else if (Itme.FLD_LEVEL == 140)
			{
				num2 = 17;
			}
			else if (Itme.FLD_LEVEL == 150)
			{
				num2 = 22;
			}
			VatPham_ThuocTinh_LaChan_GiaTang += num3 * num2;
			switch (num3)
			{
			case 6:
				VatPham_ThuocTinh_LaChan_GiaTang += 5;
				break;
			case 7:
				VatPham_ThuocTinh_LaChan_GiaTang += 15;
				break;
			case 8:
				VatPham_ThuocTinh_LaChan_GiaTang += 33;
				break;
			case 9:
				VatPham_ThuocTinh_LaChan_GiaTang += 55;
				break;
			case 10:
				VatPham_ThuocTinh_LaChan_GiaTang += 124;
				break;
			case 11:
				VatPham_ThuocTinh_LaChan_GiaTang += 207;
				break;
			case 12:
				VatPham_ThuocTinh_LaChan_GiaTang += 305;
				break;
			case 13:
				VatPham_ThuocTinh_LaChan_GiaTang += 443;
				break;
			case 14:
				VatPham_ThuocTinh_LaChan_GiaTang += 611;
				break;
			case 15:
				VatPham_ThuocTinh_LaChan_GiaTang += 814;
				break;
			case 16:
				VatPham_ThuocTinh_LaChan_GiaTang += 1057;
				break;
			}
			break;
		}
		case 6:
		{
			var num = 0;
			if (Itme.FLD_LEVEL <= 55)
			{
				num = 1;
			}
			else if (Itme.FLD_LEVEL <= 65)
			{
				num = 2;
			}
			else if (Itme.FLD_LEVEL <= 75)
			{
				num = 3;
			}
			else if (Itme.FLD_LEVEL <= 85)
			{
				num = 5;
			}
			else if (Itme.FLD_LEVEL == 95)
			{
				num = 7;
			}
			else if (Itme.FLD_LEVEL == 105)
			{
				num = 9;
			}
			else if (Itme.FLD_LEVEL == 115)
			{
				num = 11;
			}
			else if (Itme.FLD_LEVEL == 125)
			{
				num = 13;
			}
			else if (Itme.FLD_LEVEL == 135)
			{
				num = 15;
			}
			else if (Itme.FLD_LEVEL == 145)
			{
				num = 17;
			}
			else if (Itme.FLD_LEVEL == 155)
			{
				num = 22;
			}
			VatPham_ThuocTinh_LaChan_GiaTang += VatPham_ThuocTinh_Manh * num;
			if (VatPham_ThuocTinh_Manh > 5)
			{
				switch (VatPham_ThuocTinh_Manh)
				{
				case 6:
					VatPham_ThuocTinh_LaChan_GiaTang += 5;
					break;
				case 7:
					VatPham_ThuocTinh_LaChan_GiaTang += 15;
					break;
				case 8:
					VatPham_ThuocTinh_LaChan_GiaTang += 33;
					break;
				case 9:
					VatPham_ThuocTinh_LaChan_GiaTang += 55;
					break;
				case 10:
					VatPham_ThuocTinh_LaChan_GiaTang += 124;
					break;
				case 11:
					VatPham_ThuocTinh_LaChan_GiaTang += 207;
					break;
				case 12:
					VatPham_ThuocTinh_LaChan_GiaTang += 305;
					break;
				case 13:
					VatPham_ThuocTinh_LaChan_GiaTang += 443;
					break;
				case 14:
					VatPham_ThuocTinh_LaChan_GiaTang += 611;
					break;
				case 15:
					VatPham_ThuocTinh_LaChan_GiaTang += 814;
					break;
				}
			}
			break;
		}
		}
		return 0;
	}

	private void TinhToanVuKhiThuocTinh(ItmeClass Itme)
	{
		try
		{
			Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 6;
			Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 6;
			Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 6;
			Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 6;
			if (VatPham_ThuocTinh_Manh > 5)
			{
				if (Itme.FLD_JOB_LEVEL >= 3 && VatPham_ThuocTinh_Manh >= 7)
				{
					switch (VatPham_ThuocTinh_Manh)
					{
					case 7:
						Vat_Pham_Luc_Cong_Kich += 10;
						Vat_Pham_Luc_Cong_KichMAX += 10;
						Vat_Pham_Luc_Cong_KichNew += 10;
						Vat_Pham_Luc_Cong_KichMaxNew += 10;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 12;
							Vat_Pham_Luc_Cong_KichMAX += 12;
							Vat_Pham_Luc_Cong_KichNew += 12;
							Vat_Pham_Luc_Cong_KichMaxNew += 12;
						}
						break;
					case 8:
						Vat_Pham_Luc_Cong_Kich += 24;
						Vat_Pham_Luc_Cong_KichMAX += 24;
						Vat_Pham_Luc_Cong_KichNew += 24;
						Vat_Pham_Luc_Cong_KichMaxNew += 24;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 17;
							Vat_Pham_Luc_Cong_KichMAX += 17;
							Vat_Pham_Luc_Cong_KichNew += 17;
							Vat_Pham_Luc_Cong_KichMaxNew += 17;
						}
						break;
					case 9:
						Vat_Pham_Luc_Cong_Kich += 48;
						Vat_Pham_Luc_Cong_KichMAX += 48;
						Vat_Pham_Luc_Cong_KichNew += 48;
						Vat_Pham_Luc_Cong_KichMaxNew += 48;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 22;
							Vat_Pham_Luc_Cong_KichMAX += 22;
							Vat_Pham_Luc_Cong_KichNew += 22;
							Vat_Pham_Luc_Cong_KichMaxNew += 22;
						}
						break;
					case 10:
						Vat_Pham_Luc_Cong_Kich += 102;
						Vat_Pham_Luc_Cong_KichMAX += 102;
						Vat_Pham_Luc_Cong_KichNew += 102;
						Vat_Pham_Luc_Cong_KichMaxNew += 102;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 32;
							Vat_Pham_Luc_Cong_KichMAX += 32;
							Vat_Pham_Luc_Cong_KichNew += 32;
							Vat_Pham_Luc_Cong_KichMaxNew += 32;
						}
						break;
					case 11:
						Vat_Pham_Luc_Cong_Kich += 111;
						Vat_Pham_Luc_Cong_KichMAX += 111;
						Vat_Pham_Luc_Cong_KichNew += 111;
						Vat_Pham_Luc_Cong_KichMaxNew += 111;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 35;
							Vat_Pham_Luc_Cong_KichMAX += 35;
							Vat_Pham_Luc_Cong_KichNew += 35;
							Vat_Pham_Luc_Cong_KichMaxNew += 35;
						}
						break;
					case 12:
						Vat_Pham_Luc_Cong_Kich += 125;
						Vat_Pham_Luc_Cong_KichMAX += 125;
						Vat_Pham_Luc_Cong_KichNew += 125;
						Vat_Pham_Luc_Cong_KichMaxNew += 125;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 45;
							Vat_Pham_Luc_Cong_KichMAX += 45;
							Vat_Pham_Luc_Cong_KichNew += 45;
							Vat_Pham_Luc_Cong_KichMaxNew += 45;
						}
						break;
					case 13:
						Vat_Pham_Luc_Cong_Kich += 144;
						Vat_Pham_Luc_Cong_KichMAX += 144;
						Vat_Pham_Luc_Cong_KichNew += 144;
						Vat_Pham_Luc_Cong_KichMaxNew += 144;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 55;
							Vat_Pham_Luc_Cong_KichMAX += 55;
							Vat_Pham_Luc_Cong_KichNew += 55;
							Vat_Pham_Luc_Cong_KichMaxNew += 55;
						}
						break;
					case 14:
						Vat_Pham_Luc_Cong_Kich += 168;
						Vat_Pham_Luc_Cong_KichMAX += 168;
						Vat_Pham_Luc_Cong_KichNew += 168;
						Vat_Pham_Luc_Cong_KichMaxNew += 168;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 60;
							Vat_Pham_Luc_Cong_KichMAX += 60;
							Vat_Pham_Luc_Cong_KichNew += 60;
							Vat_Pham_Luc_Cong_KichMaxNew += 60;
						}
						break;
					case 15:
						Vat_Pham_Luc_Cong_Kich += 197;
						Vat_Pham_Luc_Cong_KichMAX += 197;
						Vat_Pham_Luc_Cong_KichNew += 197;
						Vat_Pham_Luc_Cong_KichMaxNew += 197;
						if (Itme.FLD_LEVEL >= 130)
						{
							Vat_Pham_Luc_Cong_Kich += 60;
							Vat_Pham_Luc_Cong_KichMAX += 60;
							Vat_Pham_Luc_Cong_KichNew += 60;
							Vat_Pham_Luc_Cong_KichMaxNew += 60;
						}
						break;
					}
				}
				else
				{
					Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 5) * 2;
					Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 5) * 2;
					Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 5) * 2;
					Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 5) * 2;
					if (Itme.FLD_LEVEL >= 130)
					{
						Vat_Pham_Luc_Cong_Kich += 8;
						Vat_Pham_Luc_Cong_KichMAX += 8;
						Vat_Pham_Luc_Cong_KichNew += 8;
						Vat_Pham_Luc_Cong_KichMaxNew += 8;
					}
				}
				if (VatPham_ThuocTinh_Manh >= 10 && FLD_RESIDE2 == 4)
				{
					switch (VatPham_ThuocTinh_Manh)
					{
					case 10:
						VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang++;
						break;
					case 11:
						if (Itme.ItmeNAME.Contains("真-") || Itme.ItmeNAME.Contains("chan"))
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 2;
						}
						break;
					case 12:
						if (Itme.ItmeNAME.Contains("真-") || Itme.ItmeNAME.Contains("chan"))
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						break;
					case 13:
						if (Itme.FLD_LEVEL >= 130)
						{
							if (Itme.ItmeNAME.Contains("真-") || Itme.ItmeNAME.Contains("chan"))
							{
								VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
							}
							else
							{
								VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
							}
						}
						else if (Itme.ItmeNAME.Contains("真-") || Itme.ItmeNAME.Contains("chan") || Itme.FLD_RESIDE1 == 8)
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 2;
						}
						break;
					case 14:
						if (Itme.FLD_LEVEL >= 130)
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						break;
					case 15:
						if (Itme.FLD_LEVEL >= 130)
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						break;
					case 16:
						if (Itme.FLD_LEVEL >= 130)
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						break;
					case 17:
						if (Itme.FLD_LEVEL >= 130)
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						break;
					case 18:
						if (Itme.FLD_LEVEL >= 130)
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						break;
					case 19:
						if (Itme.FLD_LEVEL >= 130)
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
						}
						else
						{
							VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
						}
						break;
					}
				}
				if (VatPham_ThuocTinh_Manh >= 7)
				{
					Dictionary<int, Itimesx> dictionary = new();
					dictionary.Add(0, ThuocTinh1);
					dictionary.Add(1, ThuocTinh2);
					dictionary.Add(2, ThuocTinh3);
					dictionary.Add(3, ThuocTinh4);
					for (var i = 0; i < 4; i++)
					{
						if (dictionary[i].ThuocTinhLoaiHinh == 0)
						{
							continue;
						}
						var thuocTinhSoLuong = dictionary[i].ThuocTinhSoLuong;
						switch (dictionary[i].ThuocTinhLoaiHinh)
						{
						case 7:
							if (VatPham_ThuocTinh_Manh == 7)
							{
								if (i < 2)
								{
									VatPham_ThuocTinh_VoCong_LucCongKich++;
									VatPham_ThuocTinh_VoCong_LucCongKichNew++;
								}
							}
							else if (VatPham_ThuocTinh_Manh <= 12)
							{
								VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 7;
								VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 7;
							}
							else if (VatPham_ThuocTinh_Manh <= 14)
							{
								var num3 = Math.Round(thuocTinhSoLuong / 4.0);
								VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 7 + (int)num3;
								VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 7 + (int)num3;
							}
							else
							{
								var num4 = Math.Round(thuocTinhSoLuong / 4.0);
								VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 8 + (int)num4;
								VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 8 + (int)num4;
							}
							break;
						case 8:
							if (VatPham_ThuocTinh_Manh >= 13 && thuocTinhSoLuong > 0)
							{
								VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang++;
							}
							break;
						case 10:
							if (VatPham_ThuocTinh_Manh == 7)
							{
								if (i < 2)
								{
									VatPham_ThuocTinh_ThemVao_MucThuongTon++;
								}
							}
							else if (VatPham_ThuocTinh_Manh <= 12)
							{
								VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_Manh - 7;
							}
							else if (VatPham_ThuocTinh_Manh <= 14)
							{
								VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_Manh - 7 + (int)Math.Round(thuocTinhSoLuong / 4.0);
							}
							else
							{
								VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_Manh - 8 + (int)Math.Round(thuocTinhSoLuong / 4.0);
							}
							break;
						case 3:
							if (VatPham_ThuocTinh_Manh == 7)
							{
								if (i < 2)
								{
									VatPham_ThuocTinh_SinhMenhLuc_GiaTang++;
								}
							}
							else if (VatPham_ThuocTinh_Manh <= 12)
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 7;
							}
							else if (VatPham_ThuocTinh_Manh <= 14)
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 7 + (int)Math.Round(thuocTinhSoLuong / 4.0);
							}
							else
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 8 + (int)Math.Round(thuocTinhSoLuong / 4.0);
							}
							break;
						case 1:
							if (VatPham_ThuocTinh_Manh == 7)
							{
								if (i < 2)
								{
									Vat_Pham_Luc_Cong_Kich++;
									Vat_Pham_Luc_Cong_KichMAX++;
									Vat_Pham_Luc_Cong_KichNew++;
									Vat_Pham_Luc_Cong_KichMaxNew++;
								}
							}
							else if (VatPham_ThuocTinh_Manh <= 12)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 7;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 7;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 7;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 7;
							}
							else if (VatPham_ThuocTinh_Manh <= 14)
							{
								var num = Math.Round(thuocTinhSoLuong / 4.0);
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 7 + (int)num;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 7 + (int)num;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 7 + (int)num;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 7 + (int)num;
							}
							else
							{
								var num2 = Math.Round(thuocTinhSoLuong / 4.0);
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 8 + (int)num2;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 8 + (int)num2;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 8 + (int)num2;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 8 + (int)num2;
							}
							break;
						}
					}
					dictionary.Clear();
				}
			}
			if (Itme.FLD_LEVEL >= 130 && VatPham_ThuocTinh_Manh == 5)
			{
				Vat_Pham_Luc_Cong_Kich += 2;
				Vat_Pham_Luc_Cong_KichMAX += 2;
				Vat_Pham_Luc_Cong_KichNew += 2;
				Vat_Pham_Luc_Cong_KichMaxNew += 2;
			}
			var num5 = 8;
			if (FLD_FJ_LowSoul > 0)
			{
				var num6 = FLD_FJ_LowSoul * num5;
				Vat_Pham_Luc_Cong_Kich += num6;
				Vat_Pham_Luc_Cong_KichMAX += num6;
				Vat_Pham_Luc_Cong_KichNew += num6;
				Vat_Pham_Luc_Cong_KichMaxNew += num6;
			}
		}
		catch
		{
		}
	}

	private void 计算YPhucThuocTinh(ItmeClass Itme)
	{
		try
		{
			switch (VatPham_ThuocTinh_Manh)
			{
			case 1:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 3;
					Vat_Pham_Luc_Phong_NguNew += 3;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 4;
				Vat_Pham_Luc_Phong_NguNew += 4;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 2;
					Vat_Pham_Luc_Phong_NguNew += 2;
				}
				break;
			case 2:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 9;
					Vat_Pham_Luc_Phong_NguNew += 9;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 12;
				Vat_Pham_Luc_Phong_NguNew += 12;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 4;
					Vat_Pham_Luc_Phong_NguNew += 4;
				}
				break;
			case 3:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 18;
					Vat_Pham_Luc_Phong_NguNew += 18;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 24;
				Vat_Pham_Luc_Phong_NguNew += 24;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 6;
					Vat_Pham_Luc_Phong_NguNew += 6;
				}
				break;
			case 4:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 30;
					Vat_Pham_Luc_Phong_NguNew += 30;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 40;
				Vat_Pham_Luc_Phong_NguNew += 40;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 8;
					Vat_Pham_Luc_Phong_NguNew += 8;
				}
				break;
			case 5:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 45;
					Vat_Pham_Luc_Phong_NguNew += 45;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 60;
				Vat_Pham_Luc_Phong_NguNew += 60;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 10;
					Vat_Pham_Luc_Phong_NguNew += 10;
				}
				break;
			case 6:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 63;
					Vat_Pham_Luc_Phong_NguNew += 63;
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 84;
				Vat_Pham_Luc_Phong_NguNew += 84;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 14;
					Vat_Pham_Luc_Phong_NguNew += 14;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
				break;
			case 7:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 84;
					Vat_Pham_Luc_Phong_NguNew += 84;
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 112;
				Vat_Pham_Luc_Phong_NguNew += 112;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 18;
					Vat_Pham_Luc_Phong_NguNew += 18;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
				break;
			case 8:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 108;
					Vat_Pham_Luc_Phong_NguNew += 108;
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 144;
				Vat_Pham_Luc_Phong_NguNew += 144;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 22;
					Vat_Pham_Luc_Phong_NguNew += 22;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
				break;
			case 9:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 135;
					Vat_Pham_Luc_Phong_NguNew += 135;
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 180;
				Vat_Pham_Luc_Phong_NguNew += 180;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 26;
					Vat_Pham_Luc_Phong_NguNew += 26;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += 5 + (VatPham_ThuocTinh_Manh - 5) * 5;
				break;
			case 10:
				if (Itme.FLD_LEVEL < 60)
				{
					Vat_Pham_Luc_Phong_Ngu += 165;
					Vat_Pham_Luc_Phong_NguNew += 165;
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 5 + (VatPham_ThuocTinh_Manh - 5) * 5;
					break;
				}
				Vat_Pham_Luc_Phong_Ngu += 230;
				Vat_Pham_Luc_Phong_NguNew += 230;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 31;
					Vat_Pham_Luc_Phong_NguNew += 31;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += 5 + (VatPham_ThuocTinh_Manh - 5) * 5;
				break;
			case 11:
				Vat_Pham_Luc_Phong_Ngu += 265;
				Vat_Pham_Luc_Phong_NguNew += 265;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 34;
					Vat_Pham_Luc_Phong_NguNew += 34;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += 10 + (VatPham_ThuocTinh_Manh - 5) * 5;
				break;
			case 12:
				Vat_Pham_Luc_Phong_Ngu += 315;
				Vat_Pham_Luc_Phong_NguNew += 315;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 36;
					Vat_Pham_Luc_Phong_NguNew += 36;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += 15 + (VatPham_ThuocTinh_Manh - 5) * 5;
				break;
			case 13:
				Vat_Pham_Luc_Phong_Ngu += 365;
				Vat_Pham_Luc_Phong_NguNew += 365;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 44;
					Vat_Pham_Luc_Phong_NguNew += 44;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += 20 + (VatPham_ThuocTinh_Manh - 5) * 5;
				if (Itme.ItmeNAME.Contains("chan") && Itme.FLD_LEVEL >= 150)
				{
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 460;
				}
				else
				{
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 260;
				}
				break;
			case 14:
				Vat_Pham_Luc_Phong_Ngu += 415;
				Vat_Pham_Luc_Phong_NguNew += 415;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 52;
					Vat_Pham_Luc_Phong_NguNew += 52;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += 25 + (VatPham_ThuocTinh_Manh - 5) * 5;
				if (Itme.ItmeNAME.Contains("chan") && Itme.FLD_LEVEL >= 150)
				{
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 460;
				}
				else
				{
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 260;
				}
				break;
			case 15:
				Vat_Pham_Luc_Phong_Ngu += 465;
				Vat_Pham_Luc_Phong_NguNew += 465;
				if (Itme.FLD_LEVEL >= 130)
				{
					Vat_Pham_Luc_Phong_Ngu += 60;
					Vat_Pham_Luc_Phong_NguNew += 60;
				}
				VatPham_ThuocTinh_GiamXuong_MucThuongTon += 30 + (VatPham_ThuocTinh_Manh - 5) * 5;
				if (Itme.ItmeNAME.Contains("chan") && Itme.FLD_LEVEL >= 150)
				{
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 460;
				}
				else
				{
					VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 260;
				}
				break;
			}
			var num = 5;
			if (FLD_FJ_LowSoul > 0)
			{
				var num2 = FLD_FJ_LowSoul * num;
				Vat_Pham_Luc_Phong_Ngu += num2;
				Vat_Pham_Luc_Phong_NguNew += num2;
			}
		}
		catch
		{
		}
	}

	private void DatDuocCuongHoa(string ysqh, int TriggerAttributePromotion)
	{
		try
		{
			if (!World.ItemList.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out var value) || value.FLD_PID == 1000001011 || value.FLD_PID == 1000002006 || (value.FLD_PID >= 1000001382 && value.FLD_PID <= 1000001385))
			{
				return;
			}
			switch (ysqh.Length)
			{
			case 8:
				VatPham_ThuocTinh_Giai_Doan_Loai_Hinh = 0;
				VatPham_ThuocTinh_So_Giai_Doan = 0;
				VatPham_ThuocTinh_Manh_Loai_Hinh = int.Parse(ysqh.Substring(ysqh.Length - 8, 1));
				VatPham_ThuocTinh_Manh = int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
				switch (VatPham_ThuocTinh_Manh_Loai_Hinh)
				{
				case 1:
					if (FLD_RESIDE2 == 10)
					{
						if (value.FLD_LEVEL >= 160)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 13;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 13;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 13;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 13;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 38 + (VatPham_ThuocTinh_Manh - 3) * 22;
								Vat_Pham_Luc_Cong_KichMAX += 38 + (VatPham_ThuocTinh_Manh - 3) * 22;
								Vat_Pham_Luc_Cong_KichNew += 38 + (VatPham_ThuocTinh_Manh - 3) * 22;
								Vat_Pham_Luc_Cong_KichMaxNew += 38 + (VatPham_ThuocTinh_Manh - 3) * 22;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(160, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 150)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 11;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 11;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 11;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 11;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 32 + (VatPham_ThuocTinh_Manh - 3) * 19;
								Vat_Pham_Luc_Cong_KichMAX += 32 + (VatPham_ThuocTinh_Manh - 3) * 19;
								Vat_Pham_Luc_Cong_KichNew += 32 + (VatPham_ThuocTinh_Manh - 3) * 19;
								Vat_Pham_Luc_Cong_KichMaxNew += 32 + (VatPham_ThuocTinh_Manh - 3) * 19;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(150, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 140)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 9;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 9;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 9;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 9;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 27 + (VatPham_ThuocTinh_Manh - 3) * 16;
								Vat_Pham_Luc_Cong_KichMAX += 27 + (VatPham_ThuocTinh_Manh - 3) * 16;
								Vat_Pham_Luc_Cong_KichNew += 27 + (VatPham_ThuocTinh_Manh - 3) * 16;
								Vat_Pham_Luc_Cong_KichMaxNew += 27 + (VatPham_ThuocTinh_Manh - 3) * 16;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(140, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 130)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 7;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 7;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 7;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 7;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 21 + (VatPham_ThuocTinh_Manh - 3) * 13;
								Vat_Pham_Luc_Cong_KichMAX += 21 + (VatPham_ThuocTinh_Manh - 3) * 13;
								Vat_Pham_Luc_Cong_KichNew += 21 + (VatPham_ThuocTinh_Manh - 3) * 13;
								Vat_Pham_Luc_Cong_KichMaxNew += 21 + (VatPham_ThuocTinh_Manh - 3) * 13;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(130, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 120)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 6;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 6;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 6;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 6;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 18 + (VatPham_ThuocTinh_Manh - 3) * 11;
								Vat_Pham_Luc_Cong_KichMAX += 18 + (VatPham_ThuocTinh_Manh - 3) * 11;
								Vat_Pham_Luc_Cong_KichNew += 18 + (VatPham_ThuocTinh_Manh - 3) * 11;
								Vat_Pham_Luc_Cong_KichMaxNew += 18 + (VatPham_ThuocTinh_Manh - 3) * 11;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(120, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 115)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 5;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 5;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 5;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 5;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 15 + (VatPham_ThuocTinh_Manh - 3) * 9;
								Vat_Pham_Luc_Cong_KichMAX += 15 + (VatPham_ThuocTinh_Manh - 3) * 9;
								Vat_Pham_Luc_Cong_KichNew += 15 + (VatPham_ThuocTinh_Manh - 3) * 9;
								Vat_Pham_Luc_Cong_KichMaxNew += 15 + (VatPham_ThuocTinh_Manh - 3) * 9;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(115, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 100)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 4;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 4;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 4;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 4;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 12 + (VatPham_ThuocTinh_Manh - 3) * 7;
								Vat_Pham_Luc_Cong_KichMAX += 12 + (VatPham_ThuocTinh_Manh - 3) * 7;
								Vat_Pham_Luc_Cong_KichNew += 12 + (VatPham_ThuocTinh_Manh - 3) * 7;
								Vat_Pham_Luc_Cong_KichMaxNew += 12 + (VatPham_ThuocTinh_Manh - 3) * 7;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(100, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 80)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 3;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 3;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 3;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 3;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
								Vat_Pham_Luc_Cong_KichMAX += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
								Vat_Pham_Luc_Cong_KichNew += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
								Vat_Pham_Luc_Cong_KichMaxNew += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(80, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 60)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 2;
								Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 2;
								Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 2;
								Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 2;
							}
							else
							{
								Vat_Pham_Luc_Cong_Kich += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
								Vat_Pham_Luc_Cong_KichMAX += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
								Vat_Pham_Luc_Cong_KichNew += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
								Vat_Pham_Luc_Cong_KichMaxNew += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(60, VatPham_ThuocTinh_Manh);
						}
					}
					else
					{
						TinhToanVuKhiThuocTinh(value);
					}
					break;
				case 2:
					switch (FLD_RESIDE2)
					{
					case 3:
					case 4:
						break;
					case 8:
					case 9:
					case 10:
					case 11:
					case 12:
					case 13:
						break;
					case 1:
						计算YPhucThuocTinh(value);
						DatDuocVatPham障力(value, VatPham_ThuocTinh_Manh);
						break;
					case 2:
					case 5:
						switch (VatPham_ThuocTinh_Manh)
						{
						case 1:
							Vat_Pham_Luc_Phong_Ngu += 3;
							Vat_Pham_Luc_Phong_NguNew += 3;
							break;
						case 2:
							Vat_Pham_Luc_Phong_Ngu += 6;
							Vat_Pham_Luc_Phong_NguNew += 6;
							break;
						case 3:
							Vat_Pham_Luc_Phong_Ngu += 9;
							Vat_Pham_Luc_Phong_NguNew += 9;
							break;
						case 4:
							Vat_Pham_Luc_Phong_Ngu += 12;
							Vat_Pham_Luc_Phong_NguNew += 12;
							break;
						case 5:
							Vat_Pham_Luc_Phong_Ngu += 15;
							Vat_Pham_Luc_Phong_NguNew += 15;
							break;
						case 6:
							if (value.FLD_LEVEL < 60)
							{
								Vat_Pham_Luc_Phong_Ngu += 18;
								Vat_Pham_Luc_Phong_NguNew += 18;
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
							}
							else
							{
								Vat_Pham_Luc_Phong_Ngu += 19;
								Vat_Pham_Luc_Phong_NguNew += 19;
								VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
							}
							break;
						case 7:
							if (value.FLD_LEVEL < 60)
							{
								Vat_Pham_Luc_Phong_Ngu += 21;
								Vat_Pham_Luc_Phong_NguNew += 21;
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
								break;
							}
							Vat_Pham_Luc_Phong_Ngu += 23;
							Vat_Pham_Luc_Phong_NguNew += 23;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 2;
								Vat_Pham_Luc_Phong_NguNew += 2;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						case 8:
							if (value.FLD_LEVEL < 60)
							{
								Vat_Pham_Luc_Phong_Ngu += 27;
								Vat_Pham_Luc_Phong_NguNew += 27;
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
								break;
							}
							Vat_Pham_Luc_Phong_Ngu += 29;
							Vat_Pham_Luc_Phong_NguNew += 29;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 5;
								Vat_Pham_Luc_Phong_NguNew += 5;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						case 9:
							if (value.FLD_LEVEL < 60)
							{
								Vat_Pham_Luc_Phong_Ngu += 31;
								Vat_Pham_Luc_Phong_NguNew += 31;
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
								break;
							}
							Vat_Pham_Luc_Phong_Ngu += 38;
							Vat_Pham_Luc_Phong_NguNew += 38;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 8;
								Vat_Pham_Luc_Phong_NguNew += 8;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						case 10:
							if (value.FLD_LEVEL < 60)
							{
								Vat_Pham_Luc_Phong_Ngu += 37;
								Vat_Pham_Luc_Phong_NguNew += 37;
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 5 + (VatPham_ThuocTinh_Manh - 5) * 5;
								break;
							}
							Vat_Pham_Luc_Phong_Ngu += 53;
							Vat_Pham_Luc_Phong_NguNew += 53;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 11;
								Vat_Pham_Luc_Phong_NguNew += 11;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += 5 + (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						case 11:
							Vat_Pham_Luc_Phong_Ngu += 59;
							Vat_Pham_Luc_Phong_NguNew += 59;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 20;
								Vat_Pham_Luc_Phong_NguNew += 20;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += 10 + (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						case 12:
							Vat_Pham_Luc_Phong_Ngu += 65;
							Vat_Pham_Luc_Phong_NguNew += 65;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 29;
								Vat_Pham_Luc_Phong_NguNew += 29;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += 15 + (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						case 13:
							Vat_Pham_Luc_Phong_Ngu += 51;
							Vat_Pham_Luc_Phong_NguNew += 51;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 58;
								Vat_Pham_Luc_Phong_NguNew += 58;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += 20 + (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						case 14:
							Vat_Pham_Luc_Phong_Ngu += 57;
							Vat_Pham_Luc_Phong_NguNew += 57;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 72;
								Vat_Pham_Luc_Phong_NguNew += 72;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += 25 + (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						case 15:
							Vat_Pham_Luc_Phong_Ngu += 63;
							Vat_Pham_Luc_Phong_NguNew += 63;
							if (value.FLD_LEVEL >= 130)
							{
								Vat_Pham_Luc_Phong_Ngu += 86;
								Vat_Pham_Luc_Phong_NguNew += 86;
							}
							VatPham_ThuocTinh_GiamXuong_MucThuongTon += 30 + (VatPham_ThuocTinh_Manh - 5) * 5;
							break;
						}
						DatDuocVatPham障力(value, VatPham_ThuocTinh_Manh);
						break;
					case 6:
						Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 3;
						Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 3;
						switch (VatPham_ThuocTinh_Manh)
						{
						case 6:
							Vat_Pham_Luc_Phong_Ngu += 2;
							Vat_Pham_Luc_Phong_NguNew += 2;
							if (value.FLD_LEVEL >= 65)
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 40;
							}
							else
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 45;
							}
							break;
						case 7:
							Vat_Pham_Luc_Phong_Ngu += 4;
							Vat_Pham_Luc_Phong_NguNew += 4;
							if (value.FLD_LEVEL >= 65)
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 80;
							}
							else
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 90;
							}
							break;
						case 8:
							Vat_Pham_Luc_Phong_Ngu += 10;
							Vat_Pham_Luc_Phong_NguNew += 10;
							if (value.FLD_LEVEL >= 65)
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 140;
							}
							else
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 155;
							}
							break;
						case 9:
							Vat_Pham_Luc_Phong_Ngu += 22;
							Vat_Pham_Luc_Phong_NguNew += 22;
							if (value.FLD_LEVEL >= 65)
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 200;
							}
							else
							{
								VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 220;
							}
							break;
						case 10:
						{
							Vat_Pham_Luc_Phong_Ngu += 46;
							Vat_Pham_Luc_Phong_NguNew += 46;
							var fLD_LEVEL = value.FLD_LEVEL;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 300;
							break;
						}
						case 11:
							Vat_Pham_Luc_Phong_Ngu += 61;
							Vat_Pham_Luc_Phong_NguNew += 61;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 300;
							break;
						case 12:
							Vat_Pham_Luc_Phong_Ngu += 76;
							Vat_Pham_Luc_Phong_NguNew += 76;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 300;
							break;
						case 13:
							Vat_Pham_Luc_Phong_Ngu += 71;
							Vat_Pham_Luc_Phong_NguNew += 71;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 610;
							break;
						case 14:
							Vat_Pham_Luc_Phong_Ngu += 91;
							Vat_Pham_Luc_Phong_NguNew += 91;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 610;
							break;
						case 15:
							Vat_Pham_Luc_Phong_Ngu += 111;
							Vat_Pham_Luc_Phong_NguNew += 111;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 610;
							break;
						}
						DatDuocVatPham障力(value, VatPham_ThuocTinh_Manh);
						break;
					case 7:
						if (value.FLD_LEVEL >= 140)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 9;
								Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 9;
							}
							else
							{
								Vat_Pham_Luc_Phong_Ngu += 27 + (VatPham_ThuocTinh_Manh - 3) * 16;
								Vat_Pham_Luc_Phong_NguNew += 27 + (VatPham_ThuocTinh_Manh - 3) * 16;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(140, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 130)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 7;
								Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 7;
							}
							else
							{
								Vat_Pham_Luc_Phong_Ngu += 21 + (VatPham_ThuocTinh_Manh - 3) * 13;
								Vat_Pham_Luc_Phong_NguNew += 21 + (VatPham_ThuocTinh_Manh - 3) * 13;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(130, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 120)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 6;
								Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 6;
							}
							else
							{
								Vat_Pham_Luc_Phong_Ngu += 19 + (VatPham_ThuocTinh_Manh - 3) * 11;
								Vat_Pham_Luc_Phong_NguNew += 19 + (VatPham_ThuocTinh_Manh - 3) * 11;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(120, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 115)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 5;
								Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 6;
							}
							else
							{
								Vat_Pham_Luc_Phong_Ngu += 15 + (VatPham_ThuocTinh_Manh - 3) * 9;
								Vat_Pham_Luc_Phong_NguNew += 15 + (VatPham_ThuocTinh_Manh - 3) * 9;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(115, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 100)
						{
							if (VatPham_ThuocTinh_Manh < 5)
							{
								Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 4;
								Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 4;
							}
							else
							{
								Vat_Pham_Luc_Phong_Ngu += 11 + (VatPham_ThuocTinh_Manh - 4) * 7;
								Vat_Pham_Luc_Phong_NguNew += 11 + (VatPham_ThuocTinh_Manh - 4) * 7;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(100, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 80)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 3;
								Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 3;
							}
							else
							{
								Vat_Pham_Luc_Phong_Ngu += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
								Vat_Pham_Luc_Phong_NguNew += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(80, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 60)
						{
							if (VatPham_ThuocTinh_Manh < 4)
							{
								Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 2;
								Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 2;
							}
							else
							{
								Vat_Pham_Luc_Phong_Ngu += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
								Vat_Pham_Luc_Phong_NguNew += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
							}
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(60, VatPham_ThuocTinh_Manh);
						}
						break;
					case 14:
						switch (VatPham_ThuocTinh_Manh)
						{
						case 1:
						case 2:
						case 3:
						case 4:
						case 5:
							Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 3;
							Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 3;
							break;
						case 6:
						case 7:
						case 8:
						case 9:
							Vat_Pham_Luc_Phong_Ngu += 15 + (VatPham_ThuocTinh_Manh - 5) * 4;
							Vat_Pham_Luc_Phong_NguNew += 15 + (VatPham_ThuocTinh_Manh - 5) * 4;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 25;
							break;
						case 10:
							Vat_Pham_Luc_Phong_Ngu += 37;
							Vat_Pham_Luc_Phong_NguNew += 37;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 30;
							break;
						}
						break;
					}
					break;
				case 3:
					if (FLD_RESIDE2 == 8)
					{
						if (value.FLD_LEVEL >= 140)
						{
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 80;
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(140, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 130)
						{
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 70;
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(130, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 120)
						{
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 60;
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(120, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 115)
						{
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 50;
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(115, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 100)
						{
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 40;
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(100, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 80)
						{
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 15;
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(80, VatPham_ThuocTinh_Manh);
						}
						else if (value.FLD_LEVEL >= 60)
						{
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 5;
							VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan += DatDuocTrangSucCuongHoaGiaiDoanGiaTang(60, VatPham_ThuocTinh_Manh);
						}
					}
					break;
				case 4:
					if (VatPham_ThuocTinh_Manh >= World.GioiHan_CuongHoa_AoChoang)
					{
						VatPham_ThuocTinh_Manh = World.GioiHan_CuongHoa_AoChoang;
					}
					if (VatPham_ThuocTinh_Manh > 1)
					{
						if (VatPham_ThuocTinh_Manh < 10)
						{
							Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh;
						}
						else if (VatPham_ThuocTinh_Manh >= 10 && VatPham_ThuocTinh_Manh < 20)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 10;
						}
						else if (VatPham_ThuocTinh_Manh >= 20 && VatPham_ThuocTinh_Manh < 30)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 20;
						}
						else if (VatPham_ThuocTinh_Manh >= 30 && VatPham_ThuocTinh_Manh < 40)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 30;
						}
						else if (VatPham_ThuocTinh_Manh >= 40 && VatPham_ThuocTinh_Manh < 50)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 40;
						}
						else if (VatPham_ThuocTinh_Manh >= 50 && VatPham_ThuocTinh_Manh < 60)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 50;
						}
						else if (VatPham_ThuocTinh_Manh >= 60 && VatPham_ThuocTinh_Manh < 70)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 60;
						}
						else if (VatPham_ThuocTinh_Manh >= 70 && VatPham_ThuocTinh_Manh < 80)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 70;
						}
						else if (VatPham_ThuocTinh_Manh >= 80 && VatPham_ThuocTinh_Manh < 90)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 80;
						}
						else if (VatPham_ThuocTinh_Manh >= 90 && VatPham_ThuocTinh_Manh < 100)
						{
							Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh + 90;
						}
						else
						{
							Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh / 2 + 5;
							Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh / 2 + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh / 2 * 10;
							VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh;
						}
					}
					else
					{
						Vat_Pham_Luc_Phong_Ngu += 5;
						Vat_Pham_Luc_Phong_NguNew += 5;
					}
					break;
				case 5:
				{
					var fLD_MAGIC = FLD_MAGIC2;
					if (VatPham_ThuocTinh_Manh_Loai_Hinh != 5)
					{
						break;
					}
					if (VatPham_ThuocTinh_Manh >= 100)
					{
						VatPham_ThuocTinh_Manh = 100;
					}
					if (VatPham_ThuocTinh_Manh > 1)
					{
						if (value.FLD_PID == 1000001170)
						{
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 2 + 10;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 2 + 10;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang = (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 10;
						}
						else if (value.FLD_PID == 1000001171)
						{
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.785) + 9;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.785) + 9;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang = (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 8;
						}
						else if (value.FLD_PID == 1000001172)
						{
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.089 + 5.5);
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.089 + 5.5);
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang = (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 1;
						}
						else if (value.FLD_PID == 1000001173)
						{
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.196) + 6;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.196) + 6;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang = (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 2;
						}
						else if (value.FLD_PID == 1000001174)
						{
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.785) + 9;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.785) + 9;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang = (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10;
						}
						else if (value.FLD_PID == 1000001175)
						{
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.0) + 5;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.0) + 5;
							VatPham_ThuocTinh_SinhMenhLuc_GiaTang = (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 6;
						}
						switch (fLD_MAGIC)
						{
						case 0:
							Vat_Pham_Luc_Phong_Ngu += (int)Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) + 4;
							Vat_Pham_Luc_Phong_NguNew += (int)Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) + 4;
							Vat_Pham_Luc_Cong_Kich += (int)Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) + 4;
							Vat_Pham_Luc_Cong_KichNew += (int)Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) + 4;
							break;
						case 1:
							Vat_Pham_Luc_Phong_Ngu += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.1875) + 5;
							Vat_Pham_Luc_Phong_NguNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.1875) + 5;
							Vat_Pham_Luc_Cong_Kich += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.1875) + 5;
							Vat_Pham_Luc_Cong_KichNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.1875) + 5;
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 2;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 2;
							break;
						case 2:
							Vat_Pham_Luc_Phong_Ngu += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.5 + 5.5);
							Vat_Pham_Luc_Phong_NguNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.5 + 5.5);
							Vat_Pham_Luc_Cong_Kich += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.5 + 5.5);
							Vat_Pham_Luc_Cong_KichNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.5 + 5.5);
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 4;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 4;
							break;
						case 3:
							Vat_Pham_Luc_Phong_Ngu += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Phong_NguNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_Kich += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_KichNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 8;
							break;
						case 4:
							Vat_Pham_Luc_Phong_Ngu += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Phong_NguNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_Kich += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_KichNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 8;
							break;
						case 5:
							Vat_Pham_Luc_Phong_Ngu += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Phong_NguNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_Kich += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_KichNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 8;
							break;
						case 6:
							Vat_Pham_Luc_Phong_Ngu += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Phong_NguNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_Kich += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_KichNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 8;
							break;
						case 7:
							Vat_Pham_Luc_Phong_Ngu += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Phong_NguNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_Kich += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Luc_Cong_KichNew += (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 8;
							Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 8;
							break;
						}
					}
					else if (VatPham_ThuocTinh_Manh == 1)
					{
						var num3 = 5;
						var num4 = 10;
						switch (fLD_MAGIC)
						{
						case 1:
							num3 += 6;
							num4 += 20;
							break;
						case 2:
							num3 += 7;
							num4 += 40;
							break;
						case 3:
							num3 += 10;
							num4 += 80;
							break;
						}
						Vat_Pham_Luc_Phong_Ngu += num3;
						Vat_Pham_Luc_Phong_NguNew += num3;
						Vat_Pham_Luc_Cong_Kich += num3;
						Vat_Pham_Luc_Cong_KichNew += num3;
						Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += num4;
						Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += num4;
					}
					break;
				}
				}
				break;
			case 9:
				VatPham_ThuocTinh_Giai_Doan_Loai_Hinh = 0;
				VatPham_ThuocTinh_So_Giai_Doan = 0;
				VatPham_ThuocTinh_Manh_Loai_Hinh = int.Parse(ysqh.Substring(ysqh.Length - 9, 1));
				VatPham_ThuocTinh_Manh = int.Parse(ysqh.Substring(ysqh.Length - 3, 3));
				if (VatPham_ThuocTinh_Manh_Loai_Hinh != 4)
				{
					break;
				}
				if (VatPham_ThuocTinh_Manh >= 100)
				{
					VatPham_ThuocTinh_Manh = 100;
				}
				if (VatPham_ThuocTinh_Manh > 1)
				{
					if ((VatPham_ThuocTinh_Manh & 1) == 1)
					{
						Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
						Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
						Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
						Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
						Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
						Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
						VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
						VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh - 1;
					}
					else
					{
						Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh / 2 + 5;
						Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh / 2 + 5;
						Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh / 2 + 5;
						Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh / 2 + 5;
						Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh / 2 + 5;
						Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh / 2 + 5;
						VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh / 2 * 10;
						VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh - 1;
					}
				}
				else
				{
					Vat_Pham_Luc_Phong_Ngu += 5;
					Vat_Pham_Luc_Phong_NguNew += 5;
				}
				break;
			case 10:
				VatPham_ThuocTinh_Giai_Doan_Loai_Hinh = int.Parse(ysqh.Substring(ysqh.Length - 4, 1));
				VatPham_ThuocTinh_So_Giai_Doan = int.Parse(ysqh.Substring(ysqh.Length - 3, 1)) + 1;
				VatPham_ThuocTinh_Manh_Loai_Hinh = int.Parse(ysqh.Substring(ysqh.Length - 8, 1));
				VatPham_ThuocTinh_Manh = int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
				if (VatPham_ThuocTinh_Manh_Loai_Hinh == 1)
				{
					if (value.FLD_RESIDE2 != 4)
					{
						break;
					}
					TinhToanVuKhiThuocTinh(value);
					if (VatPham_ThuocTinh_Manh > 5 && VatPham_ThuocTinh_Giai_Doan_Loai_Hinh != 0)
					{
						VatPham_ThuocTinh_So_Giai_Doan += VatPham_ThuocTinh_Manh - 5;
					}
					var num = 1;
					if (TriggerAttributePromotion == 2)
					{
						num = 2;
					}
					if (VatPham_ThuocTinh_Giai_Doan_Loai_Hinh != 0 && VatPham_ThuocTinh_So_Giai_Doan > 0)
					{
						switch (VatPham_ThuocTinh_Giai_Doan_Loai_Hinh)
						{
						case 1:
							VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu = VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num;
							break;
						case 2:
							VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram = VatPham_ThuocTinh_So_Giai_Doan * num;
							break;
						case 3:
							VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich += VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num;
							break;
						case 4:
							VatPham_ThuocTinh_VoCong_LucCongKich += (int)(VatPham_ThuocTinh_So_Giai_Doan * 0.5) * num;
							VatPham_ThuocTinh_VoCong_LucCongKichNew += (int)(VatPham_ThuocTinh_So_Giai_Doan * 0.5) * num;
							break;
						case 5:
							VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
							break;
						case 6:
							VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram += VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num;
							Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
							Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
							Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
							Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
							break;
						}
					}
				}
				else
				{
					if (VatPham_ThuocTinh_Manh_Loai_Hinh != 2 || value.FLD_RESIDE2 != 1)
					{
						break;
					}
					计算YPhucThuocTinh(value);
					DatDuocVatPham障力(value, VatPham_ThuocTinh_Manh);
					var num2 = 1;
					if (TriggerAttributePromotion == 1)
					{
						num2 = 2;
					}
					if (VatPham_ThuocTinh_Giai_Doan_Loai_Hinh != 0 && VatPham_ThuocTinh_So_Giai_Doan > 0)
					{
						switch (VatPham_ThuocTinh_Giai_Doan_Loai_Hinh)
						{
						case 1:
							VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich = VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num2;
							break;
						case 2:
							VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang = VatPham_ThuocTinh_So_Giai_Doan * num2;
							break;
						case 3:
							VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh += VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num2;
							break;
						case 4:
							VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang += VatPham_ThuocTinh_So_Giai_Doan * 8 * num2;
							VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew += VatPham_ThuocTinh_So_Giai_Doan * 4 * num2;
							break;
						case 5:
							Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_So_Giai_Doan * 3 * num2;
							Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_So_Giai_Doan * 3 * num2;
							break;
						case 6:
							VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram += VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num2;
							break;
						}
					}
				}
				break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Dat Duoc Cuong Hoa error：" + ex);
		}
	}

	public void FLD_Do_Ben_Vo_Huan(int int_84)
	{
		System.Buffer.BlockCopy(BitConverter.GetBytes(int_84), 0, VatPham_byte, 60, 2);
	}
}
