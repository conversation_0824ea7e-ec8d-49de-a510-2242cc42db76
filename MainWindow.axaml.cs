using Avalonia.Controls;
using Avalonia.Interactivity;
using HeroYulgang.Core;
using HeroYulgang.Services;
using RxjhServer;
using System.Threading.Tasks;

namespace HeroYulgang;

public partial class MainWindow : Window
{
    private Button? _startServerButton;
    private Button? _stopServerButton;
    private Button? _restartServerButton;
    private readonly World _world;
    

    public MainWindow()
    {
        InitializeComponent();

        // Get references to buttons
        _startServerButton = this.FindControl<Button>("StartServerButton");
        _stopServerButton = this.FindControl<Button>("StopServerButton");
        _restartServerButton = this.FindControl<Button>("RestartServerButton");

        // Attach event handlers
        if (_startServerButton != null)
            _startServerButton.Click += StartServerButton_Click;

        if (_stopServerButton != null)
            _stopServerButton.Click += StopServerButton_Click;

        if (_restartServerButton != null)
            _restartServerButton.Click += RestartServerButton_Click;

        // Khởi tạo World
        _world = World.Instance;

        // Log application start
        Logger.Instance.Info("Ứng dụng đã khởi động");
        Logger.Instance.Debug("Chế độ Debug đã được bật");

        // Tải cấu hình
        var config = ConfigManager.Instance;
        Logger.Instance.Info($"Đã tải cấu hình máy chủ: {config.ServerSettings.ServerName}");
        Logger.Instance.Debug($"Cổng máy chủ: {config.ServerSettings.GameServerPort}");
        Logger.Instance.Debug($"Số lượng người chơi tối đa: {config.ServerSettings.MaximumOnline}");
        // Khoi tao Database manager
        DatabaseManager.Instance.Initialize();


        // Cập nhật trạng thái ban đầu
        UpdateButtonStates();
    }

    private async void StartServerButton_Click(object? sender, RoutedEventArgs e)
    {
        // Vô hiệu hóa các nút trong khi khởi động
        SetButtonsEnabled(false);

        Logger.Instance.Info("Đang khởi động máy chủ...");

        bool success = await _world.StartAsync();

        if (success)
        {
            Logger.Instance.Info("Máy chủ đã khởi động thành công");
            Logger.Instance.Debug("Đang lắng nghe kết nối từ người chơi");
            Logger.Instance.Info("Máy chủ đã sẵn sàng phục vụ người chơi");
        }
        else
        {
            Logger.Instance.Error("Không thể khởi động máy chủ");
        }

        // Cập nhật trạng thái nút
        UpdateButtonStates();
    }

    private async void StopServerButton_Click(object? sender, RoutedEventArgs e)
    {
        // Vô hiệu hóa các nút trong khi dừng
        SetButtonsEnabled(false);

        Logger.Instance.Info("Đang dừng máy chủ...");

        bool success = await _world.StopAsync();

        if (success)
        {
            Logger.Instance.Info("Máy chủ đã dừng thành công");
            Logger.Instance.Debug("Đã đóng tất cả kết nối");
        }
        else
        {
            Logger.Instance.Error("Không thể dừng máy chủ");
        }

        // Cập nhật trạng thái nút
        UpdateButtonStates();
    }

    private async void RestartServerButton_Click(object? sender, RoutedEventArgs e)
    {
        // Vô hiệu hóa các nút trong khi khởi động lại
        SetButtonsEnabled(false);

        Logger.Instance.Info("Đang khởi động lại máy chủ...");

        await _world.RestartAsync();

        Logger.Instance.Info("Máy chủ đã khởi động lại thành công");
        Logger.Instance.Info("Máy chủ đã sẵn sàng phục vụ người chơi");

        // Cập nhật trạng thái nút
        UpdateButtonStates();
    }

    private void UpdateButtonStates()
    {
        if (_startServerButton == null || _stopServerButton == null || _restartServerButton == null)
            return;

        switch (_world.State)
        {
            case WorldState.Stopped:
                _startServerButton.IsEnabled = true;
                _stopServerButton.IsEnabled = false;
                _restartServerButton.IsEnabled = false;
                break;

            case WorldState.Running:
                _startServerButton.IsEnabled = false;
                _stopServerButton.IsEnabled = true;
                _restartServerButton.IsEnabled = true;
                break;

            case WorldState.Starting:
            case WorldState.Stopping:
                _startServerButton.IsEnabled = false;
                _stopServerButton.IsEnabled = false;
                _restartServerButton.IsEnabled = false;
                break;
        }
    }

    private void SetButtonsEnabled(bool enabled)
    {
        if (_startServerButton != null)
            _startServerButton.IsEnabled = enabled;

        if (_stopServerButton != null)
            _stopServerButton.IsEnabled = enabled;

        if (_restartServerButton != null)
            _restartServerButton.IsEnabled = enabled;
    }
}
